import React, {useEffect} from 'react';
import AppRouter from './src/router/AppRouter';
import {ToastProvider} from './src/components/Toast/Toast';
import {UserLocationProvider} from './src/hooks/userLocationContext';
import {LocationProvider} from './src/hooks/useLocationContext';
import {
  StatusBar,
  AppState,
  AppStateStatus,
  Text,
  Button,
  View,
} from 'react-native';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {I18nextProvider} from 'react-i18next';
import i18n from './src/i18n/i18n';
import {RideDetailsProvider} from './src/hooks/useRideDetailsContext';
import {DriverProvider} from './src/hooks/useDriver';
import {LoaderProvider} from './src/hooks/useLoader';
import {GeofenceProvider} from './src/hooks/useGeofence';
import {AuthProvider} from './src/hooks/useAuth';
import {NavigationContainer} from '@react-navigation/native';
import {navigationRef} from './src/router/navigationService';
import KeepAwake from './src/utils/KeepAwake';
import * as Sentry from '@sentry/react-native';

Sentry.init({
  dsn: 'https://<EMAIL>/4509356509757440',

  // Adds more context data to events (IP address, cookies, user, etc.)
  // For more information, visit: https://docs.sentry.io/platforms/react-native/data-management/data-collected/
  sendDefaultPii: true,

  // Configure Session Replay
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1,
  integrations: [
    Sentry.mobileReplayIntegration(),
    Sentry.feedbackIntegration(),
  ],

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // spotlight: __DEV__,
});

function App(): JSX.Element {
  const TextWithDefaultProps = Text as any;
  if (TextWithDefaultProps.defaultProps == null) {
    TextWithDefaultProps.defaultProps = {};
  }
  TextWithDefaultProps.defaultProps.allowFontScaling = false;

  useEffect(() => {
    try {
      KeepAwake.activate();
    } catch (error) {
      console.error('Error activating keep awake:', error);
    }

    // Handle app state changes
    const subscription = AppState.addEventListener(
      'change',
      (nextAppState: AppStateStatus) => {
        try {
          if (nextAppState === 'active') {
            KeepAwake.activate();
          } else if (
            nextAppState === 'background' ||
            nextAppState === 'inactive'
          ) {
          
            KeepAwake.deactivate();
          }
        } catch (error) {
          console.error('Error handling app state change:', error);
        }
      },
    );

    return () => {
      try {
        KeepAwake.deactivate();
        subscription.remove();
      } catch (error) {
        console.error('Error in cleanup:', error);
      }
    };
  }, []);

  return (
    <I18nextProvider i18n={i18n}>
      <GestureHandlerRootView style={{flex: 1}}>
        <StatusBar translucent={true} backgroundColor="transparent" />
        <LoaderProvider>
          <ToastProvider>
            <AuthProvider>
              <DriverProvider>
                <UserLocationProvider>
                  <LocationProvider>
                    <NavigationContainer
                      theme={{
                        dark: true,
                        colors: {
                          primary: '#007AFF',
                          background: '#000',
                          card: '#121212',
                          text: '#FFFFFF',
                          border: '#323232',
                          notification: '#FF3B30',
                        },
                      }}
                      ref={navigationRef}>
                      <RideDetailsProvider>
                        <GeofenceProvider>
                          <AppRouter />
                        </GeofenceProvider>
                      </RideDetailsProvider>
                    </NavigationContainer>
                  </LocationProvider>
                </UserLocationProvider>
              </DriverProvider>
            </AuthProvider>
          </ToastProvider>
        </LoaderProvider>
      </GestureHandlerRootView>
    </I18nextProvider>
  );
}

export default Sentry.wrap(App);
