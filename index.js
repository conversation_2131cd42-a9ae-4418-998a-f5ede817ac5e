import {AppRegistry, AppState, NativeModules, Platform} from 'react-native';
import App from './App';
import {name as appName} from './app.json';
import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import notifee, {AndroidImportance, EventType} from '@notifee/react-native';
import {rideDetails} from './src/hooks/useRideDetailsContext';
import {
  playRideSound,
  stopRideSound,
  playCancelSound,
  playAbortedSound,
  playRideStatusSound,
  playCompletedSound,
  stopAllSounds,
} from './src/utils/Sound';

const {FloatingWindow} = NativeModules;

messaging().setAutoInitEnabled(false);

// Hide floating window when app starts from terminated state
if (Platform.OS === 'android') {
  FloatingWindow.hide().catch(err => {
    console.error('Failed to hide floating window on app start:', err);
  });
}

messaging().onNotificationOpenedApp(() => Promise.resolve());
messaging()
  .getInitialNotification()
  .then(() => Promise.resolve());

// Create notification channels
async function createNotificationChannels() {
  // For driver notifications with default sound
  await notifee.createChannel({
    id: 'mapto-driver-channel',
    name: 'Driver Notifications',
    description: 'Notifications for drivers',
    lights: false,
    vibration: true,
    sound: 'default',
    importance: AndroidImportance.HIGH,
  });

  // For ride notifications without sound (we'll play sound manually)
  await notifee.createChannel({
    id: 'mapto-ride-channel',
    name: 'Driver Notifications',
    description: 'Notifications for Driver',
    lights: false,
    vibration: true,
    importance: AndroidImportance.HIGH,
  });

  await notifee.createChannel({
    id: 'mapto-chat-channel',
    name: 'User Chat Messages',
    description: 'Chat messages from users',
    lights: true,
    vibration: true,
    importance: AndroidImportance.HIGH,
  });

  await notifee.createChannel({
    id: 'mapto-ride-canceled-channel',
    name: 'User Chat Messages',
    description: 'Chat messages from users',
    lights: true,
    vibration: true,
    importance: AndroidImportance.HIGH,
  });

  await notifee.createChannel({
    id: 'mapto-ride-accept-channel',
    name: 'User Chat Messages',
    description: 'Chat messages from users',
    lights: true,
    vibration: true,
    importance: AndroidImportance.HIGH,
  });

  await notifee.createChannel({
    id: 'mapto-ride-status-check-channel',
    name: 'Driver ride status check  Notifications',
    description: 'Notifications for driver',
    lights: true,
    vibration: true,
    importance: AndroidImportance.HIGH,
  });

  await notifee.createChannel({
    id: 'mapto-ride-completed-channel',
    name: 'Driver ride aborted  Notifications',
    description: 'Notifications for driver',
    lights: true,
    vibration: true,
    importance: AndroidImportance.HIGH,
  });

  await notifee.createChannel({
    id: 'mapto-ride-aborted-channel',
    name: ' Driver ride completed  Notifications',
    description: 'Notifications for driver',
    lights: true,
    vibration: true,
    importance: AndroidImportance.HIGH,
  });
}

notifee.cancelAllNotifications().catch(err => {
  console.error('Failed to cancel notifications:', err);
});

createNotificationChannels().catch(err => {
  console.error('Error creating notification channels:', err);
});

notifee.onBackgroundEvent(async ({type, detail}) => {
  if (type === EventType.PRESS) {
    stopAllSounds();
  }
});

messaging().setBackgroundMessageHandler(async remoteMessage => {
  console.log('Received background message:', remoteMessage);

  try {
    await notifee.cancelAllNotifications();
    const event = remoteMessage?.data?.event;
    const isDriverActive = await AsyncStorage.getItem('active');

    if (!isDriverActive) return;

    // Handle reward notifications
    if (
      event === 'ReferralRewardProcessed' ||
      event === 'CommissionProcessed' ||
      event === 'TripleTreatRewardProcessed' ||
      event === 'WelcomeRewardProcessed'
    ) {
      let title;
      let body;

      // Customize notification based on event type
      switch (event) {
        case 'ReferralRewardProcessed':
          title = 'Referral Bonus Unlocked!';
          body =
            'You have just earned a reward for referring a friend — enjoy the perks!';
          break;
        case 'CommissionProcessed':
          title = 'Payout Completed'; // Fix: changed colon to equals sign
          body = 'Your commission for the latest trip has been processed.'; // Fix: changed colon to equals sign
          break;
        case 'WelcomeRewardProcessed':
          title = 'Welcome Bonus!';
          body = 'Your Welcome Bonus is here — thanks for riding with us!';
          break;
      }

      // Store event for toast display when app is opened
      await AsyncStorage.setItem('rewardEvent', event);

      // Display notification
      await notifee.displayNotification({
        id: remoteMessage?.messageId || `${Date.now()}`,
        title: title,
        body: body,
        android: {
          channelId: 'mapto-driver-channel',
          importance: AndroidImportance.HIGH,
          smallIcon: 'ic_driver',
          pressAction: {
            id: 'default',
            launchActivity: 'default',
          },
          tag: `reward_${event}_${Date.now()}`,
        },
      });
    }

    const tripIdFromMessage = remoteMessage?.data?.tripId;
    const notificationId = remoteMessage?.messageId || `${Date.now()}`;
    const sentTime = remoteMessage?.data?.sentTime;

    // if (remoteMessage.data && remoteMessage.data.content) {
    //   const messageContent =
    //     typeof remoteMessage.data.content === 'string'
    //       ? remoteMessage.data.content
    //       : JSON.stringify(remoteMessage.data.content);

    //   const newMessage = {
    //     id: Number(new Date()),
    //     text: messageContent,
    //     sender: false,
    //     time: new Date().toLocaleTimeString([], {
    //       hour: '2-digit',
    //       minute: '2-digit',
    //     }),
    //   };

    //   playChatSound();

    //   await notifee.displayNotification({
    //     id: notificationId || `${Date.now()}`,
    //     title: 'User',
    //     body: messageContent,
    //     android: {
    //       channelId: 'mapto-chat-channel',
    //       importance: AndroidImportance.HIGH,
    //       smallIcon: 'ic_driver',
    //       sound: 'chat',
    //       pressAction: {
    //         id: 'default',
    //         launchActivity: 'default',
    //       },
    //       tag: `message_${Date.now()}`,
    //       showTimestamp: true,
    //     },
    //   });

    //   await AsyncStorage.setItem('newMessage', JSON.stringify(newMessage));
    // }
    if (event === 'RideCanceledDriver') {
      rideDetails.trip_id = null;
      await AsyncStorage.multiRemove([
        'tripId',
        'showRideModal',
        'notificationId',
        'newMessage',
      ]);
    } else if (event === 'RideCanceled') {
      stopRideSound();
      rideDetails.trip_id = null;
      try {
        await FloatingWindow.openAppFromBackground();
      } catch (error) {
        console.log('FloatingWindow error:', error);
      }

      await notifee.displayNotification({
        id: notificationId || `${Date.now()}`,
        title: 'Ride Canceled',
        body: 'User canceled the ride request',
        android: {
          channelId: 'mapto-ride-canceled-channel',
          importance: AndroidImportance.HIGH,
          smallIcon: 'ic_driver',
          pressAction: {
            id: 'default',
            launchActivity: 'default',
          },
          tag: `ride_canceled_${Date.now()}`,
        },
      });

      await AsyncStorage.setItem('userCanceled', JSON.stringify(true));
      await AsyncStorage.multiRemove([
        'tripId',
        'showRideModal',
        'notificationId',
        'newMessage',
      ]);
      playCancelSound();
    } else if (event === 'RideTimeOut') {
      stopAllSounds();
      rideDetails.trip_id = null;
      await notifee.cancelAllNotifications();
      await AsyncStorage.setItem('timeout', tripIdFromMessage);
      await AsyncStorage.multiRemove([
        'tripId',
        'sentTime',
        'showRideModal',
        'notificationId',
      ]);
    } else if (event === 'RideRequest') {
      await AsyncStorage.multiRemove([
        'userCanceled',
        'timeout',
        'showRideStatusCheck',
      ]);
      await AsyncStorage.multiSet([
        ['sentTime', sentTime],
        ['tripId', tripIdFromMessage],
        ['showRideModal', JSON.stringify(true)],
        ['notificationId', notificationId],
      ]);

      rideDetails.trip_id = tripIdFromMessage;

      try {
        await FloatingWindow.openAppFromBackground();
      } catch (error) {
        console.log('FloatingWindow error:', error);
      }

      const notificationSettings = await notifee.getNotificationSettings();
      const isAppInForeground = notificationSettings.foreground;

      if (!isAppInForeground) {
        playRideSound();
      }

      await notifee.displayNotification({
        id: notificationId || `${Date.now()}`,
        title: 'Ride Request',
        body: 'You have a new ride request',
        android: {
          channelId: 'mapto-ride-channel',
          importance: AndroidImportance.HIGH,
          smallIcon: 'ic_driver',
          pressAction: {
            id: 'default',
            launchActivity: 'default',
          },
          tag: `ride_request_${tripIdFromMessage}_${Date.now()}`,
        },
      });
      notifee.onForegroundEvent(async ({type, detail}) => {
        if (type === EventType.PRESS) {
          try {
            await FloatingWindow.openAppFromBackground();
          } catch (error) {
            console.log('FloatingWindow error:', error);
          }
        }
      });

      notifee.onBackgroundEvent(async ({type, detail}) => {
        if (type === EventType.PRESS) {
          try {
            await FloatingWindow.openAppFromBackground();
          } catch (error) {
            console.log('FloatingWindow error:', error);
          }
        }
      });
    } else if (event === 'RideAborted') {
      stopAllSounds();
      playAbortedSound();
      rideDetails.trip_id = null;
      await AsyncStorage.multiSet([['showRideAborted', JSON.stringify(true)]]);
      await AsyncStorage.multiRemove([
        'tripId',
        'showRideModal',
        'notificationId',
        'newMessage',
      ]);
      await notifee.displayNotification({
        id: notificationId || `${Date.now()}`,
        title: 'Ride Aborted',
        body: 'The ride has been aborted',
        android: {
          channelId: 'mapto-driver-channel',
          importance: AndroidImportance.HIGH,
          smallIcon: 'ic_driver',
          pressAction: {
            id: 'default',
            launchActivity: 'default',
          },
          tag: `ride_aborted_${Date.now()}`,
        },
      });
      try {
        await FloatingWindow.openAppFromBackground();
      } catch (error) {
        console.log('FloatingWindow error:', error);
      }
    } else if (event === 'RideCompleted') {
      playCompletedSound();
      await AsyncStorage.multiSet([['rideStatus', 'COMPLETED']]);
      await AsyncStorage.multiRemove([
        'showRideModal',
        'notificationId',
        'newMessage',
      ]);
      await notifee.displayNotification({
        id: notificationId || `${Date.now()}`,
        title: 'Ride Completed',
        body: 'The ride has been completed successfully',
        android: {
          channelId: 'mapto-ride-completed-channel',
          importance: AndroidImportance.HIGH,
          smallIcon: 'ic_driver',
          pressAction: {
            id: 'default',
            launchActivity: 'default',
          },
          tag: `ride_completed_${Date.now()}`,
        },
      });
      try {
        await FloatingWindow.openAppFromBackground();
      } catch (error) {
        console.log('FloatingWindow error:', error);
      }
    } else if (event === 'RideStatusCheck') {
      rideDetails.trip_id = null;

      playRideStatusSound();

      await AsyncStorage.multiSet([
        ['sentTime', sentTime],
        ['tripId', tripIdFromMessage],
        ['attempt', remoteMessage?.data?.attempt],
        ['showRideStatusCheck', JSON.stringify(true)],
      ]);
      await notifee.displayNotification({
        id: notificationId || `${Date.now()}`,
        title: 'Ride Status Check',
        body: 'Please confirm your ride status',
        android: {
          channelId: 'mapto-ride-status-check-channel',
          importance: AndroidImportance.HIGH,
          smallIcon: 'ic_driver',
          pressAction: {
            id: 'default',
            launchActivity: 'default',
          },
          tag: `ride_status_check_${tripIdFromMessage}_${Date.now()}`,
        },
      });
      try {
        await FloatingWindow.openAppFromBackground();
      } catch (error) {
        console.log('FloatingWindow error:', error);
      }
    }
  } catch (error) {
    console.error('Error in headless task:', error);
  }

  return Promise.resolve();
});

// notifee.onForegroundEvent(async ({type, detail}) => {
//   if (type === EventType.FOREGROUND_NOTIFICATION_RECEIVED) {
//     console.log('Foreground notification received:', detail);

//     if (detail.notification?.android?.tag?.startsWith('ride_aborted_')) {
//       playAbortedSound();
//     } else if (
//       detail.notification?.android?.tag?.startsWith('ride_completed_')
//     ) {
//       playCompletedSound();
//     }
//   } else if (type === EventType.PRESS) {
//     console.log('Notification pressed:', detail);

//     if (detail.notification?.android?.tag?.startsWith('message_')) {
//     }

//     await FloatingWindow.openAppFromBackground();
//   }
// });

const showFloatingWindowTask = async () => {
  console.log('Headless JS: Attempting to show floating window');
  try {
    if (FloatingWindow?.show) {
      await FloatingWindow.show(); // Call the 'show' method
      console.log('Headless JS: FloatingWindow.show() called');
    } else {
      console.error(
        'Headless JS: FloatingWindow module or show method not available',
      );
    }
  } catch (error) {
    console.error('Headless JS: Error calling FloatingWindow.show()', error);
  }
};

AppRegistry.registerHeadlessTask(
  'ShowFloatingWindowTaskName',
  () => showFloatingWindowTask,
);

AppRegistry.registerComponent(appName, () => App);
