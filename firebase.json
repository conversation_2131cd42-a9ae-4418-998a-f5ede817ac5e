{"messaging_android_notification_channel_id": "mapto-driver-channel", "messaging_android_notification_channels": [{"id": "mapto-driver-channel", "name": "Driver Notifications", "description": "Notifications for drivers", "importance": "high", "sound": "default"}, {"id": "mapto-ride-channel", "name": "Customer Notifications", "description": "Notifications for customers", "importance": "high", "sound": "ride"}, {"id": "mapto-ride-accept-channel", "name": "Customer Notifications", "description": "Notifications for customers", "importance": "high", "sound": "accept"}, {"id": "mapto-chat-channel", "name": "User Chat Messages", "description": "chat messages from users", "importance": "high", "sound": "chat"}, {"id": "mapto-ride-canceled-channel", "name": "User canceled Notifications", "description": "Notifications for driver", "importance": "high", "sound": "usercanceled"}, {"id": "mapto-ride-status-check-channel", "name": "Driver ride status check  Notifications", "description": "Notifications for driver", "importance": "high", "sound": "ridestatus"}, {"id": "mapto-ride-aborted-channel", "name": "Driver ride aborted  Notifications", "description": "Notifications for driver", "importance": "high", "sound": "rideaborted"}, {"id": "mapto-ride-completed-channel", "name": "Driver ride completed  Notifications", "description": "Notifications for driver", "importance": "high", "sound": "ridecompleted"}]}