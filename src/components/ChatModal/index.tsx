import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  Animated,
  FlatList,
  StyleSheet,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  TextInput,
} from 'react-native';
import IconSvgView from '../IconSvgView/IconSvgView';
import close from '../../icons/close.svg';
import {colors, EBGaramondFont, GeistFont, sizes} from '../../constants';
import {spacing} from '../../constants/theme';
import FadingHorizontalLine from '../FadingLine/FadingHorizontalLine';
import {Message} from '../../types';
import {t} from 'i18next';

type ChatModalProps = {
  visible: boolean;
  onClose: () => void;
  driverId: string;
  messages: Message[];
  tripId: string | undefined;
  sendMessage: (message: string) => void;
};

const {height: screenHeight} = Dimensions.get('window');

const ChatModal: React.FC<ChatModalProps> = ({
  visible,
  onClose,
  messages,
  sendMessage,
}) => {
  const slideAnim = useRef(new Animated.Value(0)).current;
  const flatListRef = useRef<FlatList<Message>>(null);
  const [message, setMessage] = useState('');
  const [sending, setSending] = useState(false);

  // Handles the modal slide-in animation
  const slideInModal = () => {
    Animated.timing(slideAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  // Handles the modal slide-out animation
  const slideOutModal = () => {
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      onClose();
    });
  };

  useEffect(() => {
    if (visible) {
      slideInModal();
    }
  }, [visible]);

  useEffect(() => {
    if (messages.length > 0) {
      flatListRef.current?.scrollToEnd({animated: true});
    }
  }, [messages]);

  const handleSendMessage = async () => {
    const trimmedMessage = message.trim();
    if (!trimmedMessage || sending) {
      return;
    }

    setSending(true);
    setMessage('');

    try {
      await sendMessage(trimmedMessage);
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSending(false);
    }
  };

  return (
    <Modal transparent visible={visible} animationType="none">
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={{flex: 1}}>
        <Animated.View
          style={[
            styles.modalContainer,
            {
              transform: [
                {
                  translateY: slideAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [screenHeight, 0],
                  }),
                },
              ],
            },
          ]}>
          <View style={styles.chatContainer}>
            <View style={styles.titleContainer}>
              <Text style={styles.title}>{t('chat')}</Text>
              <TouchableOpacity
                style={{padding: spacing.md}}
                onPress={slideOutModal}
                accessible
                accessibilityLabel="Close chat modal">
                <IconSvgView source={close} size={15} />
              </TouchableOpacity>
            </View>
            <View style={{marginBottom: spacing.md}}>
              <FadingHorizontalLine />
            </View>
            <FlatList
              ref={flatListRef}
              data={messages}
              keyExtractor={(item, index) => `${index}`}
              renderItem={({item}) => (
                <>
                  <View
                    style={[
                      styles.messageBubble,
                      item.sender ? styles.sender : styles.receiver,
                    ]}>
                    <Text style={styles.messageText}>
                      {item.text ?? item?.content}
                    </Text>
                  </View>
                  <Text
                    style={[
                      styles.timeText,
                      item.sender ? styles.senderTime : styles.receiverTime,
                    ]}>
                    {item.time}
                  </Text>
                </>
              )}
              ListEmptyComponent={
                <Text style={styles.emptyText}>{t('no_messages')}</Text>
              }
              contentContainerStyle={styles.flatListContainer}
            />
            <View style={styles.inputContainer}>
              <View style={styles.inputCard}>
                <TextInput
                  style={styles.input}
                  value={message}
                  onChangeText={setMessage}
                  placeholder={t('type_your_message')}
                  placeholderTextColor={colors.lightGrey}
                  multiline={true}
                />
                <TouchableOpacity
                  style={styles.sendButton}
                  onPress={handleSendMessage}
                  disabled={sending}
                  accessible
                  accessibilityLabel={t('send_message')}>
                  <Text style={styles.sendButtonText}>{t('send')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Animated.View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    height: '75%',
  },
  titleContainer: {
    flexDirection: 'row',
    padding: spacing.md,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.h3,
    color: colors.white,
  },
  chatContainer: {
    flex: 1,
    backgroundColor: colors.black,
    paddingTop: spacing.md,
    borderTopLeftRadius: spacing.md,
    borderTopRightRadius: spacing.md,
    justifyContent: 'space-between',
  },
  flatListContainer: {
    flexGrow: 1,
    padding: spacing.md,
  },
  emptyText: {
    color: colors.lightGrey,
    textAlign: 'center',
    marginVertical: spacing.xxl,
    fontFamily: GeistFont.regular,
  },
  inputContainer: {
    padding: spacing.md,
    backgroundColor: colors.darkGrey,
  },
  inputCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: spacing.xs,
    paddingLeft: spacing.sm,
    paddingRight: spacing.xs,
    paddingVertical: spacing.xs,
  },
  input: {
    flex: 1,
    minHeight: 40,
    maxHeight: 100,
    color: colors.lightGrey,
    fontFamily: GeistFont.regular,
    paddingVertical: spacing.xs,
  },
  sendButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    alignSelf: 'flex-end',
  },
  sendButtonText: {
    color: colors.white,
    fontWeight: '700',
    fontFamily: GeistFont.regular,
  },
  messageBubble: {
    marginVertical: spacing.sm,
    padding: spacing.md,
    borderRadius: spacing.sm,
    maxWidth: '70%',
  },
  sender: {
    alignSelf: 'flex-end',
    backgroundColor: '#013A63',
  },
  receiver: {
    alignSelf: 'flex-start',
    backgroundColor: colors.darkCharcoal,
  },
  messageText: {
    color: 'white',
    fontFamily: GeistFont.regular,
  },
  timeText: {
    fontSize: sizes.h3 / 2,
    color: colors.white,
    marginBottom: spacing.sm,
    alignSelf: 'flex-end',
    fontFamily: GeistFont.regular,
  },
  senderTime: {
    alignSelf: 'flex-end',
    fontFamily: GeistFont.regular,
  },
  receiverTime: {
    alignSelf: 'flex-start',
    fontFamily: GeistFont.regular,
  },
});

export default ChatModal;
