import React, {useState, useEffect, createContext, useContext} from 'react';
import styled from 'styled-components/native';
import {Text, Dimensions, TouchableOpacity} from 'react-native';
import {spacing} from '../../constants/theme';
import {colors, GeistFont} from '../../constants';

const deviceWidth = Dimensions.get('window').width;

interface ToastProps {
  title?: string;
  message: string;
  type?: 'success' | 'failure' | 'default';
  duration?: number;
  showCloseButton?: boolean;
  onClose?: () => void;
}

const ToastContainer = styled.View<{type: 'success' | 'failure' | 'default'}>`
  background-color: ${({type}) =>
    type === 'success'
      ? colors.green
      : type === 'failure'
      ? colors.darkRed
      : colors.grey};
  padding: ${spacing.md}px;
  border-radius: ${spacing.xs}px;
  position: absolute;
  top: ${spacing.xxl * 2}px;
  right: ${spacing.xxl}px;
  max-width: ${deviceWidth * 0.8}px;
  min-height: ${spacing.xl * 2}px;
  width: auto;
`;

const ToastHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${spacing.xs}px;
`;

const ToastTitle = styled(Text)`
  color: #fff;
  font-size: 16px;
  font-weight: ${GeistFont.bold};
  width: 80%;
`;

const ToastMessage = styled(Text)`
  color: #fff;
  font-size: 14px;
  font-family: ${GeistFont.regular};
`;

const CloseButton = styled(TouchableOpacity)`
  paddinghorizontal: ${spacing.xxl}px;
`;

const CloseButtonText = styled(Text)`
  color: #fff;
  font-size: 16px;
`;

const Toast: React.FC<ToastProps> = ({
  title,
  message,
  type = 'default',
  duration = 5000,
  showCloseButton = false,
  onClose,
}) => {
  const [isVisible, setIsVisible] = useState<boolean>(true);

  useEffect(() => {
    setIsVisible(true);

    const timeout = setTimeout(() => {
      setIsVisible(false);
      onClose?.();
    }, duration);

    return () => clearTimeout(timeout);
  }, [message, duration, onClose]);

  const handleClose = () => {
    setIsVisible(false);
    onClose?.();
  };

  if (!isVisible) return null;

  return (
    <ToastContainer type={type}>
      {(title || showCloseButton) && (
        <ToastHeader>
          {title && <ToastTitle>{title}</ToastTitle>}
          {showCloseButton && (
            <CloseButton onPress={handleClose}>
              <CloseButtonText>✕</CloseButtonText>
            </CloseButton>
          )}
        </ToastHeader>
      )}
      <ToastMessage>{message}</ToastMessage>
    </ToastContainer>
  );
};

interface ToastContextProps {
  showToast: {
    (message: string, type?: 'success' | 'failure' | 'default'): void;
    (options: {
      title?: string;
      message: string;
      type?: 'success' | 'failure' | 'default';
      duration?: number;
      showCloseButton?: boolean;
      onClose?: () => void; // ✅ Added onClose
    }): void;
  };
}

interface ToastProviderProps {
  children: React.ReactNode;
}

const ToastContext = createContext<ToastContextProps | undefined>(undefined);

const ToastProvider: React.FC<ToastProviderProps> = ({children}) => {
  const [toastConfig, setToastConfig] = useState<ToastProps | null>(null);
  const [toastKey, setToastKey] = useState<number>(0);

  const showToast: ToastContextProps['showToast'] = (
    messageOrOptions:
      | string
      | {
          title?: string;
          message: string;
          type?: 'success' | 'failure' | 'default';
          duration?: number;
          showCloseButton?: boolean;
          onClose?: () => void; // ✅ Added onClose
        },
    type?: 'success' | 'failure' | 'default',
  ) => {
    if (typeof messageOrOptions === 'string') {
      setToastConfig({
        message: messageOrOptions,
        type: type || 'default',
        duration: 5000,
        showCloseButton: false,
      });
    } else {
      setToastConfig({
        title: messageOrOptions.title,
        message: messageOrOptions.message,
        type: messageOrOptions.type || 'default',
        duration: messageOrOptions.duration || 5000,
        showCloseButton: messageOrOptions.showCloseButton || false,
        onClose: messageOrOptions.onClose,
      });
    }
    setToastKey(prevKey => prevKey + 1);
  };

  const handleClose = () => {
    setToastConfig(null);
  };

  return (
    <ToastContext.Provider value={{showToast}}>
      {children}
      {toastConfig && (
        <Toast key={toastKey} {...toastConfig} onClose={handleClose} />
      )}
    </ToastContext.Provider>
  );
};

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

export {ToastProvider};
