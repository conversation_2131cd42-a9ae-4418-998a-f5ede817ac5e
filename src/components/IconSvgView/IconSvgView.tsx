import {ColorValue, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {SvgXml} from 'react-native-svg';

type Props = {
  source: string;
  size?: number;
  color?: string;
  width?: number;
  height?: number;
  svgStyle?: object; 
  stroke?:ColorValue;
  strokeOpacity?:number;
  strokeWidth?:number;
};

const IconSvgView = ({source, size = 20, color, width, height,svgStyle,stroke,strokeOpacity,strokeWidth}: Props) => {
  return (
    <SvgXml
      xml={source}
      height={height || size}
      width={width || size}
      color={color}
      style={svgStyle}
      stroke={stroke}
      strokeOpacity={strokeOpacity}
      strokeWidth={strokeWidth}
    />
  );
};

export default IconSvgView;

const styles = StyleSheet.create({});
