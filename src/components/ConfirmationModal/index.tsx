import React from 'react';
import {Modal, View, Text, TouchableOpacity} from 'react-native';
import styled from 'styled-components/native';
import Button from '../Button/Button';
import HollowButton from '../Button/HollowButton/HollowButton';
import {GeistFont, colors, sizes} from '../../constants';
import {spacing} from '../../constants/theme';
import FadingHorizontalLine from '../FadingLine/FadingHorizontalLine';
import {useTranslation} from 'react-i18next';

interface ConfirmationModalProps {
  title: string;
  visible: boolean;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  title,
  visible,
  message,
  onConfirm,
  onCancel,
}) => {
  const {t} = useTranslation();
  return (
    <Modal transparent={true} visible={visible} animationType="fade">
      <ModalBackground>
        <ModalContainer>
          <Title>{title}</Title>
          <FadingHorizontalLine />
          <Message>{message}</Message>
          <ButtonContainer>
            <HollowButton
              title={t('cancel')}
              style={{width: '45%'}}
              borderColor={colors.red}
              textColor={colors.red}
              onPress={onCancel}
            />
            <Button
              title={t('confirm')}
              style={{width: '45%'}}
              onPress={onConfirm}
            />
          </ButtonContainer>
        </ModalContainer>
      </ModalBackground>
    </Modal>
  );
};

const ModalBackground = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.8);
`;

const ModalContainer = styled.View`
  width: 90%;
  padding: ${spacing.xxl}px;
  background-color: ${colors.darkGrey};
  border-radius: ${spacing.xxs}px;
`;

const Title = styled.Text`
  font-size: ${sizes.h4}px;
  color: ${colors.white};
  margin-bottom: ${spacing.md}px;
  align-items: flex-start;
  justify-content: flex-start;
`;

const Message = styled.Text`
  font-size: ${sizes.h6}px;
  font-style: ${GeistFont.regular};
  margin-vertical: ${spacing.lg}px;
  text-align: center;
  color: ${colors.lightGrey};
`;

const ButtonContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
`;

export default ConfirmationModal;
