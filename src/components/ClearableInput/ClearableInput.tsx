import React, {forwardRef, useState} from 'react';
import {TextInput, View, TouchableOpacity} from 'react-native';
import {colors} from '../../constants';
import IconSvgView from '../IconSvgView/IconSvgView';
import closeCircle from '../../icons/closeCircle.svg';

const ClearableTextInput = forwardRef<TextInput, any>((props, ref) => {
  const {value, onChangeText, onClear, onFocus, onBlur, ...restProps} = props;
  const [isFocused, setIsFocused] = useState(false);

  const handleClear = () => {
    onChangeText('');
    onClear?.();
  };

  const handleOnFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  return (
    <View style={{flexDirection: 'row', alignItems: 'center'}}>
      <TextInput
        value={value}
        onChangeText={onChangeText}
        onFocus={handleOnFocus}
        onBlur={() => setIsFocused(false)}
        style={{flex: 1, color: colors.white}}
        ref={ref}
        cursorColor={colors.grey}
        {...restProps}
      />
      {isFocused && value.length > 0 && (
        <View >
          <TouchableOpacity onPress={handleClear} style={{marginLeft: 5}}>
            <IconSvgView width={18} source={closeCircle}/>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
});

export default ClearableTextInput;
