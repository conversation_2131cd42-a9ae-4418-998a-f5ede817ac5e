import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import Input from './Input';

describe('Input component', () => {
  const onChangeMock = jest.fn();
  const { getByTestId } = render(<Input onChange={onChangeMock} />);
  const inputField = getByTestId('input-field');

  it('renders without crashing', () => {
    render(<Input onChange={onChangeMock} />);
  });

  it('renders input title and input field', () => {
    const { getByText, getByTestId } = render(
      <Input onChange={onChangeMock} inputTitle="Test Input" />,
    );

    expect(getByText('Test Input')).toBeTruthy();
    expect(getByTestId('input-field')).toBeTruthy();
  });

  it('updates the input value on change', () => {
    const { getByTestId } = render(<Input onChange={onChangeMock} />);
    const inputField = getByTestId('input-field');

    fireEvent.changeText(inputField, 'Test Value');

    expect(onChangeMock).toHaveBeenCalledWith('Test Value');
  });

  it('handles input value correctly when the value prop changes', () => {
    const { getByTestId, rerender } = render(<Input onChange={onChangeMock} />);
    const inputField = getByTestId('input-field');

    rerender(<Input onChange={onChangeMock} value="Initial Value" />);
    expect(inputField.props.value).toBe('Initial Value');

    fireEvent.changeText(inputField, 'New Value');
    expect(onChangeMock).toHaveBeenCalledWith('New Value');
  });

});
