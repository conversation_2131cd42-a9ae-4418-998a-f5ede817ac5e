import React, {useState, useEffect} from 'react';
import {
  TextInputProps,
  View,
  TextInput,
  Text,
  TouchableOpacity,
} from 'react-native';
import styled from 'styled-components/native';
import {spacing} from '../../constants/theme';
import {colors, sizes} from '../../constants';
import DatePicker from 'react-native-date-picker';
import {useTranslation} from 'react-i18next';

interface StyledInputProps extends TextInputProps {
  isFocused: boolean;
  isError: boolean;
  disabled?: boolean;
}

const StyledInput = ({
  isFocused,
  isError,
  disabled,
  ...props
}: StyledInputProps) => (
  <TextInput
    style={{
      fontFamily: 'GeistVariableVF',
      height: spacing.xxl * 2,
      borderWidth: 1,
      padding: spacing.sm,
      borderRadius: spacing.xs,
      backgroundColor: disabled
        ? 'rgba(226, 227, 233, 0.4)'
        : isFocused
        ? 'rgba(226, 227, 233, 0.20)'
        : isError
        ? 'rgba(236, 135, 134, 0.10)'
        : 'rgba(226, 227, 233, 0.10)',
      borderColor: disabled
        ? colors.white
        : isFocused
        ? colors.white
        : isError
        ? colors.red
        : colors.lightGrey,
      color: disabled
        ? colors.white
        : isFocused
        ? colors.white
        : colors.lightGrey,
      opacity: disabled ? 0.7 : 1,
    }}
    placeholderTextColor="#838489"
    {...props}
  />
);

const StyledInputLabel = styled.Text`
  font-family: GeistVariableVF;
  font-weight: 400;
  font-size: ${sizes.body}px;
  margin-vertical: ${spacing.md}px;
  color: ${colors.grey};
`;

const StyledErrorMessage = styled.Text`
  font-family: GeistVariableVF;
  font-weight: 400;
  font-size: ${sizes.body / 1.2}px;
  margin-vertical: ${spacing.md}px;
  color: ${colors.red};
`;

interface InputProps extends TextInputProps {
  onChange: (e: any) => void;
  value?: string;
  inputTitle?: string;
  defaultValue?: string;
  error?: string | null;
  isFocused?: boolean;
  message?: string;
  type?: 'text' | 'date';
  editable?: boolean;
  rightButton?: {
    title: string;
    onPress: () => void;
    disabled?: boolean;
  };
}

const Input: React.FC<InputProps> = ({
  onChange,
  value = '',
  inputTitle,
  error,
  isFocused,
  message,
  onBlur,
  onFocus,
  type = 'text',
  editable = true,
  rightButton,
  ...props
}) => {
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [open, setOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const {t} = useTranslation();

  const validateInput = (text: string) => {
    const emojiRegex =
      /[\u{1F300}-\u{1F5FF}\u{1F900}-\u{1F9FF}\u{1F600}-\u{1F64F}\u{1F680}-\u{1F6FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}\u{1F1E6}-\u{1F1FF}]/gu;
    return !emojiRegex.test(text);
  };

  useEffect(() => {
    if (value) {
      const parsedDate = new Date(value);
      if (!isNaN(parsedDate.getTime())) {
        setSelectedDate(parsedDate);
      }
    }
  }, [value]);

  const handleDateChange = (date: Date) => {
    setSelectedDate(date);
    onChange(date.toISOString().split('T')[0]);
    setOpen(false);
  };

  const handleDatePress = () => {
    if (!editable) return;
    setOpen(true);
  };

  return (
    <View>
      {inputTitle && <StyledInputLabel>{inputTitle}</StyledInputLabel>}
      {type === 'date' ? (
        <TouchableOpacity onPress={handleDatePress} disabled={!editable}>
          <StyledInput
            value={selectedDate ? selectedDate.toLocaleDateString('en-GB') : ''}
            placeholder={t('select_date')}
            editable={false}
            isFocused={isInputFocused}
            isError={!!error}
            disabled={!editable}
            {...props}
          />
        </TouchableOpacity>
      ) : (
        <View style={{ position: 'relative' }}>
          <StyledInput
            value={value}
            onChangeText={text => {
              if (validateInput(text)) {
                onChange(text);
              }
            }}
            isFocused={isInputFocused}
            isError={!!error}
            disabled={!editable}
            editable={editable}
            onFocus={e => {
              setIsInputFocused(true);
              onFocus?.(e);
            }}
            onBlur={e => {
              setIsInputFocused(false);
              onBlur?.(e);
            }}
            {...props}
            style={[
              {
                fontFamily: 'GeistVariableVF',
                height: spacing.xxl * 2,
                borderWidth: 1,
                padding: spacing.sm,
                borderRadius: spacing.xs,
                backgroundColor: editable
                  ? isInputFocused
                    ? 'rgba(226, 227, 233, 0.20)'
                    : !!error
                    ? 'rgba(236, 135, 134, 0.10)'
                    : 'rgba(226, 227, 233, 0.10)'
                  : 'rgba(226, 227, 233, 0.4)',
                borderColor: editable
                  ? isInputFocused
                    ? colors.white
                    : !!error
                    ? colors.red
                    : colors.lightGrey
                  : colors.white,
                color: editable
                  ? isInputFocused
                    ? colors.white
                    : colors.lightGrey
                  : colors.white,
                opacity: editable ? 1 : 0.7,
                paddingRight: rightButton ? 80 : spacing.sm,
              },
              props.style,
            ]}
          />
          {rightButton && (
            <TouchableOpacity
              style={{
                position: 'absolute',
                right: 10,
                top: '50%',
                transform: [{ translateY: -15 }],
                backgroundColor: rightButton.disabled ? 'rgba(141, 141, 141, 0.4)' : colors.white,
                paddingHorizontal: 12,
                paddingVertical: 6,
                borderRadius: 4,
              }}
              onPress={rightButton.onPress}
              disabled={rightButton.disabled}>
              <Text
                style={{
                  color: rightButton.disabled ? colors.white : colors.darkGrey,
                  fontFamily: 'GeistVariableVF',
                  fontWeight: '500',
                  fontSize: 12,
                }}>
                {rightButton.title}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
      {message && !error && <StyledInputLabel>{message}</StyledInputLabel>}
      {error && (
        <StyledErrorMessage numberOfLines={2}>{error}</StyledErrorMessage>
      )}
      <DatePicker
        modal
        mode="date"
        open={open}
        date={selectedDate || new Date()}
        onConfirm={handleDateChange}
        onCancel={() => setOpen(false)}
      />
    </View>
  );
};

export default Input;
