import React from 'react';
import {TouchableOpacity, View, Text} from 'react-native';
import styled from 'styled-components/native';
import {colors, GeistFont} from '../../constants';

interface ToggleSwitchProps {
  isOn: boolean;
  handleToggle: () => void;
  activeColor?: string;
}

const ToggleSwitch: React.FC<ToggleSwitchProps> = ({
  isOn, 
  handleToggle, 
  activeColor = colors.white
}) => {
  return (
    <TouchableOpacity
      onPress={handleToggle}
      style={{
        width: 60,
        height: 25,
        borderRadius: 15,
        backgroundColor: isOn ? activeColor : colors.grey,
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
        padding: 2,
      }}>
      <SwitchText isOn={isOn} activeColor={activeColor}>
        {isOn ? 'On' : 'Off'}
      </SwitchText>
      <SwitchThumb isOn={isOn} activeColor={activeColor} />
    </TouchableOpacity>
  );
};

interface SwitchProps {
  isOn: boolean;
  activeColor?: string;
}

const SwitchText = styled(Text)<SwitchProps>`
  color: ${props => (props.isOn ? colors.darkGrey : colors.white)};
  font-style: ${GeistFont.regular};
  font-weight: bold;
  position: absolute;
  left: ${props => (props.isOn ? '10px' : '32px')};
  z-index: 1;
`;

const SwitchThumb = styled(View)<SwitchProps>`
  width: 20px;
  height: 20px;
  border-radius: 13px;
  background-color: ${props => (props.isOn ? colors.white : colors.white)};
  position: absolute;
  left: ${props => (props.isOn ? '38px' : '3px')};
  top: 2.5px;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
`;

export default ToggleSwitch;
