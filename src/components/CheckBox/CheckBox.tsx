import React from 'react';
import { TouchableOpacity, View, Text, StyleSheet } from 'react-native';
import { colors } from '../../constants';
import { spacing } from '../../constants/theme';

interface CheckBoxProps {
  checked: boolean;
  onPress: () => void;
  label?: string;
  disabled?: boolean;
}

const CheckBox: React.FC<CheckBoxProps> = ({
  checked,
  onPress,
  label,
  disabled = false,
}) => {
  return (
    <TouchableOpacity 
      style={styles.container} 
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      <View style={[
        styles.checkbox,
        checked ? styles.checked : styles.unchecked,
        disabled && styles.disabled
      ]}>
        {checked && <Text style={styles.checkmark}>✓</Text>}
      </View>
      {label && <Text style={styles.label}>{label}</Text>}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.sm,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checked: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.white,
  },
  unchecked: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.grey,
  },
  disabled: {
    opacity: 0.5,
  },
  checkmark: {
    color: colors.black,
    fontSize: 14,
    fontWeight: 'bold',
  },
  label: {
    marginLeft: spacing.sm,
    color: colors.white,
    fontFamily: 'GeistVariableVF',
    fontSize: 14,
  }
});

export default CheckBox;
