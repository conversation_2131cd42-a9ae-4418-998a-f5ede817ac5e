import React from 'react';
import { render } from '@testing-library/react-native';
import FadingLine from './FadingLine';

//adding mock
jest.mock('react-native-linear-gradient', () => 'LinearGradient');

describe('FadingLine component', () => {
  test('renders correctly with default props', () => {
    const { getByTestId } = render(<FadingLine startX={0} startY={0} endX={1} endY={1} />);
    const fadingLine = getByTestId('fading-line');

    expect(fadingLine).toBeTruthy();
  });

  test('renders with custom start and end points', () => {
    const { getByTestId } = render(<FadingLine startX={0.2} startY={0.5} endX={0.8} endY={0.5} />);
    const fadingLine = getByTestId('fading-line');

    expect(fadingLine).toBeTruthy();
  });

});
