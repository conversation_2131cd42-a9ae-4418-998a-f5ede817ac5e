import React from 'react';
import { View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

const FadingLine = ({
  startX,
  startY,
  endX,
  endY,
  width,
  height
}: {
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  width?: any,
  height?: any,
}) => {
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }} testID="fading-line">
      <LinearGradient
        start={{ x: startX, y: startY }}
        end={{ x: endX, y: endY }}
        locations={[0, 0.8]}
        colors={['rgba(233, 233, 233, 1)', 'rgba(244, 244, 244, 0)']}
        style={{
          width: width || '100%', height: height || '1%' }}
      />
    </View>
  );
};

export default FadingLine;
