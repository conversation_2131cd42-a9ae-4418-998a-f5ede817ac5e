import React from 'react';
import { render } from '@testing-library/react-native';
import FlexContainer from './FlexContainer';
import { Text } from 'react-native';

describe('FlexContainer component', () => {
  test('renders correctly with default props', () => {
    const { getByTestId } = render(<FlexContainer />);
    const flexContainer = getByTestId('flex-container');

    expect(flexContainer).toBeTruthy();
  });

  test('renders children', () => {
    const { getByTestId } = render(
      <FlexContainer>
        <Text testID="child-text">Hello, World!</Text>
      </FlexContainer>
    );

    const childText = getByTestId('child-text');
    expect(childText).toBeTruthy();
  });
});
