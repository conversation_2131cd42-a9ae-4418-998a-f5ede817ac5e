import React from 'react';
import {ViewProps} from 'react-native';
import styled from 'styled-components/native';

type FlexContainerProps = ViewProps & {
  children?: React.ReactNode;
  direction?: 'row' | 'column';
  justifyContent?:
    | 'flex-start'
    | 'flex-end'
    | 'center'
    | 'space-between'
    | 'space-around';
  alignItems?: 'flex-start' | 'flex-end' | 'center' | 'stretch';
  flex?: number;
  styles?: any;
};

const StyledFlexContainer = styled.View<FlexContainerProps>`
  flex: ${({flex}) => flex || 1};
  flex-direction: ${({direction}) => direction || 'column'};
  justify-content: ${({justifyContent}) => justifyContent || 'flex-start'};
  align-items: ${({alignItems}) => alignItems || 'stretch'};
  ${({styles}) => styles};
`;

const FlexContainer: React.FC<FlexContainerProps> = ({children, ...props}) => {
  return (
    <StyledFlexContainer {...props} testID="flex-container">
      {children}
    </StyledFlexContainer>
  );
};

export default FlexContainer;
