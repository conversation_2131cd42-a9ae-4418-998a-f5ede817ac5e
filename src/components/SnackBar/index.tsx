import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Animated} from 'react-native';
import {spacing} from '../../constants/theme';
import {colors, sizes} from '../../constants';

interface SnackbarProps {
  visible: boolean;
  message: (string | React.ReactNode)[];
  onOkPress: () => void;
}

const Snackbar: React.FC<SnackbarProps> = ({visible, message, onOkPress}) => {
  const translateY = React.useRef(new Animated.Value(-100)).current;

  React.useEffect(() => {
    if (visible) {
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(translateY, {
        toValue: -100,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, translateY]);

  if (!visible) return null;

  return (
    <Animated.View
      style={[styles.snackbarContainer, {transform: [{translateY}]}]}>
      <View style={styles.content}>
        <Text style={styles.message}>
          {message.map((part, index) =>
            typeof part === 'string' ? (
              part
            ) : (
              <React.Fragment key={index}>{part}</React.Fragment>
            ),
          )}
        </Text>
        <TouchableOpacity style={styles.okButton} onPress={onOkPress}>
          <Text style={styles.okText}>OK</Text>
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  snackbarContainer: {
    position: 'absolute',
    top: '6%',
    left: 0,
    right: 0,
    backgroundColor: 'red',
    padding: spacing.md,
    zIndex: 1000,
    elevation: 10,
    marginHorizontal: spacing.xl,
    borderRadius: spacing.xxs,
  },
  content: {
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: spacing.sm,
  },
  message: {
    color: colors.white,
    fontSize: sizes.h6,
    textAlign: 'center',
  },
  okButton: {
    marginTop: spacing.xl,
    width: '100%',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.white,
    borderRadius: spacing.xxs,
  },
  okText: {
    textAlign: 'center',
    color: colors.darkGrey,
    fontWeight: 'bold',
    fontSize: sizes.body,
  },
});

export default Snackbar;
