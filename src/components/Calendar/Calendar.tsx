import React from 'react';
import { TouchableOpacity, Text, ViewStyle, StyleSheet, ScrollView } from 'react-native';

interface CalendarProps {
  activeView: string;
  subtitleItems: { month: string; day: string }[];
  selectedMonth: string;
  selectedDay: string;
  handleSubtitleItemClick: (item: { month: string; day: string }) => void;
}

const Calendar: React.FC<CalendarProps> = ({
  activeView,
  subtitleItems,
  selectedMonth,
  selectedDay,
  handleSubtitleItemClick,
}) => {
  const renderDay = () => {
    return subtitleItems.map((item, index) => (
      <TouchableOpacity
        key={index}
        onPress={() => handleSubtitleItemClick(item)}
        style={[
          styles.subtitleItem,
          selectedDay === item.day && styles.activeSubtitleItem,
        ]}>
        <Text style={styles.subtitleText}>{item.day}</Text>
        <Text style={styles.subtitleText}>{item.month}</Text>
      </TouchableOpacity>
    ));
  };

  const renderWeek = () => {
    return subtitleItems.map((item, index) => (
      <TouchableOpacity
        key={index}
        onPress={() => handleSubtitleItemClick(item)}
        style={[
          styles.subtitleItem,
          selectedMonth === item.month && styles.activeSubtitleItem,
        ]}>
        <Text style={styles.subtitleText}>{item.month}</Text>
      </TouchableOpacity>
    ));
  };

  const renderMonth = () => {
    return subtitleItems.map((item, index) => (
      <TouchableOpacity
        key={index}
        onPress={() => handleSubtitleItemClick(item)}
        style={[
          styles.subtitleItem,
          selectedMonth === item.month && styles.activeSubtitleItem,
        ]}>
        <Text style={styles.subtitleText}>{item.month}</Text>
      </TouchableOpacity>
    ));
  };

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.subtitleContainer}>
      {activeView === 'day' && renderDay()}
      {activeView === 'week' && renderWeek()}
      {activeView === 'month' && renderMonth()}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  subtitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  subtitleItem: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginHorizontal: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  activeSubtitleItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  subtitleText: {
    fontSize: 16,
    color: 'white',
  },
});

export default Calendar;
