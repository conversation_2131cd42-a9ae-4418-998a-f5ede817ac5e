import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import QRCode from 'react-native-qrcode-svg';
import {colors, GeistFont, sizes} from '../../constants';
import {spacing} from '../../constants/theme';
import {t} from 'i18next';

const QrCode = ({
  upiID,
  payeeName,
  amount,
  route,
  size,
}: {
  upiID?: string;
  payeeName?: string;
  amount?: string;
  route?: string;
  size?: number;
}) => {
  const upiUrl = `upi://pay?pa=${upiID}&pn=${payeeName}&am=${amount}&cu=INR`;

  return (
    <View style={styles.container}>
      <QRCode
        value={upiUrl}
        size={size ? size : 100}
        color="black"
        backgroundColor="white"
      />
      {!route && (
        <Text adjustsFontSizeToFit numberOfLines={2} style={styles.title}>
          {t('scan_pay')}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    margin: spacing.md,
  },
  title: {
    fontSize: sizes.body,
    margin: spacing.md,
    color: colors.grey,
    marginBottom: spacing.xl,
    fontFamily: GeistFont.regular,
    fontWeight: '700',
  },
});

export default QrCode;
