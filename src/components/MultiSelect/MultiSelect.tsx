import React, {FC, useRef, useState, ReactElement, useEffect} from 'react';
import {
  FlatList,
  Modal,
  TouchableOpacity,
  Text,
  View,
  Image,
  Platform,
  Dimensions,
} from 'react-native';
import styled from 'styled-components/native';
import images from '../../constants/images';
import {colors, sizes} from '../../constants';
import {spacing} from '../../constants/theme';
import IconSvgView from '../IconSvgView/IconSvgView';
import Close from '../../icons/close.svg';
import {SafeAreaView, useSafeAreaInsets} from 'react-native-safe-area-context';

interface MultiSelectDropdownProps {
  label: string;
  data: Array<{label: string; value: string}>;
  onSelect: (items: Array<{label: string; value: string}>) => void;
  title: string;
  onClear?: () => void;
  defaultValue?: string | Array<{label: string; value: string}>;
}

// Styled components
const StyledDropdownTitle = styled.Text`
  font-family: GeistVariableVF;
  font-weight: 400;
  font-size: ${sizes.body}px;
  margin-vertical: ${spacing.md}px;
  color: #838489;
`;

const DropdownButtonContainer = styled(TouchableOpacity)`
  flex-direction: row;
  align-items: center;
  background-color: rgba(226, 227, 233, 0.1);
  height: ${spacing.xxl * 2}px;
  z-index: 1;
  border-radius: 4px;
  border-color: #838489;
  border-width: 1px;
`;

const DropdownText = styled(Text)`
  flex: 1;
  padding: ${spacing.md}px;
  color: ${colors.lightGrey};
  font-family: GeistVariableVF;
  font-size: ${sizes.body}px;
`;

const DropdownItem = styled(TouchableOpacity)`
  padding-horizontal: ${spacing.md}px;
  padding-vertical: ${spacing.xxs}px;
  border-bottom-width: 1px;
  border-bottom-color: rgba(131, 132, 137, 0.3);
`;

const DropdownItemText = styled(Text)`
  font-family: GeistVariableVF;
  font-weight: 400;
  font-size: ${sizes.body}px;
  margin-vertical: ${spacing.md}px;
  color: ${colors.grey};
`;

const MultiSelect: FC<MultiSelectDropdownProps> = ({
  label,
  data,
  onSelect,
  title,
  onClear,
  defaultValue,
}) => {
  const DropdownButton = useRef<TouchableOpacity>(null);
  const [visible, setVisible] = useState(false);
  const [selectedItems, setSelectedItems] = useState<
    Array<{label: string; value: string}>
  >([]);
  const [dropdownTop, setDropdownTop] = useState(0);
  const prevDefaultValueRef = useRef<
    string | Array<{label: string; value: string}> | undefined
  >();

  const insets = useSafeAreaInsets();
  const windowHeight = Dimensions.get('window').height;

  useEffect(() => {
    if (defaultValue !== prevDefaultValueRef.current) {
      let initialItems: Array<{label: string; value: string}> = [];

      if (defaultValue) {
        if (typeof defaultValue === 'string') {
          const values = defaultValue.split(',').map(v => v.trim());
          initialItems = data.filter(item =>
            values.some(v => item.label.toLowerCase() === v.toLowerCase()),
          );
        } else if (Array.isArray(defaultValue)) {
          initialItems = defaultValue.filter(defaultItem =>
            data.some(dataItem => dataItem.value === defaultItem.value),
          );
        }
      }

      setSelectedItems(initialItems);
      onSelect(initialItems);
      prevDefaultValueRef.current = defaultValue;
    }
  }, [defaultValue, data]);

  const openDropdown = (): void => {
    DropdownButton.current?.measure((_fx, _fy, _w, h, _px, py) => {
      const topOffset = Platform.OS === 'android' ? insets.top : 0;
      const dropdownY = py + h - topOffset;
      setDropdownTop(dropdownY);
      
      const availableSpaceBelow = windowHeight - dropdownY - insets.bottom;
      const estimatedDropdownHeight = Math.min(data.length * 50, 300);
      
      if (availableSpaceBelow < estimatedDropdownHeight && dropdownY > estimatedDropdownHeight) {
        setDropdownTop(dropdownY - estimatedDropdownHeight - h);
      }
    });
    setVisible(true);
  };

  const onItemPress = (item: {label: string; value: string}): void => {
    const isItemSelected = selectedItems.some(
      selected => selected.value === item.value,
    );
    const updatedItems = isItemSelected
      ? selectedItems.filter(selected => selected.value !== item.value)
      : [...selectedItems, item];

    setSelectedItems(updatedItems);
    onSelect(updatedItems);
  };

  const clearSelection = (): void => {
    setSelectedItems([]);
    onSelect([]);
    if (onClear) {
      onClear();
    }
    setVisible(false);
  };

  const closeDropdown = () => {
    setVisible(false);
  };

  const renderItem = ({
    item,
  }: {
    item: {label: string; value: string};
  }): ReactElement => {
    const isSelected = selectedItems.some(
      selected => selected.value === item.value,
    );
    return (
      <DropdownItem onPress={() => onItemPress(item)}>
        <DropdownItemText>
          {isSelected ? '✓ ' : ''}
          {item.label}
        </DropdownItemText>
      </DropdownItem>
    );
  };

  const getDisplayText = () => {
    if (selectedItems.length === 0) {
      return label;
    }
    return selectedItems.map(item => item.label).join(', ');
  };

  const maxDropdownHeight = windowHeight - dropdownTop - insets.bottom - 20;

  return (
    <>
      <StyledDropdownTitle>{title}</StyledDropdownTitle>
      <DropdownButtonContainer ref={DropdownButton} onPress={openDropdown}>
        <DropdownText numberOfLines={1}>{getDisplayText()}</DropdownText>
        {selectedItems.length > 0 ? (
          <TouchableOpacity
            style={{padding: 15}}
            onPress={clearSelection}
            hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
            <IconSvgView size={14} source={Close} />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={{marginRight: spacing.md}}
            hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
            <Image source={images.dropDown} style={{width: 20, height: 20}} />
          </TouchableOpacity>
        )}
      </DropdownButtonContainer>
      <Modal visible={visible} transparent animationType="none">
        <TouchableOpacity
          style={{flex: 1, backgroundColor: 'rgba(0,0,0)'}}
          activeOpacity={1}
          onPress={closeDropdown}>
          <SafeAreaView style={{flex: 1}}>
            <View
              style={{
                backgroundColor: colors.darkCharcoal,
                position: 'absolute',
                top: dropdownTop,
                left: '5.5%',
                right: '5.5%',
                borderRadius: 5,
                shadowColor: '#000',
                shadowOffset: {width: 0, height: 2},
                shadowOpacity: 0.25,
                shadowRadius: 4,
                elevation: 5,
                maxHeight: Math.min(data.length * 50, windowHeight * 0.4), // Limit to 40% of screen height
              }}>
              <FlatList
                data={data}
                renderItem={renderItem}
                keyExtractor={item => item.value}
                showsVerticalScrollIndicator
                nestedScrollEnabled={true}
              />
            </View>
          </SafeAreaView>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

export default MultiSelect;
