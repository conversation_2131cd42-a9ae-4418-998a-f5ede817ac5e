import {StyleSheet} from 'react-native';
import {spacing} from '../../constants/theme';
import {GeistFont, colors, sizes} from '../../constants';

export default StyleSheet.create({
  marker: {
    flex: 1,
    resizeMode: 'cover',
  },
  locationContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: '#7b7c82',
    borderRadius: 1,
    height: 50,
    width: 50,
  },
  diamondIcon: {
    marginRight: spacing.xl,
  },
  markerFixed: {
    left: 0,
    right: 0,
    marginLeft: 0,
    marginTop: 0,
    position: 'absolute',
    top: '40%',
    alignItems: 'center',
  },
  mapPinContainer: {
    backgroundColor: colors.white,
    padding: spacing.xs,
  },
  mapPinText: {
    color: colors.black,
    fontSize: sizes.body,
    fontWeight: '600',
  },
  appGif: {
    width: 350,
    height: 350,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderImage: {
    width: 50,
    height: 50,
  },
});
