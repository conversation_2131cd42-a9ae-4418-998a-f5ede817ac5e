import React, {forwardRef, useEffect, useState} from 'react';
import {Animated, View, StyleSheet, Modal, Image} from 'react-native';
import MapView, {
  Details,
  LatLng,
  PROVIDER_GOOGLE,
  Polyline,
  UserLocationChangeEvent,
} from 'react-native-maps';
import {colors, images} from '../../constants';
import {DarkMapStyle} from '../../constants/constants';
import {calculateDistance} from '../../utils/MapUtils';
import {userLocationContext} from '../../hooks/userLocationContext';
import {useLoader} from '../../hooks/useLoader.tsx';
import Config from 'react-native-config';

const MapComponent = forwardRef<
  MapView,
  {
    setAddress: (address: string) => void;
    showLocation?: boolean;
    marker?: boolean;
    children?: React.ReactNode;
    style?: any;
    onMapMove?: () => void;
    mapPinText?: string;
    region: LatLng | null;
    setRegion: (region: LatLng | null) => void;
  }
>(
  (
    {
      setAddress,
      showLocation,
      marker = true,
      children,
      style,
      onMapMove,
      mapPinText,
      region,
      setRegion,
    },
    ref,
  ) => {
    const {userLocation, setUserLocation} = userLocationContext();
    const [showText, setShowText] = useState<boolean>(false);
    const [isMapLoading, setIsMapLoading] = useState<boolean>(true);
    const scaleAnim = useState(new Animated.Value(0))[0];
    const {showLoader, hideLoader} = useLoader();

    const fetchAddress = async (latitude: number, longitude: number) => {
      try {
        showLoader();
        const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${Config.GOOGLE_API}`;
        const response = await fetch(url);
        const data = await response.json();
        if (data.status === 'OK') {
          setAddress(data.results[0].formatted_address);
        } else {
          setAddress('Unable to retrieve address');
        }
      } catch (error) {
        setAddress('Error fetching address');
      } finally {
        hideLoader();
      }
    };

    useEffect(() => {
      if (region?.latitude && region?.longitude) {
        fetchAddress(region.latitude, region.longitude);
      }
    }, [region]);

    const handlechange = (e: UserLocationChangeEvent) => {
      e.persist();
      const coordinate = e.nativeEvent.coordinate;

      coordinate?.latitude &&
        coordinate.longitude &&
        setUserLocation({
          latitude: coordinate.latitude,
          longitude: coordinate.longitude,
        });
    };

    const handleRegionChangeComplete = (region: LatLng, details: Details) => {
      if (details.isGesture && onMapMove) {
        setRegion(region);
        onMapMove();
        setShowText(true);
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }).start();
      }
    };

    const INITIAL_REGION = userLocation
      ? {
          latitude: userLocation.latitude,
          longitude: userLocation.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }
      : {
          latitude: 20.5937,
          longitude: 78.9629,
          latitudeDelta: 5,
          longitudeDelta: 5,
        };

    return (
      <View style={[styles.container, style]}>
        <View style={styles.mapContainer}>
          <MapView
            initialRegion={INITIAL_REGION}
            provider={PROVIDER_GOOGLE}
            ref={ref}
            style={[StyleSheet.absoluteFillObject, styles.map]}
            showsUserLocation={showLocation}
            showsMyLocationButton={false}
            showsCompass={false}
            onRegionChangeComplete={handleRegionChangeComplete}
            customMapStyle={DarkMapStyle}
            onUserLocationChange={handlechange}
            onMapLoaded={() => setIsMapLoading(false)}
            loadingEnabled={false}
            loadingBackgroundColor={colors.black}
            userLocationUpdateInterval={20000}>
            {children}
            {region &&
            userLocation &&
            marker &&
            calculateDistance(userLocation, region)! <= 250 ? (
              <Polyline
                coordinates={[userLocation, region]}
                strokeColor="white"
                strokeWidth={4}
                lineDashPattern={[20, 10]}
              />
            ) : null}
          </MapView>
        </View>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  mapContainer: {
    flex: 1,
    backgroundColor: colors.black,
  },
  map: {
    backgroundColor: colors.black,
  },
  loaderContainer: {
    flex: 1,
    backgroundColor: colors.black,
  },
  loaderWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.black,
  },
  loaderImage: {
    width: 50,
    height: 50,
  },
});

export default MapComponent;
