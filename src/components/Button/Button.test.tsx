import { render, fireEvent } from '@testing-library/react-native';
import Button from './Button';

describe('Button component', () => {
    test('renders correctly with default props', () => {
        const { getByText, getByTestId } = render(<Button title="Test Button" />);

        const button = getByTestId('button-container');
        const buttonText = getByText('Test Button');

        expect(button).toBeTruthy();
        expect(buttonText).toBeTruthy();
    });

    test('handles press event when not disabled', () => {
        const onPressMock = jest.fn();
        const { getByText, getByTestId } = render(
            <Button title="Enabled Button" onPress={onPressMock} />
        );

        const button = getByTestId('button-container');
        fireEvent.press(button);

        expect(onPressMock).toHaveBeenCalled();
    });

    test('does not handle press event when disabled', () => {
        const onPressMock = jest.fn();
        const { getByTestId } = render(
            <Button title="Disabled Button" onPress={onPressMock} disabled />
        );

        const button = getByTestId('button-container');
        fireEvent.press(button);

        expect(onPressMock).not.toHaveBeenCalled();
    });
});
