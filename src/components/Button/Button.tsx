import React from 'react';
import {
  TouchableOpacity,
  Text,
  TouchableOpacityProps,
  View,
  Image,
  StyleSheet,
} from 'react-native';
import styled from 'styled-components/native';
import {spacing} from '../../constants/theme';
import {GeistFont, colors, sizes} from '../../constants';

interface ButtonProps extends TouchableOpacityProps {
  title: string;
  disabled?: boolean;
  textColor?: string;
  loading?: boolean; 
}

const ButtonContainer = styled(TouchableOpacity)<{disabled?: boolean}>`
  padding: ${spacing.md}px ${spacing.lg}px;
  border-radius: ${spacing.xxs}px;
  align-items: center;
  justify-content: center;
  background-color: ${({disabled}) =>
    disabled ? 'rgba(45, 46, 50, 0.40)' : colors.lightGrey};
`;

const ButtonText = styled(Text)<{disabled?: boolean; textColor?: string}>`
  color: ${({disabled, textColor}) =>
    disabled ? '#4A4B4F' : textColor ? textColor : colors.davyGrey};
  font-size: ${sizes.h6}px;
  font-weight: 500;
  font-style: ${GeistFont.regular};
`;

const Button: React.FC<ButtonProps> = ({
  onPress,
  title,
  disabled = false,
  textColor,
  loading = false, 
  ...rest
}) => {
  return (
    <ButtonContainer
      onPress={disabled || loading ? undefined : onPress} 
      disabled={disabled || loading} 
      {...rest}
      testID="button-container">
      <ButtonText
        disabled={disabled}
        textColor={textColor}
        testID="button-text">
        {loading ? (
          <View style={styles.loaderContainer}>
            <Image
              source={require('../../icons/LOADER.gif')}
              style={styles.loaderImage}
            />
          </View>
        ) : (
          title
        )}
      </ButtonText>
    </ButtonContainer>
  );
};

const styles = StyleSheet.create({
  loaderContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderImage: {
    width: 30,
    height: 30,
  },
});

export default Button;
