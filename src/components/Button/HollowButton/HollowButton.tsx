import React from 'react';
import {
  TouchableOpacity,
  Text,
  TouchableOpacityProps,
  View,
  Image,
  StyleSheet,
} from 'react-native';
import styled from 'styled-components/native';
import {colors, sizes} from '../../../constants';
import {spacing} from '../../../constants/theme';

interface ButtonProps extends TouchableOpacityProps {
  title: string;
  disabled?: boolean;
  textColor?: string;
  borderColor?: string;
  loading?: boolean;
}

const ButtonContainer = styled(TouchableOpacity)<{
  disabled?: boolean;
  borderColor?: string;
}>`
  padding: ${spacing.md}px ${spacing.lg}px;
  border-radius: ${spacing.xxs}px;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border-width: 2px;
  border-color: ${({disabled, borderColor}) =>
    disabled
      ? 'rgba(45, 46, 50, 0.40)'
      : borderColor
      ? borderColor
      : colors.lightGrey};
`;

const ButtonText = styled(Text)<{disabled?: boolean; textColor?: string}>`
  color: ${({disabled, textColor}) =>
    disabled ? '#4A4B4F' : textColor ? textColor : colors.lightGrey};
  font-size: ${sizes.h6}px;
  font-weight: 500;
`;

const HollowButton: React.FC<ButtonProps> = ({
  onPress,
  title,
  disabled = false,
  textColor,
  borderColor,
  loading = false,
  ...rest
}) => {
  return (
    <ButtonContainer
      onPress={disabled || loading ? undefined : onPress}
      disabled={disabled || loading}
      borderColor={borderColor}
      {...rest}
      testID="button-container">
      {loading ? (
        <View style={styles.loaderContainer}>
          <Image
            source={require('../../../icons/LOADER.gif')}
            style={styles.loaderImage}
          />
        </View>
      ) : (
        <ButtonText
          numberOfLines={2}
          disabled={disabled}
          textColor={textColor}
          testID="button-text">
          {title}
        </ButtonText>
      )}
    </ButtonContainer>
  );
};

const styles = StyleSheet.create({
  loaderContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderImage: {
    width: 30,
    height: 30,
  },
});

export default HollowButton;
