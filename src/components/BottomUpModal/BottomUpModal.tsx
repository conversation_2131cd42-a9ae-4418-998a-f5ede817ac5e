import React, {useRef} from 'react';
import {
  Modal,
  ScrollView,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  View,
  TouchableWithoutFeedback,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import {SafeAreaView} from 'react-native-safe-area-context';
import close from '../../icons/close.svg';
import styled from 'styled-components/native';
import IconSvgView from '../IconSvgView/IconSvgView';
import {colors, sizes} from '../../constants';
import Button from '../Button/Button';
import FadingHorizontalLine from '../FadingLine/FadingHorizontalLine';
import {spacing} from '../../constants/theme';

const {height} = Dimensions.get('window');

interface BottomUpModalProps {
  showModal: boolean;
  onClose: () => void;
  title: string;
  description: string;
  buttonText: string;
  onButtonClick: () => void;
  forceUpdate: boolean;
}

const BottomUpModal: React.FC<BottomUpModalProps> = ({
  showModal,
  onClose,
  title,
  description,
  buttonText,
  onButtonClick,
  forceUpdate,
}) => {
  const {t} = useTranslation();
  const scrollViewRef = useRef<ScrollView>(null);

  return (
    <Modal
      transparent={true}
      animationType="slide"
      visible={showModal}
      onRequestClose={() => {
        if (!forceUpdate) onClose();
      }}>
      <TouchableWithoutFeedback>
        <OverlayContainer>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={{flex: 1}}>
            <SafeAreaView style={styles.safeArea}>
              <ModalContainer>
                <HeaderContainer>
                  <Title>{title}</Title>
                  {!forceUpdate && (
                    <CloseButton onPress={onClose}>
                      <IconSvgView width={14} source={close} />
                    </CloseButton>
                  )}
                </HeaderContainer>
                <FadingHorizontalLine />

                <Description>{description}</Description>

                <Button title={buttonText} onPress={onButtonClick} />
              </ModalContainer>
            </SafeAreaView>
          </KeyboardAvoidingView>
        </OverlayContainer>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const OverlayContainer = styled.View`
  flex: 1;
  background-color: rgba(43, 43, 43, 0.3);
  justify-content: flex-end;
`;

const ModalContainer = styled(View)`
  background-color: ${colors.darkCharcoal};
  padding: ${spacing.xxl}px;
  border-top-left-radius: ${spacing.xxl}px;
  border-top-right-radius: ${spacing.xxl}px;
  elevation: 5;
  shadow-color: #000;
  shadow-opacity: 0.1;
  shadow-radius: 10px;
`;

const HeaderContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${spacing.xxl}px;
`;

const Title = styled.Text`
  font-size: ${sizes.h5}px;
  font-weight: bold;
  line-height: ${sizes.body * 2}px;
`;

const CloseButton = styled.TouchableOpacity``;

const Description = styled.Text`
  font-size: ${sizes.body}px;
  color: ${colors.white};
  margin-vertical: ${spacing.xl}px;
  line-height: ${sizes.body * 1.5}px;
`;

const styles = {
  safeArea: {
    flex: 1,
    justifyContent: 'flex-end' as 'flex-end',
  },
};

export default BottomUpModal;
