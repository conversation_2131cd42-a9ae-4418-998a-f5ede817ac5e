import React, {useRef, useEffect, useState} from 'react';
import {
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  Text,
  Animated,
  Linking,
  Platform,
  Alert,
  Dimensions,
  StyleSheet,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import FadingHorizontalLine from '../FadingLine/FadingHorizontalLine';
import {colors, GeistFont, sizes} from '../../constants/fonts';
import {spacing} from '../../constants/theme';
import Api from '../../services/Api'; 
import {requestCallPhonePermission} from '../../constants/permissions';
import support from '../../icons/support.svg';

interface SupportMenuProps {
  visible: boolean;
  onClose: () => void;
  position?: {x?: number; y?: number; width?: number; height?: number} | null;
}

const SupportMenu: React.FC<SupportMenuProps> = ({
  visible,
  onClose,
  position = null,
}) => {
  const {t} = useTranslation();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const [supportContacts, setSupportContacts] = useState({
    primary: '+911234567890', 
    secondary: '+919876543210', 
  });
  const windowDimensions = Dimensions.get('window');
  

  useEffect(() => {
    if (visible) {
      fetchDriverConfig();
      
      fadeAnim.setValue(0);
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  const fetchDriverConfig = async () => {
    try {
      const response = await Api.get('driver-config');
      
      if (
        response.data &&
        response.data.data &&
        Array.isArray(response.data.data)
      ) {
        const configItems = response.data.data;

        // Extract phone numbers from the config data
        const contacts = {
          primary:
            configItems.find(
              (item: {key: string}) => item.key === 'support_contact_primary',
            )?.value || '+911234567890',
          secondary:
            configItems.find(
              (item: {key: string}) => item.key === 'support_contact_secondary',
            )?.value || '+919876543210',
        };

        setSupportContacts(contacts);
      }
    } catch (error) {
      console.error('Failed to fetch driver config:', error);
      // Fallback contacts are already set in state initialization
    }
  };

  const makeCall = async (phoneNumber: string) => {
    if (!phoneNumber) {
      Alert.alert('No phone number available');
      return;
    }

    // Format phone number properly
    let formattedNumber = phoneNumber;
    if (!phoneNumber.startsWith('+')) {
      formattedNumber = `+91${phoneNumber.replace(/^\+91/, '')}`;
    }

    // Check for call phone permission
    const hasPermission = await requestCallPhonePermission();
    
    if (hasPermission) {
      Linking.openURL(`tel:${formattedNumber}`).catch(() =>
        Alert.alert('Error', 'Unable to make a call'),
      );
    } else {
      Alert.alert(
        'Permission Required',
        'Call permission is required to make a phone call. Please enable it in settings.',
        [
          {text: 'Cancel', style: 'cancel'},
          {text: 'Open Settings', onPress: () => Linking.openSettings()},
        ],
      );
    }
  };

  if (!visible) return null;

  let menuRight = 20;
  let menuTop = 100;
  
  if (position && position.x !== undefined && position.y !== undefined) {
    menuRight = windowDimensions.width - (position.x + (position.width || 0));
    menuTop = position.y + (position.height || 0) + 5;
  }

  return (
    <TouchableWithoutFeedback onPress={onClose}>
      <View style={styles.overlay}>
        <Animated.View 
          style={[
            styles.menuContent,
            {
              position: 'absolute',
              top: menuTop,
              right: menuRight,
              opacity: fadeAnim,
            }
          ]}
        >
          <TouchableOpacity
            activeOpacity={0.7}
            style={styles.supportOption}
            onPress={() => {
              onClose();
              makeCall(supportContacts.primary);
            }}>
            <Text style={styles.supportOptionText}>
              {t('primary_support')}
            </Text>
          </TouchableOpacity>

          <View style={styles.dividerContainer}>
            <FadingHorizontalLine />
          </View>

          <TouchableOpacity
            activeOpacity={0.7}
            style={styles.supportOption}
            onPress={() => {
              onClose();
              makeCall(supportContacts.secondary);
            }}>
            <Text style={styles.supportOptionText}>
              {t('secondary_support')}
            </Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999, 
  },
  menuContent: {
    backgroundColor: colors.darkGrey,
    borderRadius: spacing.xs,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 10000, 
    minWidth: 180,
    width: 'auto',
  },
  dividerContainer: {
    width: '100%',
    marginVertical: spacing.sm,
  },
  supportOption: {
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.sm,
  },
  supportOptionText: {
    color: colors.white,
    fontSize: sizes.h5,
    fontFamily: GeistFont.regular,
  },
});

export default SupportMenu;
