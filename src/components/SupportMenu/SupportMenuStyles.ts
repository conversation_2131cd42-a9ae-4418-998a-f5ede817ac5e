import { StyleSheet } from 'react-native';
import { colors, spacing, sizes } from '../../constants';
import { GeistFont } from '../../constants/fonts';

export default StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.darkGrey,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: spacing.lg,
    paddingBottom: spacing.xl * 2,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  modalTitle: {
    color: colors.white,
    fontSize: sizes.h4,
    fontFamily: GeistFont.bold,
  },
  supportOption: {
    paddingVertical: spacing.lg,
  },
  supportOptionText: {
    color: colors.white,
    fontSize: sizes.h5,
    fontFamily: GeistFont.regular,
  },
});