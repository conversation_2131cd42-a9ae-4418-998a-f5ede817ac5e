import React, {FC, ReactElement, useEffect, useRef, useState} from 'react';
import {
  FlatList,
  Modal,
  TouchableOpacity,
  Text,
  View,
  Image,
  Platform,
  Keyboard,
  Dimensions,
} from 'react-native';
import styled from 'styled-components/native';
import images from '../../constants/images';
import {colors, sizes} from '../../constants';
import {spacing} from '../../constants/theme';
import {StatusBar} from 'react-native';

const {height: windowHeight} = Dimensions.get('window');

interface DropdownProps {
  label: string;
  data: Array<{label: string; value: string}>;
  onSelect: (item: {label: string; value: string}) => void;
  title: string;
  dropdownStyle?: object;
  defaultValue?: string;
}

const StyledDropdownTitle = styled.Text`
  font-family: GeistVariableVF;
  font-weight: 400;
  font-size: ${sizes.body}px;
  margin-vertical: ${spacing.md}px;
  color: #838489;
`;

const DropdownButtonContainer = styled(TouchableOpacity)`
  flex-direction: row;
  align-items: center;
  background-color: rgba(226, 227, 233, 0.1);
  height: ${spacing.xxl * 2}px;
  z-index: 1;
  border-radius: 4px;
  border-color: #838489;
  border-width: 1px;
  color: ${colors.grey};
`;

const DropdownButtonText = styled(Text)`
  flex: 1;
  padding: ${spacing.md}px;
  color: ${colors.lightGrey};
  font-family: GeistVariableVF;
`;

const DropdownIcon = styled(Image)`
  margin-right: ${spacing.md}px;
`;

const DropdownModal = styled(Modal)`
  transparent: true;
`;

const Overlay = styled(TouchableOpacity)`
  height: 100%;
  align-items: center;
  margin-horizontal: ${spacing.xxs * 9}px;
`;

const DropdownView = styled(View)<{customStyle?: object}>`
  position: absolute;
  background-color: ${colors.darkCharcoal};
  width: 100%;
  ${props => props.customStyle && {...props.customStyle}};
  shadow-color: #000000;
  shadow-radius: 4px;
  shadow-opacity: 0.5;
  border-radius: 5px;
`;

const DropdownItem = styled(TouchableOpacity)`
  padding-horizontal: ${spacing.md}px;
  padding-vertical: ${spacing.xxs}px;
  border-bottom-width: 1px;
`;

const DropdownItemText = styled(Text)`
  font-family: GeistVariableVF;
  font-weight: 400;
  font-size: ${sizes.body}px;
  margin-vertical: ${spacing.md}px;
  color: ${colors.grey};
`;

const Dropdown: FC<DropdownProps> = ({
  label,
  data,
  onSelect,
  title,
  dropdownStyle,
  defaultValue,
}) => {
  const DropdownButton = useRef<TouchableOpacity>(null);
  const [visible, setVisible] = useState(false);
  const [selected, setSelected] = useState<
    {label: string; value: string} | undefined
  >(undefined);
  const [dropdownTop, setDropdownTop] = useState(0);

  useEffect(() => {
    if (defaultValue) {
      let defaultItem = data.find(item => item.value === defaultValue);

      if (!defaultItem) {
        defaultItem = data.find(
          item => item.label.toLowerCase() === defaultValue.toLowerCase(),
        );
      }

      if (!defaultItem) {
        defaultItem = data.find(
          item => item.label.toLowerCase() === defaultValue.toLowerCase(),
        );
      }

      if (defaultItem) {
        setSelected(defaultItem);
      }
    }
  }, [defaultValue, data]);

  const toggleDropdown = (): void => {
    Keyboard.dismiss();
    visible ? setVisible(false) : openDropdown();
  };

  const openDropdown = (): void => {
    if (DropdownButton.current) {
      DropdownButton.current.measure((_fx, _fy, _w, h, _px, py) => {
        const topOffset =
          Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0;
        const calculatedTop = py + h - topOffset;
        
        const availableSpace = windowHeight - calculatedTop - 20; // 20px buffer
        const estimatedDropdownHeight = Math.min(data.length * 50, 200); // Estimate height
        
        if (availableSpace < estimatedDropdownHeight && calculatedTop > estimatedDropdownHeight) {
          setDropdownTop(calculatedTop - estimatedDropdownHeight - h);
        } else {
          setDropdownTop(calculatedTop);
        }
        
        setVisible(true);
      });
    }
  };

  const onItemPress = (item: {label: string; value: string}): void => {
    setSelected(item);
    onSelect(item);
    setVisible(false);
  };
  const renderItem = ({
    item,
  }: {
    item: {label: string; value: string};
  }): ReactElement => (
    <DropdownItem
      onPress={() => onItemPress(item)}
      activeOpacity={0.7}
      delayPressIn={0}>
      <DropdownItemText>{item.label}</DropdownItemText>
    </DropdownItem>
  );

  const renderDropdown = (): ReactElement => (
    <DropdownModal visible={visible} transparent animationType="none">
      <Overlay onPress={() => setVisible(false)} activeOpacity={1}>
        <DropdownView style={{top: dropdownTop}} customStyle={dropdownStyle}>
          <FlatList
            data={data}
            renderItem={renderItem}
            keyExtractor={(item, index) => index.toString()}
            style={{maxHeight: Math.min(data.length * 50, 200)}} // Limit height based on items
          />
        </DropdownView>
      </Overlay>
    </DropdownModal>
  );

  return (
    <>
      <StyledDropdownTitle>{title}</StyledDropdownTitle>
      <DropdownButtonContainer ref={DropdownButton} onPress={toggleDropdown}>
        {renderDropdown()}
        <DropdownButtonText>
          {(selected && selected.label) || label}
        </DropdownButtonText>
        <DropdownIcon source={images.dropDown} />
      </DropdownButtonContainer>
    </>
  );
};

export default Dropdown;
