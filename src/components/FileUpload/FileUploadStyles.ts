import {Platform, StyleSheet, Dimensions, ViewStyle} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

const {width, height} = Dimensions.get('window');

const responsiveWidth = (percentage: number) => (width * percentage) / 100;
const responsiveHeight = (percentage: number) => (height * percentage) / 100;
const isSmallDevice = width < 375;
const responsiveSpacing = (size: number) => (isSmallDevice ? size * 0.8 : size);

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },
  safeArea: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: spacing.xl,
  },
  iconContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: responsiveSpacing(spacing.xxl),
  },
  icon: {
    marginTop: responsiveSpacing(spacing.xl),
  },
  diamondIcon: {
    marginTop: responsiveSpacing(spacing.xl),
    marginRight: responsiveSpacing(spacing.xl),
  },
  backContainer: {
    position: 'absolute',
    top: responsiveHeight(6),
    left: responsiveWidth(5),
  },
  title: {
    fontFamily: EBGaramondFont.regular,
    fontSize: isSmallDevice ? sizes.largeTitle * 0.9 : sizes.largeTitle,
    color: colors.white,
    marginTop: responsiveSpacing(spacing.md),
  },
  subtitle: {
    fontFamily: GeistFont.variable,
    fontSize: isSmallDevice ? sizes.body * 0.9 : sizes.body,
    color: colors.grey,
    marginVertical: responsiveSpacing(spacing.lg),
  },
  uploadCard: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: spacing.xxs,
    backgroundColor: colors.darkCharcoal,
    width: '100%',
    height: responsiveHeight(30), // Relative height
    minHeight: 180, // Minimum height to ensure visibility
    marginTop: responsiveSpacing(spacing.lg),
  },
  uploadOptionsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    marginBottom: responsiveSpacing(spacing.md),
  },
  uploadButton: {
    backgroundColor: colors.darkCharcoal,
    paddingVertical: responsiveSpacing(spacing.md),
    paddingHorizontal: responsiveSpacing(spacing.xl),
    borderRadius: spacing.xs,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  uploadText: {
    fontFamily: GeistFont.variable,
    fontSize: isSmallDevice ? sizes.body * 0.9 : sizes.body,
    color: colors.white,
    marginTop: responsiveSpacing(spacing.md),
  },
  continueBtn: {
    marginBottom: spacing.sm,
    width: '100%',
  } as ViewStyle,
  cancelBtn: {
    backgroundColor: 'rgba(192, 104, 104, 0.04)',
    width: '100%',
  },
  filePreview: {
    position: 'relative',
    width: responsiveWidth(50),
    maxWidth: 200,
    height: responsiveWidth(50),
    maxHeight: 200,
    marginBottom: responsiveSpacing(spacing.md),
  },
  filePreviewImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  removeIcon: {
    position: 'absolute',
    top: responsiveSpacing(spacing.sm),
    right: responsiveSpacing(spacing.sm),
    zIndex: 1,
  },
  backIcon: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginTop: responsiveSpacing(spacing.xl),
  },
  pdfContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: responsiveSpacing(10),
  },
  fileName: {
    marginTop: responsiveSpacing(5),
    fontSize: isSmallDevice ? 10 : 12,
    color: colors.lightGrey,
    maxWidth: responsiveWidth(30),
    textAlign: 'center',
  },
  scrollContent: {
    flexGrow: 1,
    paddingTop: spacing.md,
    paddingBottom: spacing.xxl * 3, // Increased padding for fixed footer
  },
  validationText: {
    marginTop: responsiveSpacing(spacing.sm),
    fontSize: isSmallDevice ? sizes.body * 0.8 : sizes.body,
    color: colors.lightGrey,
    fontFamily: GeistFont.regular,
  },
  fixedHeader: {
    width: '100%',
    paddingTop: Platform.OS === 'ios' ? spacing.md : spacing.sm,
    backgroundColor: 'transparent',
    zIndex: 10,
  },
  fixedFooter: {
    width: '100%',
    paddingVertical: spacing.md,
    backgroundColor: colors.black, 
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 10,
  },
});
