import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import OTPInput from './OTPInput';


describe('OTPInput component', () => {
  const onOtpChangeMock = jest.fn();
  const onOtpCompleteMock = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(
      <OTPInput
        onOtpChange={onOtpChangeMock}
        defaultValue="123456"
        onOtpComplete={onOtpCompleteMock}
      />,
    );
  });

  it('displays the correct number of input fields', () => {
    const { getAllByTestId } = render(
      <OTPInput
        onOtpChange={onOtpChangeMock}
        defaultValue="123456"
        onOtpComplete={onOtpCompleteMock}
      />,
    );
  
    const inputFields = Array.from({ length: 6 }, (_, index) =>
      getAllByTestId(`otp-input-field-${index}`),
    );
  
    expect(inputFields.flat()).toHaveLength(6);
  });
  
});
