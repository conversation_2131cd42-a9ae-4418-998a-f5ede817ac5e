import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import IconSvgView from '../IconSvgView/IconSvgView';
import upload from '../../icons/upload.svg';
import camera from '../../icons/camera.svg';
import closeCircle from '../../icons/closeCircle.svg';
import { colors, GeistFont } from '../../constants';
import { spacing } from '../../constants/theme';

interface UploadOptionsModalProps {
  visible: boolean;
  onClose: () => void;
  onCameraSelect: () => void;
  onDocumentSelect: () => void;
}

const { width } = Dimensions.get('window');
const isSmallDevice = width < 375;

const UploadOptionsModal: React.FC<UploadOptionsModalProps> = ({
  visible,
  onClose,
  onCameraSelect,
  onDocumentSelect,
}) => {
  const { t } = useTranslation();

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{t('choose_upload_method')}</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <IconSvgView source={closeCircle} width={24} height={24} />
            </TouchableOpacity>
          </View>

          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={styles.optionButton}
              onPress={onDocumentSelect}
            >
              <IconSvgView
                source={upload}
                size={isSmallDevice ? 24 : 30}
              />
              <Text style={styles.optionText}>{t('upload_document')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.optionButton}
              onPress={onCameraSelect}
            >
              <IconSvgView
                source={camera}
                size={isSmallDevice ? 24 : 30}
              />
              <Text style={styles.optionText}>{t('take_photo')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    width: '80%',
    backgroundColor: colors.darkCharcoal,
    borderRadius: spacing.xs,
    padding: spacing.xl,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  closeButton: {
    padding: spacing.xs,
  },
  modalTitle: {
    fontSize: isSmallDevice ? 18 : 20,
    fontFamily: GeistFont.regular,
    color: colors.white,
    textAlign: 'left',
  },
  optionsContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  optionButton: {
    alignItems: 'center',
    padding: spacing.lg,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    width: '45%',
  },
  optionText: {
    marginTop: spacing.md,
    color: colors.white,
    fontFamily: GeistFont.regular,
    fontSize: isSmallDevice ? 14 : 16,
    textAlign: 'center',
  },
});

export default UploadOptionsModal;
