import React, {useState, useEffect, useCallback, useRef} from 'react';
import {
  CardStyleInterpolators,
  createStackNavigator,
} from '@react-navigation/stack';
import {
  View,
  Text,
  StyleSheet,
  AppState,
  Easing,
  NativeModules,
  Platform,
  AppStateStatus,
  Linking,
} from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import messaging from '@react-native-firebase/messaging';
import {colors, GeistFont, sizes} from '../constants';
import {appRoutes} from './router';
import {useLoader} from '../hooks/useLoader';
import FallBack from '../screens/FallBack';
import OfflineScreen from '../screens/Offline';
import RideModal from '../screens/RideModal';
import {useRideDetails} from '../hooks/useRideDetailsContext';
import {useToast} from '../components/Toast/Toast';
import TripService from '../services/TripService';
import {playRideStatusSound, stopAllSounds} from '../utils/Sound';
import {
  MAX_RETRIES,
  RETRY_DELAY,
  RideEvents,
  STATUS_CODE,
} from '../constants/constants';
import {spacing} from '../constants/theme';
import {useTranslation} from 'react-i18next';
import notifee, {
  AndroidImportance,
  AndroidVisibility,
} from '@notifee/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {CommonActions, useFocusEffect} from '@react-navigation/native';
import {navigationRef} from './navigationService';
import {Message} from '../types/chat';
import {formatTime} from '../utils/TimeUtils';
import MessageService from '../services/MessageService';
import BottomUpModal from '../components/BottomUpModal/BottomUpModal';
import {useDriver} from '../hooks/useDriver';
import RideStatusCheckModal from '../screens/RideStatusCheckModal';
import {
  playRideSound,
  stopRideSound,
  playAbortedSound,
  playCompletedSound,
  playCancelSound,
} from '../utils/Sound';

const Stack = createStackNavigator();

const AppRouter = () => {
  const {showLoader, hideLoader} = useLoader();
  const {
    setShowRideModal,
    setTripDetails,
    tripDetails,
    tripId,
    showRideModal,
    showRideStatusModal,
    setShowRideStatusModal,
    setTripId,
    setRideExpiryTime,
    setMessages,
  } = useRideDetails();
  const {forceUpdate, setForceUpdate, isSystemPaused, fetchDriver} =
    useDriver();
  const {showToast} = useToast();
  const {t} = useTranslation();
  const [isConnected, setIsConnected] = useState(true);
  const [onlineStatus, setOnlineStatus] = useState('');
  const [wasOffline, setWasOffline] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [initialRoute] = useState<string>('SplashScreen');
  const {FloatingWindow} = NativeModules;
  const [tripStatus, setTripStatus] = useState<string | null>(null);
  const lastAppStateChange = useRef<number>(0);

  useEffect(() => {
    const hideFloatingWindow = async () => {
      if (Platform.OS === 'android') {
        try {
          await FloatingWindow.hide();
        } catch (error) {
          console.error('Failed to hide floating window on mount:', error);
        }
      }
    };

    hideFloatingWindow();
  }, []);

  useEffect(() => {
    const handleAppStateChange = async (nextAppState: AppStateStatus) => {
      const now = Date.now();
      // Only allow execution if 100ms have passed since last execution
      if (now - lastAppStateChange.current < 100) {
        return;
      }
      lastAppStateChange.current = now;
      const active = await AsyncStorage.getItem('active');
      if (
        active &&
        JSON.parse(active) &&
        Platform.OS === 'android' &&
        nextAppState === 'background'
      ) {
        try {
          await FloatingWindow.show();
        } catch (error) {
          console.error('Failed to show floating window:', error);
        }
      } else if (nextAppState === 'active') {
        try {
          await FloatingWindow.hide();
        } catch (error) {
          console.error('Failed to hide floating window:', error);
        }
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      subscription.remove();
    };
  }, []);

  //fetch messages
  // const fetchMessages = async () => {
  //   if (!tripDetails?.id) return;
  //   try {
  //     const response = await MessageService.getMessages(
  //       Number(tripDetails?.id),
  //     );
  //     console.log(response.data.data, 'response for getting messages');

  //     if (response.status === 200) {
  //       const formattedMessages = response.data.data.map((msg: any) => ({
  //         id: msg.id,
  //         text: msg.content,
  //         sender: msg.userId !== null,
  //         time: msg.timestamp
  //           ? formatTime(new Date(msg.timestamp))
  //           : 'Unknown Time',
  //       }));

  //       formattedMessages.sort(
  //         (
  //           a: {time: string | number | Date},
  //           b: {time: string | number | Date},
  //         ) => new Date(a.time).getTime() - new Date(b.time).getTime(),
  //       );

  //       setMessages(formattedMessages);
  //     }
  //   } catch (error) {
  //     console.error('Error fetching messages:', error);
  //   }
  // };

  const navigateToHome = async () => {
    await clearLocalStorage();
    navigationRef.current?.reset({
      index: 0,
      routes: [{name: 'BottomTab'}],
    });
  };

  const handleUserCanceled = async () => {
    stopAllSounds();
    playCancelSound();
    setTripDetails(null);
    setTripId('');
    clearLocalStorage();
    await navigateToHome();
    showToast({
      title: t('ride_canceled'),
      message: t('ride_canceled_message'),
      type: 'failure',
      duration: 10000,
      showCloseButton: true,
      onClose: navigateToHome,
    });
  };

  const handleRideAborted = async () => {
    stopAllSounds();
    playAbortedSound();
    setTripDetails(null);
    setTripId('');
    clearLocalStorage();
    await navigateToHome();
    showToast({
      title: t('ride_aborted'),
      message: t('ride_aborted_message'),
      type: 'failure',
      duration: 10000,
      showCloseButton: true,
      onClose: navigateToHome,
    });
  };

  useEffect(() => {
    const subscription = AppState.addEventListener(
      'change',
      async nextAppState => {
        const storedTripId = await AsyncStorage.getItem('tripId');
        const showModal = await AsyncStorage.getItem('showRideModal');
        const timeout = await AsyncStorage.getItem('userCanceled');
        const canceled = await AsyncStorage.getItem('userCanceled');
        const newMessage = await AsyncStorage.getItem('newMessage');
        const rideAborted = await AsyncStorage.getItem('showRideAborted');
        const rideStatus = await AsyncStorage.getItem('rideStatus');

        const showRideStatusModal = await AsyncStorage.getItem(
          'showRideStatusCheck',
        );
        const rideStatusModal =
          showRideStatusModal && JSON.parse(showRideStatusModal);
        rideStatusModal &&
          setShowRideStatusModal(JSON.parse(showRideStatusModal));
        if (canceled) {
          timeout && showToast(t('ride_timeout'), 'failure');
          handleUserCanceled();
          await notifee.cancelAllNotifications();
        }
        if (rideAborted) {
          handleRideAborted();
        }

        showModal && setShowRideModal(JSON.parse(showModal));
        if (storedTripId) {
          try {
            const sentTime = await AsyncStorage.getItem('sentTime');
            const sentTimeNumber = Number(sentTime);
            if (!isNaN(sentTimeNumber)) {
              const ttl = 20 * 1000;
              const ExpiryTime = sentTimeNumber + ttl;
              const remainingTime = ExpiryTime - Date.now();
              setRideExpiryTime(Math.round(remainingTime / 1000));
            }
            const response = await TripService.getTrip(
              JSON.parse(storedTripId),
            );
            if (response.status === STATUS_CODE.ok) {
              setTripDetails(response.data.data);
            }
            // if (newMessage) {
            //   fetchMessages();
            // }
          } catch (err: any) {
            const status = err?.response?.status;
            if (
              [STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)
            ) {
              return;
            }
          }
        }
      },
    );
    return () => {
      subscription.remove();
    };
  }, []);

  const fetchTripDetails = async () => {
    if (tripId) {
      try {
        showLoader();
        const sentTime = await AsyncStorage.getItem('sentTime');
        const sentTimeNumber = Number(sentTime);
        if (!isNaN(sentTimeNumber)) {
          const ttl = 20 * 1000;
          const ExpiryTime = sentTimeNumber + ttl;
          const remainingTime = ExpiryTime - Date.now();
          setRideExpiryTime(Math.round(remainingTime / 1000));
        }

        const response = await TripService.getTrip(JSON.parse(tripId));
        if (response.status === STATUS_CODE.ok) {
          setTripDetails(response.data.data);
          console.log('tripdetails', response.data.data);
        }
      } catch (err: any) {
        const status = err?.response?.status;
        if (
          [STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)
        ) {
          return;
        }
      } finally {
        hideLoader();
      }
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchTripDetails();
    }, [tripId]),
  );

  const handleUpdateClick = () => {
    const androidUrl =
      'https://play.google.com/store/apps/details?id=com.mapto.driver&pli=1';
    const iosUrl =
      'https://play.google.com/store/apps/details?id=com.mapto.driver&pli=1';
    const url = Platform.OS === 'android' ? androidUrl : iosUrl;
    Linking.openURL(url).catch(err => console.error('Error opening URL:', err));
  };

  const handleTripStatus = async () => {
    if (tripStatus == 'accepted') {
      await AsyncStorage.setItem('accepted', 'ONPICKUP');
      navigationRef.current?.reset({routes: [{name: 'PickupSpot'}]});
    } else if (tripStatus == 'verified') {
      await AsyncStorage.setItem('rideStatus', 'ONRIDE');
      navigationRef.current?.reset({routes: [{name: 'RideRoute'}]});
    } else if (tripStatus == 'completed') {
      navigationRef.current?.reset({routes: [{name: 'CompleteRide'}]});
    } else if (tripStatus == 'aborted') {
      navigationRef.current?.reset({routes: [{name: 'BottomTab'}]});
    } else if (tripStatus == 'user_canceled') {
      await AsyncStorage.removeItem('rideStatus');
      navigationRef.current?.reset({routes: [{name: 'BottomTab'}]});
    }
  };

  useEffect(() => {
    const checkAndUpdateTripStatus = async () => {
      const tripId = await AsyncStorage.getItem('tripId');
      if (tripId) {
        await handleTripStatus();
      }
    };

    checkAndUpdateTripStatus();
  }, []);

  useEffect(() => {
    const unsubscribeNetInfo = NetInfo.addEventListener(async state => {
      const isNowConnected = !!state.isConnected;
      setIsConnected(isNowConnected);

      if (isNowConnected && wasOffline) {
        setOnlineStatus(t('back_online'));
        setWasOffline(false);
        setTimeout(() => setOnlineStatus(''), 5000);
        await fetchTripDetails();
      } else if (!isNowConnected) {
        setOnlineStatus('');
        setWasOffline(true);
      }
    });

    return () => unsubscribeNetInfo();
  }, [wasOffline, t]);

  const clearLocalStorage = async () => {
    setShowRideModal(false);
    setTripDetails(null);
    setTripId('');
    setShowRideModal(false);
    setRideExpiryTime(0);
    setShowRideStatusModal(false);
    await AsyncStorage.removeItem('showRideModal');
    await AsyncStorage.removeItem('tripId');
    await AsyncStorage.removeItem('rideStatus');
    await AsyncStorage.removeItem('userCanceled');
    await AsyncStorage.removeItem('timeout');
    await AsyncStorage.removeItem('sentTime');
    await AsyncStorage.removeItem('showRideStatusCheck');
    await AsyncStorage.removeItem('showRideAborted');
    await AsyncStorage.removeItem('sentTime');

    await notifee.cancelAllNotifications();

    const noti = await AsyncStorage.getItem('notificationId');
    noti && (await notifee.cancelDisplayedNotification(noti));
  };

  // Firebase notifications listener
  useEffect(() => {
    const unsubscribeMessaging = messaging().onMessage(async remoteMessage => {
      if (remoteMessage) {
        const notificationId = remoteMessage.messageId;
        const event = remoteMessage?.data?.event;

        if (remoteMessage.data && remoteMessage.data.content) {
          const newMessage = {
            id: Number(new Date()),
            text:
              typeof remoteMessage.data.content === 'string'
                ? remoteMessage.data.content
                : JSON.stringify(remoteMessage.data.content),
            sender: false,
            time: new Date().toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit',
            }),
          };

          setMessages(prevMessages => [...prevMessages, newMessage as Message]);
        }

        if (event === RideEvents.RIDE_ACCEPTED) {
          showToast(t('driver_accept'), 'success');
          const noti = await AsyncStorage.getItem('notificationId');
          noti && (await notifee.cancelDisplayedNotification(noti));
          await AsyncStorage.setItem('showRideModal', JSON.stringify(false));
          await AsyncStorage.setItem('rideStatus', 'ONPICKUP');
          setShowRideModal(false);
          stopRideSound();
          navigationRef.current?.dispatch(
            CommonActions.reset({
              routes: [{name: 'PickupSpot'}],
            }),
          );
        } else if (event === RideEvents.RIDE_TIMEOUT) {
          showToast(t('ride_timeout'), 'failure');
          stopRideSound();
          await clearLocalStorage();
          const noti = await AsyncStorage.getItem('notificationId');
          noti && (await notifee.cancelDisplayedNotification(noti));
          await notifee.cancelAllNotifications();
        } else if (event === RideEvents.RIDE_CANCELED) {
          setTripDetails(null);
          setTripId('');
          await notifee.displayNotification({
            id: notificationId || `${Date.now()}`,
            title: 'Ride Canceled',
            body: 'User canceled the ride',
            android: {
              channelId: 'mapto-ride-canceled-channel',
              importance: AndroidImportance.HIGH,
              smallIcon: 'ic_driver',
              pressAction: {
                id: 'default',
                launchActivity: 'default',
              },
              tag: `ride_canceled_${Date.now()}`,
            },
          });
          handleUserCanceled();
        } else if (event === RideEvents.RIDE_REJECTED) {
          showToast(t('driver_decline'), 'failure');
          const noti = await AsyncStorage.getItem('notificationId');
          noti && (await notifee.cancelDisplayedNotification(noti));
          await clearLocalStorage();
        } else if (event === RideEvents.RIDE_OTP_VERIFY) {
          if (remoteMessage?.data?.is_verified === 'true') {
            showToast(t('pin_verified'), 'success');
            await AsyncStorage.removeItem('newMessage');
            await AsyncStorage.setItem('rideStatus', 'ONRIDE');
            navigationRef.current?.reset({
              index: 0,
              routes: [{name: 'RideRoute'}],
            });
          } else {
            showToast(t('pin_not_verified'), 'failure');
          }
        } else if (event === RideEvents.RIDE_CANCELED_DRIVER) {
          await clearLocalStorage();
          showToast(t('driver_cancel'), 'failure');
          navigationRef.current?.reset({
            index: 0,
            routes: [{name: 'BottomTab'}],
          });
        } else if (event === RideEvents.RIDE_COMPLETED) {
          playCompletedSound();
          await clearLocalStorage();
          showToast(t('ride_completed'), 'success');
          await AsyncStorage.setItem('rideStatus', 'COMPLETED');
          navigationRef.current?.reset({
            index: 0,
            routes: [{name: 'CompleteRide'}],
          });
        } else if (event === RideEvents.RIDE_REQUEST) {
          setTripId(remoteMessage?.data?.tripId);
          await fetchTripDetails();
          if (
            remoteMessage.data &&
            typeof remoteMessage.data.tripId === 'string'
          ) {
            const tripIdFromMessage = remoteMessage?.data?.tripId;
            if (typeof tripIdFromMessage === 'string') {
              await AsyncStorage.setItem('tripId', tripIdFromMessage);
            }
            await AsyncStorage.setItem('showRideModal', JSON.stringify(true));
            if (typeof remoteMessage?.data?.sentTime === 'string') {
              await AsyncStorage.setItem(
                'sentTime',
                remoteMessage.data.sentTime,
              );
            }
          }
          playRideSound();
          setShowRideModal(true);
        } else if (event === RideEvents.RIDE_STATUS_CHECK) {
          playRideStatusSound();
          await AsyncStorage.setItem(
            'showRideStatusCheck',
            JSON.stringify(true),
          );
          setShowRideStatusModal(true);
        } else if (event === RideEvents.RIDE_ABORTED) {
          handleRideAborted();
        }
      }
      if (remoteMessage.notification) {
        await notifee.displayNotification({
          id: remoteMessage?.messageId || `${Date.now()}`,
          title: remoteMessage.notification.title,
          body: remoteMessage.notification.body,
          android: {
            channelId: 'mapto-driver-channel',
            importance: AndroidImportance.HIGH,
            smallIcon: 'ic_driver',
            pressAction: {
              id: 'default',
              launchActivity: 'default',
            },
            tag: `notification_${remoteMessage?.messageId || Date.now()}`,
          },
        });
      }
    });

    // messaging().onNotificationOpenedApp(remoteMessage => {
    //   console.log('Notification opened from background:', remoteMessage);
    //   setShowRideModal(true);
    //   setTripId(remoteMessage?.data?.tripId || null);
    // });

    // messaging()
    //   .getInitialNotification()
    //   .then(remoteMessage => {
    //     if (remoteMessage) {
    //       console.log('Notification opened from quit state:', remoteMessage);
    //       setShowRideModal(true);
    //       setTripId(remoteMessage?.data?.tripId || null);
    //     }
    //   });

    return () => unsubscribeMessaging();
  }, []);

  // Handle manual refresh
  const handleRefresh = async () => {
    showLoader();
    setRetryCount(0);

    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      const state = await NetInfo.fetch();
      if (state.isConnected) {
        setIsConnected(true);
        hideLoader();
        navigationRef.current?.reset({
          index: 0,
          routes: [{name: 'NavigationGateway'}],
        });
        return;
      }
      setRetryCount(attempt);
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
    }
    setIsConnected(false);
    hideLoader();
  };

  useEffect(() => {
    const initialCheck = async () => {
      await fetchDriver();
    };

    initialCheck();

    const handleAppStateChange = async (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        await fetchDriver();
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      subscription.remove();
    };
  }, []);

  const handleRetrySystemPause = async () => {
    await fetchDriver();
  };

  const handleRewardNotification = (event: string) => {
    let title = '';
    let message = '';

    switch (event) {
      case 'ReferralRewardProcessed':
        title = t('referral_bonus');
        message = t('referral_bonus_message');
        break;
      case 'CommissionProcessed':
        title = t('payout_completed');
        message = t('commission_processed_message');
        break;
      case 'TripleTreatRewardProcessed':
        title = t('triple_treat_reward');
        message = t('triple_treat_reward_message');
        break;
      case 'WelcomeRewardProcessed':
        title = t('welcome_bonus');
        message = t('welcome_bonus_message');
        break;
    }
  };

  useEffect(() => {
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      console.log('Foreground message received in AppRouter:', remoteMessage);
      const event = remoteMessage?.data?.event as string | undefined;

      // if (remoteMessage.data && remoteMessage.data.content) {
      //   const messageContent =
      //     typeof remoteMessage.data.content === 'string'
      //       ? remoteMessage.data.content
      //       : JSON.stringify(remoteMessage.data.content);

      //   const senderType = remoteMessage.data.sender_type || 'User';
      //   const notificationTitle = senderType === 'Driver' ? 'User' : senderType;

      //   playChatSound();

      //   await notifee.displayNotification({
      //     id: remoteMessage?.messageId || `${Date.now()}`,
      //     title: notificationTitle,
      //     body: messageContent,
      //     android: {
      //       channelId: 'mapto-user-chat-channel' as string,
      //       importance: AndroidImportance.HIGH,
      //       smallIcon: 'ic_driver',
      //       sound: 'chat',
      //       pressAction: {
      //         id: 'default',
      //         launchActivity: 'default',
      //       },
      //       tag: `message_${Date.now()}`,
      //       showTimestamp: true,
      //     },
      //   });

      //   await AsyncStorage.setItem('newMessage', 'true');

      //   const newMessage = {
      //     id: Number(new Date()),
      //     text: messageContent,
      //     sender: false,
      //     time: new Date().toLocaleTimeString([], {
      //       hour: '2-digit',
      //       minute: '2-digit',
      //     }),
      //   };

      //   setMessages(prevMessages => [...prevMessages, newMessage as Message]);
      // } else

      if (
        event &&
        [
          'ReferralRewardProcessed',
          'CommissionProcessed',
          'TripleTreatRewardProcessed',
          'WelcomeRewardProcessed',
        ].includes(event)
      ) {
        // Customize notification based on event type
        let notificationTitle: string = 'Reward Notification';
        let notificationBody: string = 'You have received a reward!';

        if (event === 'ReferralRewardProcessed') {
          notificationTitle = 'Referral Bonus Unlocked!';
          notificationBody =
            'You have just earned a reward for referring a friend — enjoy the perks!';
        } else if (event === 'CommissionProcessed') {
          notificationTitle = 'Payout Completed';
          notificationBody =
            'Your commission for the latest trip has been processed.';
        } else if (event === 'WelcomeRewardProcessed') {
          notificationTitle = 'Welcome Bonus!';
          notificationBody =
            'Your Welcome Bonus is here — thanks for riding with us!';
        }

        // Display notification even in foreground
        await notifee.displayNotification({
          id: remoteMessage?.messageId || `${Date.now()}`,
          title: notificationTitle,
          body: notificationBody,
          android: {
            channelId: 'mapto-driver-channel',
            importance: AndroidImportance.HIGH,
            smallIcon: 'ic_driver',
            pressAction: {
              id: 'default',
              launchActivity: 'default',
            },
            tag: `reward_${event}_${Date.now()}`,
          },
        });

        handleRewardNotification(event);
      }
    });

    return () => {
      unsubscribe();
    };
  }, []);

  return (
    <>
      {showRideModal && <RideModal />}
      {showRideStatusModal && <RideStatusCheckModal />}
      <Stack.Navigator
        initialRouteName={initialRoute}
        screenOptions={{
          cardStyleInterpolator: ({current, layouts}) => ({
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [layouts.screen.width, 0],
                  }),
                },
              ],
            },
          }),
          headerShown: false,
        }}>
        {!isConnected ? (
          <Stack.Screen
            name="Offline"
            options={{headerShown: false}}
            children={() => (
              <OfflineScreen
                handleRefresh={handleRefresh}
                retryCount={retryCount}
              />
            )}
          />
        ) : (
          appRoutes.map(item => (
            <Stack.Screen
              key={item.name}
              name={item.name}
              component={item.component}
              options={{
                headerShown: false,
                gestureEnabled: true,
              }}
            />
          ))
        )}
        <Stack.Screen
          name="Fallback"
          options={{headerShown: false}}
          component={FallBack}
        />
      </Stack.Navigator>

      {onlineStatus && (
        <View style={styles.statusContainer}>
          <Text style={styles.statusText}>{onlineStatus}</Text>
        </View>
      )}

      {forceUpdate && (
        <BottomUpModal
          showModal={forceUpdate}
          onClose={() => setForceUpdate(false)}
          title={t('Updates Available!')}
          description={t('Please update to continue using the app')}
          buttonText={t('Update')}
          onButtonClick={() => {
            setForceUpdate(false);
            handleUpdateClick();
          }}
          forceUpdate={true}
        />
      )}

      {isSystemPaused && (
        <BottomUpModal
          showModal={isSystemPaused}
          onClose={() => {}}
          title={t('system_maintenance')}
          description={t('system_paused_message')}
          buttonText={t('retry')}
          onButtonClick={handleRetrySystemPause}
          forceUpdate={true}
        />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  statusContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    alignItems: 'center',
    padding: 10,
    backgroundColor: colors.green,
  },
  statusText: {
    color: colors.white,
    textAlign: 'center',
    fontFamily: GeistFont.regular,
    fontSize: sizes.h6,
    marginVertical: spacing.xl,
  },
});

export default AppRouter;
