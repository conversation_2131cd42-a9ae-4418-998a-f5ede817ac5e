import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import Maps from '../screens/Maps';
import MyRide from '../screens/MyRide';
import MyEarnings from '../screens/MyEarnings';
import MyProfile from '../screens/MyProfile';
import home from '../icons/home.svg';
import homeActive from '../icons/home_active.svg';
import ride from '../icons/rides.svg';
import rideActive from '../icons/ride_active.svg';
import profile from '../icons/profile.svg';
import profileActive from '../icons/profile_active.svg';
import earnings from '../icons/earnings.svg';
import earningsActive from '../icons/earnings_active.svg';
import IconSvgView from '../components/IconSvgView/IconSvgView';
import {Text, View} from 'react-native';
import {colors, sizes} from '../constants';
import {useTranslation} from 'react-i18next';
import {spacing} from '../constants/theme';

const Tab = createBottomTabNavigator();

function BottomTab() {
  const {t} = useTranslation();

  return (
    <>
      <Tab.Navigator
        initialRouteName="Home"
        screenOptions={({route}) => ({
          tabBarIcon: ({color, focused}) => {
            let icon;
            if (route.name === t('home')) {
              icon = (
                <IconSvgView
                  source={focused ? homeActive : home}
                  color={color}
                  size={30}
                />
              );
            } else if (route.name === t('my_rides')) {
              icon = (
                <IconSvgView
                  source={focused ? rideActive : ride}
                  color={color}
                  size={25}
                />
              );
            } else if (route.name === t('my_earnings')) {
              icon = (
                <IconSvgView
                  source={focused ? earningsActive : earnings}
                  color={color}
                  size={25}
                />
              );
            } else if (route.name === t('profile')) {
              icon = (
                <IconSvgView
                  source={focused ? profileActive : profile}
                  color={color}
                  size={25}
                />
              );
            }

            return <View style={{marginTop: spacing.md}}>{icon}</View>;
          },
          tabBarLabel: ({color}) => {
            const labelStyle = {
              fontSize: sizes.body / 1.1,
              color: color,
              textAlign: 'center',
              padding: spacing.xs || 4,
              fontFamily: 'Geist-Regular',
            };
          
            return (
              <View style={{flex: 1, alignItems: 'center'}}>
                <Text
                  numberOfLines={2}
                  adjustsFontSizeToFit
                  minimumFontScale={0.8}
                  style={labelStyle}>
                  {t(route.name)}
                </Text>
              </View>
            );
          },
          
          tabBarActiveTintColor: colors.white,
          tabBarInactiveTintColor: colors.grey,
          tabBarStyle: {
            backgroundColor: colors.darkGrey,
            borderTopColor: colors.davyGrey,
            justifyContent: 'center',
            height: 75,
          },
          headerShown: false,
        })}>
        <Tab.Screen name={t('home')} component={Maps} />
        <Tab.Screen name={t('my_rides')} component={MyRide} />
        <Tab.Screen name={t('my_earnings')} component={MyEarnings} />
        <Tab.Screen name={t('profile')} component={MyProfile} />
      </Tab.Navigator>
    </>
  );
}

export default BottomTab;
