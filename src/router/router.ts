import Start from '../screens/Start';
import UserSetup from '../screens/UserSetup';
import Vehicle from '../screens/Vehicle';
import Welcome from '../screens/Welcome';
import Dl from '../screens/Dl';
import DlNumber from '../screens/DlNumber';
import Document from '../screens/Documents';
import Language from '../screens/MyProfile/Language';
import ProfileDocuments from '../screens/MyProfile/ProfileDocuments';
import Phone from '../screens/Phone';
import PickupSpot from '../screens/PickupSpot';
import Pp from '../screens/Pp';
import Rc from '../screens/Rc';
import RidePin from '../screens/RidePin';
import RideRoute from '../screens/RideRoute';
import CompleteRide from '../screens/CompleteRide';
import Otp from '../screens/Otp';
import Rating from '../screens/Rating';
import IDCard from '../screens/MyProfile/IDCard';
import Performance from '../screens/MyProfile/Performance';
import VerifiedRc from '../screens/MyProfile/ProfileDocuments/VerifiedRc';
import VerifiedDl from '../screens/MyProfile/ProfileDocuments/VerifiedDl';
import {FC} from 'react';
import BottomTab from './TabNavigator';
import AddQr from '../screens/CompleteRide/AddQr';
import ProfilePicture from '../screens/MyProfile/ProfileDocuments/ProfilePicture';
import SplashScreen from '../screens/SplashScreen';
import LocationPermission from '../screens/Permissions/LocationPermission';
import NotificationPermission from '../screens/Permissions/NotificationPermission';
import OverlayPermission from '../screens/Permissions/OverlayPermission';
import PermissionList from '../screens/Permissions/PermissionList';
import BatteryPermission from '../screens/Permissions/BatteryPermission';
import NavigationGateway from './NavigationGateway';
import Insurance from '../screens/Insurance';
import VerifiedInsurance from '../screens/MyProfile/ProfileDocuments/VerifiedInsurance';
import Wallet from '../screens/MyProfile/Wallet';
import AllTransactions from '../screens/MyProfile/Wallet/AllTransactions';
import Promotions from '../screens/MyProfile/Wallet/Promotions';

const screenNames = {
  splashScreen: 'SplashScreen',
  navigationGateway: 'NavigationGateway',

  startScreen: 'Start',
  phoneScreen: 'Phone',
  otpScreen: 'Otp',
  userSetupScreen: 'UserSetup',

  vehicleScreen: 'Vehicle',
  documentScreen: 'Document',
  RcScreen: 'Rc',
  DlNumberScreen: 'DlNumber',
  DlScreen: 'Dl',
  InsuranceScreen: 'Insurance',
  PpScreen: 'Pp',
  welcomeScreen: 'Welcome',

  pickupSpotScreen: 'PickupSpot',
  profileDocumentScreen: 'ProfileDocuments',
  languageScreen: 'Language',
  ridePinScreen: 'RidePin',
  verificationPinScreen: 'VerificationPin',
  rideRouteScreen: 'RideRoute',
  completeRideScreen: 'CompleteRide',
  ratingScreen: 'Rating',
  idcardScreen: 'IDCard',
  performanceScreen: 'Performance',
  verifiedRcScreen: 'VerifiedRc',
  verifiedDlScreen: 'VerifiedDl',
  verifiedInsuranceScreen: 'VerifiedInsurance',
  tabScreen: 'BottomTab',
  addQrScreen: 'AddQr',
  profilePictureScreen: 'ProfilePicture',
  locationPermissionScreen: 'LocationPermission',
  notificationPermissionScreen: 'NotificationPermission',
  overlayPermissionScreen: 'OverlayPermission',
  permissionListScreen: 'PermissionList',
  batteryPermissionScreen: 'BatteryPermission',
  foregroundPermissionScreen: 'ForegroundPermission',
  walletScreen: 'Wallet',
  allTransactionsScreen: 'AllTransactions',
  promotionScreen: 'Promotions',
};

const appRoutes: {name: string; component: FC<any>}[] = [
  {name: screenNames.splashScreen, component: SplashScreen},
  {name: screenNames.navigationGateway, component: NavigationGateway},

  {name: screenNames.startScreen, component: Start},
  {name: screenNames.phoneScreen, component: Phone},
  {name: screenNames.otpScreen, component: Otp},
  {name: screenNames.userSetupScreen, component: UserSetup},
  {name: screenNames.vehicleScreen, component: Vehicle},
  {name: screenNames.documentScreen, component: Document},
  {name: screenNames.RcScreen, component: Rc},
  {name: screenNames.DlNumberScreen, component: DlNumber},
  {name: screenNames.DlScreen, component: Dl},
  {name: screenNames.InsuranceScreen, component: Insurance},
  {name: screenNames.PpScreen, component: Pp},
  {name: screenNames.welcomeScreen, component: Welcome},

  {name: screenNames.pickupSpotScreen, component: PickupSpot},
  {name: screenNames.ridePinScreen, component: RidePin},
  {name: screenNames.verificationPinScreen, component: RidePin},
  {name: screenNames.rideRouteScreen, component: RideRoute},
  {name: screenNames.completeRideScreen, component: CompleteRide},
  {name: screenNames.addQrScreen, component: AddQr},

  {name: screenNames.idcardScreen, component: IDCard},
  {name: screenNames.walletScreen, component: Wallet},
  {name: screenNames.performanceScreen, component: Performance},
  {name: screenNames.profileDocumentScreen, component: ProfileDocuments},
  {name: screenNames.languageScreen, component: Language},
  {name: screenNames.ratingScreen, component: Rating},
  {name: screenNames.verifiedRcScreen, component: VerifiedRc},
  {name: screenNames.verifiedInsuranceScreen, component: VerifiedInsurance},
  {name: screenNames.verifiedDlScreen, component: VerifiedDl},
  {name: screenNames.profilePictureScreen, component: ProfilePicture},
  {name: screenNames.allTransactionsScreen, component: AllTransactions},
  {name: screenNames.promotionScreen, component: Promotions},

  {name: screenNames.permissionListScreen, component: PermissionList},
  {name: screenNames.locationPermissionScreen, component: LocationPermission},
  {name: screenNames.batteryPermissionScreen, component: BatteryPermission},
  {
    name: screenNames.notificationPermissionScreen,
    component: NotificationPermission,
  },
  {
    name: screenNames.overlayPermissionScreen,
    component: OverlayPermission,
  },

  {name: screenNames.tabScreen, component: BottomTab},
];

export {appRoutes, screenNames};
