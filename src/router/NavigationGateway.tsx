import React, {useCallback, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {useDriver} from '../hooks/useDriver';
import {useAuth} from '../hooks/useAuth';
import {Driver} from '../types';
import {
  Image,
  ImageBackground,
  SafeAreaView,
  StyleSheet,
  View,
} from 'react-native';
import {images} from '../constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useRideDetails} from '../hooks/useRideDetailsContext';
import {spacing} from '../constants/theme';
import {STATUS_CODE} from '../constants/constants';
import TripService from '../services/TripService';
import {useFocusEffect} from '@react-navigation/native';

interface NavigationGatewayProps {
  navigation?: any;
}

const NavigationGateway: React.FC<NavigationGatewayProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {fetchDriver, setIsVerified} = useDriver();
  const {isAuthenticated, checkAuthentication} = useAuth();
  const [loading, setLoading] = useState<boolean>(true);
  const {setTripDetails} = useRideDetails();

  const checkTripStatus = async () => {
    const tripId = await AsyncStorage.getItem('tripId');
    if (tripId) {
      (async () => {
        if (tripId) {
          try {
            const response = await TripService.getTrip(JSON.parse(tripId));
            if (response.status === STATUS_CODE.ok) {
              setTripDetails(response.data.data);
            }
          } catch (err: any) {
            const status = err?.response?.status;
            if (
              [STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)
            ) {
              return;
            }
          }
        }
      })();
      const status = await AsyncStorage.getItem('rideStatus');
      return status;
    }
    if (!tripId) {
      clearLocalStorage();
    }
    return null;
  };

  const clearLocalStorage = async () => {
    setTripDetails(null);
    await AsyncStorage.removeItem('showRideModal');
    await AsyncStorage.removeItem('tripId');
    await AsyncStorage.removeItem('rideStatus');
    await AsyncStorage.removeItem('userCanceled');
    await AsyncStorage.removeItem('timeout');
    await AsyncStorage.removeItem('sentTime');
    await AsyncStorage.removeItem('showRideStatusCheck');
    await AsyncStorage.removeItem('showRideAborted');
  };

  useFocusEffect(
    useCallback(() => {
      const initialize = async () => {
        setLoading(true);
        const status = await checkTripStatus();

        try {
          const isAuthenticated = await checkAuthentication();
          if (!isAuthenticated) {
            navigation.reset({index: 0, routes: [{name: 'Start'}]});
            return;
          }

          const driver = await fetchDriver();
          if (driver?.isVerified) {
            setIsVerified(true);
          }

          handleTripNavigation(status, driver);
        } catch (error) {
          console.error('Initialization error:', error);
        } finally {
          setLoading(false);
        }
      };

      initialize();

      return () => {
        console.log('Screen unfocused, cleanup here if necessary');
      };
    }, []),
  );

  const handleTripNavigation = (
    tripStatus: string | null,
    driver: Driver | undefined,
  ) => {
    if (driver) {
      if (tripStatus) {
        switch (tripStatus) {
          case 'ONPICKUP':
            navigation.reset({index: 0, routes: [{name: 'PickupSpot'}]});
            break;
          case 'ONRIDE':
            navigation.reset({index: 0, routes: [{name: 'RideRoute'}]});
            break;
          case 'ONRIDEPIN':
            navigation.reset({index: 0, routes: [{name: 'RidePin'}]});
            break;
          case 'COMPLETED':
            navigation.reset({index: 0, routes: [{name: 'CompleteRide'}]});
            break;
        }
      } else {
        handleDriverSetupNavigation(driver);
      }
    }
  };

  const handleDriverSetupNavigation = (driver: Driver) => {
    console.log('Driver setup navigation:', driver);

    if (
      !driver.name ||
      !driver.gender ||
      !driver.location ||
      !driver.preferred_language
    ) {
      navigation.reset({index: 0, routes: [{name: 'UserSetup'}]});
    } else if (!driver.vehicles || driver.vehicles.length === 0) {
      navigation.replace('Vehicle');
    } else if (!driver.registration_certificate) {
      navigation.replace('Document');
    } else if (!driver.driving_license_no && !driver.dob) {
      navigation.replace('DlNumber');
    } else if (!driver.driving_license) {
      navigation.replace('Dl');
    } else if (!driver.profile_photo) {
      navigation.replace('Pp');
    } else if (!driver.isVerified) {
      navigation.reset({index: 0, routes: [{name: 'Welcome'}]});
    } else if (driver.isVerified) {
      navigation.reset({index: 0, routes: [{name: 'BottomTab'}]});
    }
  };

  return (
    <ImageBackground source={images.map} style={styles.background}>
      <SafeAreaView style={styles.safearea}>
        <View style={styles.loaderContainer}>
          <Image
            source={require('../icons/LOADER.gif')}
            style={styles.appGif}
          />
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  background: {
    flex: 1,
    resizeMode: 'cover',
  },
  safearea: {
    flex: 1,
    margin: spacing.xl,
  },
  appGif: {
    width: 50,
    height: 50,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default NavigationGateway;
