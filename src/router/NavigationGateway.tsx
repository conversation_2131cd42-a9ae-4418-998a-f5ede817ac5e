import React, {useEffect, useState, useRef} from 'react';
import {useTranslation} from 'react-i18next';
import {useDriver} from '../hooks/useDriver';
import {useAuth} from '../hooks/useAuth';
import {Driver} from '../types';
import {
  Image,
  ImageBackground,
  SafeAreaView,
  StyleSheet,
  View,
} from 'react-native';
import {images} from '../constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useRideDetails} from '../hooks/useRideDetailsContext';
import {spacing} from '../constants/theme';
import {STATUS_CODE} from '../constants/constants';
import TripService from '../services/TripService';

interface NavigationGatewayProps {
  navigation?: any;
}

const NavigationGateway: React.FC<NavigationGatewayProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {fetchDriver, setIsVerified} = useDriver();
  const {isAuthenticated, checkAuthentication} = useAuth();
  const [loading, setLoading] = useState<boolean>(true);
  const {setTripDetails} = useRideDetails();
  const initializationInProgress = useRef<boolean>(false);
  const hasInitialized = useRef<boolean>(false);

  const checkTripStatus = async () => {
    try {
      const response = await TripService.getActiveTrip();

      if (response.status === STATUS_CODE.ok) {
        const activeTrip = response.data.data.activeRide;
        console.log('[NavigationGateway] Active trip response:', activeTrip);

        if (!activeTrip) {
          console.log('[NavigationGateway] No active trip, checking last trip');
          const lastTripResponse = await TripService.getLastTrip();
          const status = await AsyncStorage.getItem('rideStatus');
          if (status === 'COMPLETED') {
            navigation.reset({
              index: 0,
              routes: [{name: 'CompleteRide'}],
            });
            return status;
          }
          if (lastTripResponse.status === STATUS_CODE.ok) {
            const lastTrip = lastTripResponse.data.data.lastTrip;

            if (lastTrip?.status === 'COMPLETED') {
              console.log('[NavigationGateway] Found completed trip');
              await AsyncStorage.setItem('tripId', lastTrip.id.toString());
              await AsyncStorage.setItem(
                'rideStatus',
                lastTrip.status.toUpperCase(),
              );
              setTripDetails(lastTrip);
              return lastTrip.status;
            }
          }
          await AsyncStorage.multiRemove(['tripId', 'rideStatus']);
          return null;
        }

        console.log(
          '[NavigationGateway] Active trip found:',
          activeTrip.status,
        );
        await AsyncStorage.setItem('tripId', activeTrip.id.toString());
        await AsyncStorage.setItem(
          'rideStatus',
          activeTrip.status.toUpperCase(),
        );
        setTripDetails(activeTrip);
        return activeTrip.status;
      }
      return null;
    } catch (err: any) {
      if (err?.response?.data?.response?.code === 'no_active_trip') {
        console.log('[NavigationGateway] No active trip found');
        await AsyncStorage.multiRemove(['tripId', 'rideStatus']);
      } else {
        console.error('Error checking trip status:', err);
      }
      return null;
    }
  };

  const determineRoute = (
    tripStatus: string | null,
    driver: Driver | undefined,
  ): string => {
    // Handle unauthenticated case
    if (!driver) {
      return 'Start';
    }

    // Handle incomplete profile cases
    if (
      !driver.name ||
      !driver.gender ||
      !driver.location ||
      !driver.preferred_language
    ) {
      return 'UserSetup';
    }
    if (!driver.vehicles || driver.vehicles.length === 0) {
      return 'Vehicle';
    }
    if (!driver.registration_certificate) {
      return 'Document';
    }
    if (!driver.driving_license_no && !driver.dob) {
      return 'DlNumber';
    }
    if (!driver.driving_license) {
      return 'Dl';
    }
    if (!driver.profile_photo) {
      return 'Pp';
    }
    if (!driver.isVerified) {
      return 'Welcome';
    }

    // If profile is complete, handle based on trip status
    if (tripStatus) {
      const navigationRoutes: {[key: string]: string} = {
        ACCEPTED: 'PickupSpot',
        VERIFIED: 'RideRoute',
        ONPICKUP: 'PickupSpot',
        ONRIDE: 'RideRoute',
        ONRIDEPIN: 'RidePin',
        COMPLETED: 'CompleteRide',
        ABORTED: 'BottomTab',
        DRIVER_CANCELLED: 'BottomTab',
        USER_CANCELLED: 'BottomTab',
      };
      return navigationRoutes[tripStatus.toUpperCase()] || 'BottomTab';
    }

    // Default case: verified driver with no active trip
    return 'BottomTab';
  };

  const handleNavigation = (route: string) => {
    navigation.reset({
      index: 0,
      routes: [{name: route}],
    });
  };

  useEffect(() => {
    let isActive = true;

    const initializeAuth = async () => {
      // Prevent multiple initializations
      if (initializationInProgress.current || hasInitialized.current) {
        return;
      }

      initializationInProgress.current = true;

      try {
        setLoading(true);
        console.log('[NavigationGateway] Starting initialization');
        const isDriverAuthenticated = await checkAuthentication();

        if (!isDriverAuthenticated) {
          if (isActive) {
            console.log(
              '[NavigationGateway] Not authenticated, navigating to Start',
            );
            handleNavigation('Start');
          }
          return;
        }

        const driver = await fetchDriver();
        if (!isActive) return;

        if (driver?.isVerified) {
          setIsVerified(true);
        }

        const status = await checkTripStatus();
        if (!isActive) return;

        if (isActive) {
          const route = determineRoute(status, driver);
          console.log('[NavigationGateway] Determined route:', route);
          handleNavigation(route);
        }
      } catch (error) {
        console.error('[NavigationGateway] Initialization error:', error);
        if (isActive) {
          handleNavigation('Start');
        }
      } finally {
        if (isActive) {
          setLoading(false);
          hasInitialized.current = true;
          initializationInProgress.current = false;
        }
      }
    };

    initializeAuth();

    return () => {
      isActive = false;
    };
  }, []);

  return (
    <ImageBackground source={images.map} style={styles.background}>
      <SafeAreaView style={styles.safearea}>
        <View style={styles.loaderContainer}>
          <Image
            source={require('../icons/LOADER.gif')}
            style={styles.appGif}
          />
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  background: {
    flex: 1,
    resizeMode: 'cover',
  },
  safearea: {
    flex: 1,
    margin: spacing.xl,
  },
  appGif: {
    width: 50,
    height: 50,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default NavigationGateway;
