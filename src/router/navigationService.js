import { NavigationContainerRef } from '@react-navigation/native';
import React from 'react';

export const navigationRef = React.createRef();

export const navigate = (name, params) => {
  navigationRef.current?.navigate(name, params);
};

export const replace = (name, params) => {
  navigationRef.current?.reset({
    index: 0,
    routes: [{ name, params }],
  });
};


export const reset = (routes, index = 0) => {
  navigationRef.current?.reset({
    index,
    routes,
  });
};