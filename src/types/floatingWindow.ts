import { NativeModules, Platform } from 'react-native';

const { FloatingWindow } = NativeModules;

export interface FloatingWindowInterface {
  checkPermission(): Promise<boolean>;
  requestPermission(): Promise<void>;
  show(): Promise<void>;
  hide(): Promise<void>;
}

const unsupportedPlatform: FloatingWindowInterface = {
  checkPermission: async () => false,
  requestPermission: async () => {},
  show: async () => {},
  hide: async () => {},
};

export default Platform.select({
  android: FloatingWindow,
  default: unsupportedPlatform,
}) as FloatingWindowInterface;
