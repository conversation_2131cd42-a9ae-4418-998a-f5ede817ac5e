export type Driver = {
  id: number;
  phone: string;
  gender: string;
  profile_photo?: string;
  registration_certificate?: string;
  preferred_language?: string;
  name?: string;
  location?: string;
  driving_license_no?: string;
  license_expiry?: string;
  rc_expiry?: string;
  insurance_expiry?: string;
  insurance_number?: string;
  rc_number?: string;
  driving_license?: string;
  vehicle_insurance?: string;
  dob?: string;
  average_rating?: string;
  paymentDetails?: { upi_id: string };
  created_at?: string;
  vehicles: Vehicle[];
  isVerified: boolean;
}

export type Vehicle = {
  id: number;
  vehicle_type: string;
  vehicle_no: string;
  rc: string;
  ownership_type: string;
  fuel_type: string;  
}