export type Coordinates = {
  latitude: number;
  longitude: number;
};

export type Trip = {
  id: number;
  userId: number;
  driverId: number;
  otp: string;
  source: Coordinates;
  source_address: string;
  destination: Coordinates;
  destination_address: string;
  status: string;
  distance: string;
  vehicleType: string;
  duration: number;
  fare: number;
  tip: number | null;
  created_at: string;
  updated_at: string;
};

export type Vehicle = {
  id: number;
  driverId: number;
  vehicle_no: string;
  vehicle_name: string | null;
  vehicle_type: string;
  registration_certificate: string | null;
  extra_amenities: string | null;
  created_at: string;
  updated_at: string;
};

export type DriverDetails = {
  id: number;
  name: string;
  current_location: string | null;
  location: string;
  dob: string;
  driving_license_no: string;
  email: string | null;
  isEmailVerified: boolean;
  vehicleId: number;
  profile_photo: string;
  driving_license: string;
  registration_certificate: string;
  preferred_language: string;
  gender: string;
  phone: string;
  device_token: string | null;
  created_at: string;
  updated_at: string;
  average_rating: number;
  isDeleted: boolean;
  isVerified: boolean;
  paymentDetails: string | null;
  suspensionUntil: string | null;
  suspensionReason: string | null;
  vehicles: Vehicle[];
};

export type TripDetails = {
  message: string;
  trip: Trip;
  driverDetails: DriverDetails;
  vehicle: Vehicle;
  user: User;
};

export type User = {
  id: number;
  name: string;
  phone: string;
};

