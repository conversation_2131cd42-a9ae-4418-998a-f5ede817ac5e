import {Alert, Linking, NativeModules, Platform} from 'react-native';
import {
  check,
  openSettings,
  PERMISSIONS,
  request,
  RESULTS,
} from 'react-native-permissions';
import messaging from '@react-native-firebase/messaging';
import {navigationRef} from '../router/navigationService';

const {FloatingWindow} = NativeModules;

export const navigateToSettings = () => {
  Alert.alert(
    'Permission Required',
    'This app requires additional permissions. Please go to settings and enable them for full functionality.',
    [
      {text: 'Cancel', style: 'cancel'},
      {text: 'Go to Settings', onPress: () => Linking.openSettings()},
    ],
  );
};

const {SystemLocationModule} = NativeModules;

export const requestCallPhonePermission = async () => {
  if (Platform.OS === 'android') {
    const currentStatus = await check(PERMISSIONS.ANDROID.CALL_PHONE);

    if (currentStatus === RESULTS.GRANTED) {
      return true;
    }

    if (currentStatus === RESULTS.DENIED) {
      const result = await request(PERMISSIONS.ANDROID.CALL_PHONE);
      return result === RESULTS.GRANTED;
    }
    return false;
  }
  return true;
};

export const checkDeviceLocationServices = async () => {
  try {
    const isLocationEnabled = await SystemLocationModule.isLocationEnabled();
    if (isLocationEnabled) {
      console.log('Location services are enabled');
    } else {
      console.log('Location services are disabled');
    }
    return isLocationEnabled;
  } catch (error) {
    console.error('Error checking location services:', error);
    return false;
  }
};

export const requestLocationPermission = async () => {
  const permission = Platform.select({
    ios: PERMISSIONS.IOS.LOCATION_ALWAYS,
    android: PERMISSIONS.ANDROID.ACCESS_BACKGROUND_LOCATION,
  });

  try {
    const result = await check(permission!);

    switch (result) {
      case RESULTS.UNAVAILABLE:
        Alert.alert(
          'Background Location Unavailable',
          'This device does not support background location services.',
          [{text: 'OK'}],
        );
        return false;

      case RESULTS.DENIED:
        const requestResult = await request(permission!);
        return requestResult === RESULTS.GRANTED;

      case RESULTS.BLOCKED:
        Alert.alert(
          'Background Location Permission Required',
          'Please enable background location permissions in your device settings.',
          [
            {text: 'Cancel', style: 'cancel'},
            {text: 'Open Settings', onPress: openSettings},
          ],
        );
        return false;

      case RESULTS.GRANTED:
        return true;

      default:
        return false;
    }
  } catch (error) {
    console.error('Error requesting background location permission:', error);
    return false;
  }
};

export const requestFineLocationPermission = async () => {
  const permission = Platform.select({
    ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
    android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
  });

  try {
    const result = await check(permission!);

    switch (result) {
      case RESULTS.UNAVAILABLE:
        Alert.alert(
          'Location Service Unavailable',
          'This device does not support location services.',
          [{text: 'OK'}],
        );
        return false;

      case RESULTS.DENIED:
        const requestResult = await request(permission!);
        return requestResult === RESULTS.GRANTED;

      case RESULTS.BLOCKED:
        Alert.alert(
          'Location Permission Required',
          'Please enable location permissions in your device settings.',
          [
            {text: 'Cancel', style: 'cancel'},
            {text: 'Open Settings', onPress: openSettings},
          ],
        );
        return false;

      case RESULTS.GRANTED:
        return true;

      default:
        return false;
    }
  } catch (error) {
    console.error('Permission check error:', error);
    return false;
  }
};

export const requestOverlayPermission = async () => {
  if (Platform.OS === 'android') {
    try {
      const hasPermission = await FloatingWindow.checkPermission();
      if (!hasPermission) {
        navigationRef.current.navigate('OverlayPermission');
        // await FloatingWindow.requestPermission();
        const updatedPermission = await FloatingWindow.checkPermission();
        return updatedPermission;
      }
      return true;
    } catch (error) {
      console.error('Error requesting overlay permission:', error);
      return false;
    }
  } else {
    return true;
  }
};

export const requestNotificationPermission = async () => {
  try {
    if (Platform.OS === 'android') {
      if (Platform.Version >= 33) {
        const result = await request(PERMISSIONS.ANDROID.POST_NOTIFICATIONS);
        if (result === RESULTS.GRANTED) {
          return true;
        }
      } else {
        console.log(
          'Notification permission not required for Android version below 33',
        );
        return true;
      }
    } else {
      // iOS
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log('Notification permission granted on iOS');
        return true;
      } else {
        console.log('Notification permission denied on iOS');
        // Go directly to settings
        Linking.openSettings();
        return false;
      }
    }
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return false;
  }
};

// Check Fine Location Permission
export const checkForegorundLocationPermission = async () => {
  try {
    if (Platform.OS === 'ios') {
      const result = await check(PERMISSIONS.IOS.LOCATION_ALWAYS);
      return result === RESULTS.GRANTED;
    } else if (Platform.OS === 'android') {
      const fineLocation = await check(
        PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
      );
      return fineLocation === RESULTS.GRANTED;
    }
    return false;
  } catch (error) {
    console.error('Error checking location permission:', error);
    return false;
  }
};

// Check background Location Permission
export const checkLocationPermission = async () => {
  try {
    if (Platform.OS === 'ios') {
      // Check for fine location permission (iOS does not have coarse location)
      const alwaysLocation = await check(PERMISSIONS.IOS.LOCATION_ALWAYS);
      const whenInUseLocation = await check(
        PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
      );
      return (
        alwaysLocation === RESULTS.GRANTED ||
        whenInUseLocation === RESULTS.GRANTED
      );
    } else if (Platform.OS === 'android') {
      const fineLocation = await check(
        PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
      );
      if (fineLocation !== RESULTS.GRANTED) {
        return false;
      }

      // Check for background location
      const backgroundLocation = await check(
        PERMISSIONS.ANDROID.ACCESS_BACKGROUND_LOCATION,
      );
      return backgroundLocation === RESULTS.GRANTED;
    }
    return false;
  } catch (error) {
    console.error('Error checking location permission:', error);
    return false;
  }
};

//check precise location
export const checkPreciseLocationPermission = async () => {
  try {
    if (Platform.OS === 'ios') {
      // Check for precise location permissions on iOS
      const alwaysLocation = await check(PERMISSIONS.IOS.LOCATION_ALWAYS);
      const whenInUseLocation = await check(
        PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
      );
      return (
        alwaysLocation === RESULTS.GRANTED ||
        whenInUseLocation === RESULTS.GRANTED
      );
    } else if (Platform.OS === 'android') {
      // Check for precise location permission on Android
      const fineLocation = await check(
        PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
      );
      const coarseLocation = await check(
        PERMISSIONS.ANDROID.ACCESS_COARSE_LOCATION,
      );

      // Ensure only fine location is granted (not coarse)
      return (
        fineLocation === RESULTS.GRANTED && coarseLocation !== RESULTS.GRANTED
      );
    }
    return false;
  } catch (error) {
    console.error('Error checking location permission:', error);
    return false;
  }
};

// Check Overlay Permission
export const checkOverlayPermission = async () => {
  if (Platform.OS === 'android') {
    try {
      const hasPermission = await FloatingWindow.checkPermission();
      return hasPermission;
    } catch (error) {
      console.error('Error checking overlay permission:', error);
      return false;
    }
  }
  return true; // Always true for iOS
};

// Check Notification Permission
export const checkNotificationPermission = async () => {
  try {
    if (Platform.OS === 'android') {
      if (Platform.Version >= 33) {
        const result = await check(PERMISSIONS.ANDROID.POST_NOTIFICATIONS);
        return result === RESULTS.GRANTED;
      } else {
        console.log(
          'Notification permission not required for Android version below 33',
        );
        return true;
      }
    } else {
      const authStatus = await messaging().hasPermission();
      return (
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL
      );
    }
  } catch (error) {
    console.error('Error checking notification permission:', error);
    return false;
  }
};
