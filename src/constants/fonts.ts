let i18n;
try {
  i18n = require('../i18n/i18n').default;
} catch (error) {
  console.warn('Failed to import i18n, defaulting to English:', error);
  i18n = {language: 'en'};
}

const getLocale = () => {
  try {
    return i18n && i18n.language ? i18n.language : 'en';
  } catch (e) {
    console.warn('Error accessing i18n language, defaulting to en:', e);
    return 'en';
  }
};

const needsSmallerFonts = () => {
  const locale = getLocale();
  return locale === 'ta' || locale === 'ma';
};

const getResponsiveSize = (normalSize, smallerSize) => {
  return needsSmallerFonts() ? smallerSize : normalSize;
};

// Font sizes with dynamic calculation
export const sizes = {
  get largeTitle() {
    return getResponsiveSize(36, 36);
  },
  get h1() {
    return getResponsiveSize(32, 32);
  },
  get h2() {
    return getResponsiveSize(28, 28);
  },
  get h3() {
    return getResponsiveSize(24, 24);
  },
  get h4() {
    return getResponsiveSize(22, 22);
  },
  get h5() {
    return getResponsiveSize(20, 20);
  },
  get h6() {
    return getResponsiveSize(18, 18);
  },
  get body() {
    return getResponsiveSize(16, 16);
  },
};

export const GeistFont = {
  bold: 'Geist-Bold',
  regular: 'Geist-Regular',
  variable: 'GeistVariableVF',
};

export const EBGaramondFont = {
  EBbold: 'EBGaramond-Bold',
  regular: 'EBGaramond-Regular',
};

export const colors = {
  white: '#fafbfe',
  grey: '#838489',
  darkGrey: '#1f1f1f',
  black: '#141418',
  lightGrey: '#CDCED4',
  red: '#EC8786',
  darkRed: '#DC3545',
  davyGrey: '#4A4B4F',
  darkCharcoal: '#2d2e32',
  slateGray: '#7b7c82',
  metallicSilver: '#A5A6AB',
  semiTransparentWhite: '#FFFFFF33',
  grayishBlack: '#343539',
  green: '#14761F',
  yellow: '#dda73b',
  blue: '#4169E1',
  lightGreen: '#00a884',
  fluorescentGreen: '#228B22',
  mapBlue: '#0863ba',
};
