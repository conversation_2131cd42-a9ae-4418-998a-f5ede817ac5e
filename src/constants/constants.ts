import i18n from '../i18n/i18n';

export const Keyboard = {
  BACKSPACE: 'Backspace',
};

export const DarkMapStyle = [
  {
    elementType: 'geometry',
    stylers: [
      {
        color: '#1f1f1f',
      },
    ],
  },
  {
    elementType: 'labels.text.stroke',
    stylers: [
      {
        color: '#242f3e',
      },
    ],
  },
  {
    elementType: 'labels.text.fill',
    stylers: [
      {
        color: '#746855',
      },
    ],
  },
  {
    featureType: 'road',
    elementType: 'geometry',
    stylers: [
      {
        color: '#0d0d0d',
      },
    ],
  },
  {
    featureType: 'road',
    elementType: 'labels.text.fill',
    stylers: [
      {
        color: 'grey',
      },
    ],
  },
];

export const vehiclesData = [
  {
    id: '1',
    name: i18n.t('auto'),
    backendName: 'AUTORICKSHAW',
    image: require('../../assets/images/auto.png'),
    seats: `3 ${i18n.t('seats')}`,
    rate: '₹20 - ₹40',
    disabled: false,
  },
  {
    id: '2',
    name: i18n.t('car'),
    backendName: 'HATCHBACK',
    image: require('../../assets/images/car.png'),
    seats: `4 ${i18n.t('seats')}`,
    rate: '₹160',
    disabled: true,
  },
  // {
  //   id: '3',
  //   name: i18n.t('bike'),
  //   backendName: 'SUV',
  //   image: require('../../assets/images/bike.png'),
  //   seats: `5 ${i18n.t('seats')}`,
  //   rate: '₹460',
  //   disabled: true,
  // },
  // {
  //   id: '4',
  //   name: i18n.t('suv_luxe'),
  //   backendName: 'SUVLUXE',
  //   image: require('../../assets/images/suvLuxe.png'),
  //   seats: `7 ${i18n.t('seats')}`,
  //   rate: '₹1019',
  // },
];

export const monthAbbreviations: {[key: string]: string} = {
  January: i18n.t('jan'),
  February: i18n.t('feb'),
  March: i18n.t('mar'),
  April: i18n.t('apr'),
  May: i18n.t('may'),
  June: i18n.t('jun'),
  July: i18n.t('jul'),
  August: i18n.t('aug'),
  September: i18n.t('sep'),
  October: i18n.t('oct'),
  November: i18n.t('nov'),
  December: i18n.t('dec'),
};

export const fareDetails = [
  {label: i18n.t('min_fare'), value: '₹30'},
  {label: i18n.t('rate_above'), value: '₹10 per km'},
  {label: i18n.t('driver_pickup'), value: '₹10'},
  {
    label: i18n.t('driver_addition'),
    value: '10% ' + i18n.t('of') + ' ' + i18n.t('base_fare'),
  },
];
export const OTP_TIMEOUT = 60;
export const OTP_RESEND_TIMEOUT = 60;

export const STATUS_CODE = {
  created: 201,
  ok: 200,
  unauthorized: 401,
  not_found: 404,
  server_error: 500,
  bad_request: 400,
  conflict: 409,
};

export const MAX_RETRIES = 3;
export const RETRY_DELAY = 2000;

export const RideEvents = {
  RIDE_REQUEST: 'RideRequest',
  RIDE_ACCEPTED: 'RideAccepted',
  RIDE_COMPLETED: 'RideCompleted',
  RIDE_REJECTED: 'RideRejected',
  RIDE_TIMEOUT: 'RideTimeOut',
  RIDE_OTP_VERIFY: 'RideOtpVerify',
  RIDE_CANCELED: 'RideCanceled',
  RIDE_CANCELED_DRIVER: 'RideCanceledDriver',
  RIDE_STATUS_CHECK: 'RideStatusCheck',
  RIDE_ABORTED: 'RideAborted',
};

// Add a constant for the default ride expiry time
export const DEFAULT_RIDE_EXPIRY_TIME = 18; // 18 seconds default timeout
