<svg width="44" height="56" viewBox="0 0 44 56" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M22 56C23.6569 56 25 55.1046 25 54C25 52.8954 23.6569 52 22 52C20.3431 52 19 52.8954 19 54C19 55.1046 20.3431 56 22 56Z" fill="#F2F3F8"/>
<g filter="url(#filter0_dd_1004_4964)">
<rect x="12" width="20" height="20" rx="1" fill="#FAFBFE" shape-rendering="crispEdges"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M22 14C24.2091 14 26 12.2091 26 10C26 7.79086 24.2091 6 22 6C19.7909 6 18 7.79086 18 10C18 12.2091 19.7909 14 22 14Z" fill="#141418"/>
</g>
<path opacity="0.5" d="M22 20L22 44" stroke="#FAFBFE" stroke-width="2"/>
<defs>
<filter id="filter0_dd_1004_4964" x="0" y="0" width="44" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="2" operator="erode" in="SourceAlpha" result="effect1_dropShadow_1004_4964"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.03 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1004_4964"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4" operator="erode" in="SourceAlpha" result="effect2_dropShadow_1004_4964"/>
<feOffset dy="12"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1004_4964" result="effect2_dropShadow_1004_4964"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1004_4964" result="shape"/>
</filter>
</defs>
</svg>
