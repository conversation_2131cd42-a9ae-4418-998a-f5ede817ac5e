import Sound from 'react-native-sound';

Sound.setCategory('Playback');

let rideSound: Sound | null = null;
let cancelSound: Sound | null = null;
let chatSound: Sound | null = null;
let acceptSound: Sound | null = null;
let offerSound: Sound | null = null;
let aborterSound: Sound | null = null;
let completedSound: Sound | null = null;
let rideStatusSound: Sound | null = null;

let soundTimer: NodeJS.Timeout | null = null;
let loopCount: number = 0;
let statusSoundIntervalTimer: NodeJS.Timeout | null = null;

const TARGET_DURATION_MS: number = 18000;

export const playRideSound = () => {
  stopRideSound();
  loopCount = 0;

  rideSound = new Sound('ride.mp3', Sound.MAIN_BUNDLE, error => {
    if (error) {
      console.error('Failed to load the sound', error);
      return;
    }

    const soundDurationSec = rideSound?.getDuration() || 0;
    const soundDurationMs = soundDurationSec * 1000;
    console.log(`Individual sound duration: ${soundDurationSec} seconds`);

    const requiredLoops = Math.ceil(TARGET_DURATION_MS / soundDurationMs);
    console.log(
      `Will loop sound ${requiredLoops} times to reach ${
        TARGET_DURATION_MS / 1000
      } seconds`,
    );

    const playLoop = () => {
      if (!rideSound) return;

      rideSound.play(success => {
        if (!success) {
          console.error('Playback failed due to audio decoding errors');
          return;
        }

        loopCount++;
        console.log(`Completed loop ${loopCount} of ${requiredLoops}`);

        if (loopCount < requiredLoops && rideSound) {
          playLoop();
        }
      });
    };

    playLoop();

    soundTimer = setTimeout(() => {
      console.log(
        `Timer reached ${TARGET_DURATION_MS / 1000} seconds, stopping sound`,
      );
      stopRideSound();
    }, TARGET_DURATION_MS);
  });
};

export const stopRideSound = () => {
  if (soundTimer) {
    clearTimeout(soundTimer);
    soundTimer = null;
  }

  if (rideSound) {
    console.log('Stopping ride sound');
    rideSound.stop();
    rideSound.release();
    rideSound = null;
  }

  loopCount = 0;
};

export const playCancelSound = () => {
  stopAllSounds();

  cancelSound = new Sound('usercanceled.mp3', Sound.MAIN_BUNDLE, error => {
    if (error) {
      console.error('Failed to load the cancel sound', error);
      return;
    }

    cancelSound?.play(success => {
      if (!success) {
        console.error(
          'Playback of cancel sound failed due to audio decoding errors',
        );
      }
    });
  });
};

// export const playChatSound = () => {
//   stopAllSounds();

//   chatSound = new Sound('chat.mp3', Sound.MAIN_BUNDLE, error => {
//     if (error) {
//       console.error('Failed to load the chat sound', error);
//       return;
//     }

//     chatSound?.play(success => {
//       if (!success) {
//         console.error(
//           'Playback of chat sound failed due to audio decoding errors',
//         );
//       }
//     });
//   });
// };

export const playAcceptSound = () => {
  stopAllSounds();

  acceptSound = new Sound('accept.mp3', Sound.MAIN_BUNDLE, error => {
    if (error) {
      console.error('Failed to load the accept sound', error);
      return;
    }

    acceptSound?.play(success => {
      if (!success) {
        console.error(
          'Playback of accept sound failed due to audio decoding errors',
        );
      }
    });
  });
};

export const playOfferSound = () => {
  stopOfferSound();

  offerSound = new Sound('notification.mp3', Sound.MAIN_BUNDLE, error => {
    if (error) {
      console.error('Failed to load the offer notification sound', error);
      return;
    }

    offerSound?.play(success => {
      if (!success) {
        console.error(
          'Playback of offer sound failed due to audio decoding errors',
        );
      }
    });
  });
};

export const stopOfferSound = () => {
  if (offerSound) {
    offerSound.stop();
    offerSound.release();
    offerSound = null;
  }
};

export const playAbortedSound = () => {
  stopAllSounds();

  aborterSound = new Sound('rideaborted.mp3', Sound.MAIN_BUNDLE, error => {
    if (error) {
      console.error('Failed to load the aborter sound', error);
      return;
    }

    aborterSound?.play(success => {
      if (!success) {
        console.error(
          'Playback of aborter sound failed due to audio decoding errors',
        );
      }
    });
  });
};

export const playCompletedSound = () => {
  stopAllSounds();

  completedSound = new Sound('ridecompleted.mp3', Sound.MAIN_BUNDLE, error => {
    if (error) {
      console.error('Failed to load the completed sound', error);
      return;
    }

    completedSound?.play(success => {
      if (!success) {
        console.error(
          'Playback of completed sound failed due to audio decoding errors',
        );
      }
    });
  });
};

export const playRideStatusSound = () => {
  stopRideStatusSound();

  rideStatusSound = new Sound('ridestatus.mp3', Sound.MAIN_BUNDLE, error => {
    if (error) {
      console.error('Failed to load the ride status sound', error);
      return;
    }

    const playSound = () => {
      if (rideStatusSound) {
        rideStatusSound.play(success => {
          if (!success) {
            console.error('Playback failed due to audio decoding errors');
          }
        });
      }
    };

    playSound();

    statusSoundIntervalTimer = setInterval(() => {
      playSound();
    }, 5000);
  });
};

export const stopRideStatusSound = () => {
  if (statusSoundIntervalTimer) {
    clearInterval(statusSoundIntervalTimer);
    statusSoundIntervalTimer = null;
  }

  if (rideStatusSound) {
    rideStatusSound.stop();
    rideStatusSound.release();
    rideStatusSound = null;
  }
};

export const stopAllSounds = () => {
  // First stop and release each sound individually
  if (rideSound) {
    rideSound.stop();
    rideSound.release();
    rideSound = null;
  }

  if (cancelSound) {
    cancelSound.stop();
    cancelSound.release();
    cancelSound = null;
  }

  if (chatSound) {
    chatSound.stop();
    chatSound.release();
    chatSound = null;
  }

  if (acceptSound) {
    acceptSound.stop();
    acceptSound.release();
    acceptSound = null;
  }

  if (offerSound) {
    offerSound.stop();
    offerSound.release();
    offerSound = null;
  }

  if (aborterSound) {
    aborterSound.stop();
    aborterSound.release();
    aborterSound = null;
  }

  if (completedSound) {
    completedSound.stop();
    completedSound.release();
    completedSound = null;
  }
  
  if (rideStatusSound) {
    rideStatusSound.stop();
    rideStatusSound.release();
    rideStatusSound = null;
  }

  if (soundTimer) {
    clearTimeout(soundTimer);
    soundTimer = null;
  }
  
  if (statusSoundIntervalTimer) {
    clearInterval(statusSoundIntervalTimer);
    statusSoundIntervalTimer = null;
  }

  loopCount = 0;
};
