import MapView, {LatLng} from 'react-native-maps';
import Config from 'react-native-config';

//Direction.tsx

export const decodePolyline = (encoded: string) => {
  const points: {latitude: number; longitude: number}[] = [];
  let index = 0,
    lat = 0,
    lng = 0;

  while (index < encoded.length) {
    let b,
      shift = 0,
      result = 0;

    do {
      b = encoded.charCodeAt(index++) - 63;
      result |= (b & 0x1f) << shift;
      shift += 5;
    } while (b >= 0x20);

    const dlat = (result & 1) !== 0 ? ~(result >> 1) : result >> 1;
    lat += dlat;

    shift = 0;
    result = 0;

    do {
      b = encoded.charCodeAt(index++) - 63;
      result |= (b & 0x1f) << shift;
      shift += 5;
    } while (b >= 0x20);

    const dlng = (result & 1) !== 0 ? ~(result >> 1) : result >> 1;
    lng += dlng;

    points.push({latitude: lat / 1e5, longitude: lng / 1e5});
  }

  return points;
};

export const zoomToRoute = (
  mapViewRef: React.RefObject<MapView>,
  coordinates: {latitude: number; longitude: number}[],
) => {
  if (coordinates.length > 0 && mapViewRef.current) {
    const minLat = Math.min(...coordinates.map(coord => coord.latitude));
    const maxLat = Math.max(...coordinates.map(coord => coord.latitude));
    const minLng = Math.min(...coordinates.map(coord => coord.longitude));
    const maxLng = Math.max(...coordinates.map(coord => coord.longitude));

    const padding = 0.01;
    const region = {
      latitude: (minLat + maxLat) / 2,
      longitude: (minLng + maxLng) / 2,
      latitudeDelta: Math.max(maxLat - minLat + padding, 0.01),
      longitudeDelta: Math.max(maxLng - minLng + padding, 0.01),
    };
    mapViewRef.current.animateToRegion(region, 500);
  }
};

interface DirectionsResponse {
  points: {latitude: number; longitude: number}[];
  distance: string;
  duration: string;
}

// Function to convert miles to kilometers
const milesToKilometers = (miles: number) => miles * 1.60934;

export const getDirections = async (
  origin: string,
  destination: string,
): Promise<DirectionsResponse> => {
  try {
    const directionsApiUrl = `https://maps.googleapis.com/maps/api/directions/json?origin=${origin}&destination=${destination}&key=${Config.GOOGLE_API}`;
    const response = await fetch(directionsApiUrl);
    const data = await response.json();

    if (
      data.routes &&
      data.routes.length > 0 &&
      data.routes[0].overview_polyline
    ) {
      const overviewPolyline = data.routes[0].overview_polyline.points;
      const points = decodePolyline(overviewPolyline);
      const distanceInMeters = data.routes[0].legs[0].distance.value;
      const duration = data.routes[0].legs[0].duration.text;

      const distanceInKilometers = distanceInMeters / 1000; // Convert meters to kilometers
      const distance = `${distanceInKilometers.toFixed(2)} km`;

      return {points, distance, duration};
    } else {
      throw new Error('Directions not found');
    }
  } catch (error) {
    console.error('Error fetching directions:', error);
    throw error;
  }
};

//MapComponent.tsx

export const calculateDistance = (
  userLocation: LatLng | null,
  region: LatLng | null,
): number => {
  if (userLocation && region) {
    const R = 6371e3; // Radius of the Earth in meters
    const lat1 = userLocation.latitude * (Math.PI / 180);
    const lat2 = region.latitude * (Math.PI / 180);
    const lon1 = userLocation.longitude * (Math.PI / 180);
    const lon2 = region.longitude * (Math.PI / 180);

    const dLat = lat2 - lat1;
    const dLon = lon2 - lon1;

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1) * Math.cos(lat2) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }
  return 0;
};

export const fetchAddress = async (latitude: number, longitude: number) => {
  try {
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${Config.GOOGLE_API}`;
    const response = await fetch(url);
    const data = await response.json();
    if (data.status === 'OK') {
      return data.results[0].formatted_address;
    } else {
      return 'Unable to retrieve address';
    }
  } catch (error) {
    console.error('Error fetching address:', error);
    return 'Error fetching address';
  }
};

export const calculateRemainingRoute = (
  points: LatLng[],
  driverLocation: LatLng,
): LatLng[] => {
  const threshold = 0.01;

  const remainingPoints = points.filter((point, index) => {
    if (index === 0) return true;
    const previousPoint = points[index - 1];
    const distanceToPreviousPoint = calculateDistance(
      driverLocation,
      previousPoint,
    );
    return distanceToPreviousPoint > threshold;
  });
  return remainingPoints;
};
