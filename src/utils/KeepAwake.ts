import {NativeModules, Platform} from 'react-native';

/**
 * Utility class for managing screen wake lock
 * When activated, it will prevent the device from going to sleep
 * while the app is in the foreground
 */
export default class KeepAwake {
  /**
   * Activates the keep awake function to prevent the device from sleeping
   * This should be called when your app is in the foreground and needs to stay awake
   */
  static activate(): void {
    try {
      if (Platform.OS === 'android' && NativeModules.KCKeepAwake) {
        NativeModules.KCKeepAwake.activate();
      } else if (Platform.OS === 'ios' && NativeModules.KCKeepAwake) {
        NativeModules.KCKeepAwake.activate();
      } else {
        console.log('KeepAwake: Native module not available');
      }
    } catch (error) {
      console.error('Failed to activate keep awake:', error);
    }
  }

  /**
   * Deactivates the keep awake function, allowing the device to sleep normally
   * This can be called when your app goes to the background or when keeping the screen on is no longer needed
   */
  static deactivate(): void {
    try {
      if (Platform.OS === 'android' && NativeModules.KCKeepAwake) {
        NativeModules.KCKeepAwake.deactivate();
      } else if (Platform.OS === 'ios' && NativeModules.KCKeepAwake) {
        NativeModules.KCKeepAwake.deactivate();
      } else {
        console.log('KeepAwake: Native module not available');
      }
    } catch (error) {
      console.error('Failed to deactivate keep awake:', error);
    }
  }
}
