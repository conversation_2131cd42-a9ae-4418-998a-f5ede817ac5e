// services/i18n.ts
import i18n from 'i18next';
import {initReactI18next} from 'react-i18next';
import * as RNLocalize from 'react-native-localize';
import en from './en/en.json';
import ta from './ta/ta.json';
import ma from './ma/ma.json';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const LANGUAGE_KEY = '@app_language';

const resources = {
  en: {
    translation: en,
  },
  ta: {
    translation: ta,
  },
  ma: {
    translation: ma,
  },
};

i18n.use(initReactI18next).init({
  compatibilityJSON: 'v3',
  lng: 'en',
  fallbackLng: 'en',
  resources,
  interpolation: {
    escapeValue: false,
  },
  react: {
    useSuspense: false,
  },
});

const loadStoredLanguage = async () => {
  try {
    const savedLanguage = await AsyncStorage.getItem(LANGUAGE_KEY);
    if (savedLanguage) {
      await i18n.changeLanguage(savedLanguage);
    } else {
      const deviceLanguage = RNLocalize.getLocales()[0].languageCode;
      const supportedLanguages = ['en', 'ta', 'ma'];
      if (supportedLanguages.includes(deviceLanguage)) {
        await i18n.changeLanguage(deviceLanguage);
        await AsyncStorage.setItem(LANGUAGE_KEY, deviceLanguage);
      }
    }
  } catch (error) {
    console.error('Error loading saved language:', error);
  }
};

loadStoredLanguage();

export default i18n;
