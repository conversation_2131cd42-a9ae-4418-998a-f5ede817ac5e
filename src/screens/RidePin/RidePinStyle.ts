import {StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, theme, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  safeArea: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: spacing.xxl,
  },

  iconContainer: {
    flexDirection: 'row',
    marginBottom: spacing.md,
  },

  icon: {
    marginTop: spacing.xl,
  },

  title: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.largeTitle,
    color: colors.white,
    marginBottom: spacing.xl,
    maxWidth: '90%',
  },

  resendTxt: {
    fontFamily: GeistFont.regular,
    fontSize: sizes.h6,
    color: colors.grey,
  },

  resendOtp: {
    fontSize: sizes.h6,
    fontWeight: '400',
    color: colors.white,
    textDecorationLine: 'underline',
    textDecorationColor: colors.lightGrey,
    textDecorationStyle: 'solid',
    paddingTop: spacing.lg,
  },

  enterBtn: {
    marginBottom: spacing.xl,
  },
});
