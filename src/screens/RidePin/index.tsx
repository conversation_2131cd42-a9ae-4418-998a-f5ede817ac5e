import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  ImageBackground,
  Text,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  View,
} from 'react-native';
import {images} from '../../constants';
import styles from './RidePinStyle';
import {useToast} from '../../components/Toast/Toast';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import OTPInput from '../../components/OtpInput/OtpInput';
import Button from '../../components/Button/Button';
import Back from '../../icons/back.svg';
import close from '../../icons/close.svg';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import {useTranslation} from 'react-i18next';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useLoader} from '../../hooks/useLoader';
import {STATUS_CODE} from '../../constants/constants';
import HollowButton from '../../components/Button/HollowButton/HollowButton';
import RideService from '../../services/RideService';
import {useRideDetails} from '../../hooks/useRideDetailsContext';
import * as Sentry from '@sentry/react-native';
import {debounce} from 'lodash';

interface RidePinScreenProps {
  navigation: any;
  route: any;
}

const RidePin: React.FC<RidePinScreenProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {showLoader, hideLoader} = useLoader();
  const otpInputRef = useRef<any>(null);
  const {tripDetails} = useRideDetails();
  const [isOtpComplete, setIsOtpComplete] = useState<boolean>(false);
  const [pin, setPin] = useState<string>('');
  const {showToast} = useToast();
  const title = route.params?.title || t('enter_pin');
  const [loadingButton, setLoadingButton] = useState<
    'verify' | 'cancel' | null
  >(null);

  const verifyOtp = async () => {
    try {
      setLoadingButton('verify');
      if (tripDetails) {
        const response = await RideService.verifyOtp(tripDetails.id, pin);
      } else {
        hideLoader();
      }
    } catch (err: any) {
      const status = err?.response?.status;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
      Sentry.captureException(err, {
        tags: {
          tripId: tripDetails?.id,
          action: 'pin_verification',
        },
        extra: {
          pin_length: pin?.length,
          status_code: status,
        },
      });
    } finally {
      setLoadingButton(null);
    }
  };

  const handleChange = (otpValue: string) => {
    setPin(otpValue);
  };

  const handleOtpComplete = useCallback(
    debounce((complete: boolean) => {
      if (complete) {
        setIsOtpComplete(true);
      } else {
        setIsOtpComplete(false);
      }
    }, 300),
    [],
  );


  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          style={{flex: 1}}>
          <ScrollView
            contentContainerStyle={{flexGrow: 1}}
            keyboardShouldPersistTaps="handled">
            <View style={styles.iconContainer}>
              <TouchableOpacity
                onPress={() => navigation.navigate('PickupSpot')}>
                <IconSvgView svgStyle={styles.icon} source={Back} />
              </TouchableOpacity>
            </View>
            <View>
              <Text numberOfLines={3} adjustsFontSizeToFit style={styles.title}>
                {title}
              </Text>
            </View>
            <View style={{width: '70%'}}>
              <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : undefined}
                style={{flex: 1}}
                testID="otp-input">
                <OTPInput
                  ref={otpInputRef}
                  otp={pin}
                  onOtpChange={handleChange}
                  defaultValue={4}
                  onOtpComplete={handleOtpComplete}
                />
              </KeyboardAvoidingView>
            </View>
            <FlexContainer justifyContent="flex-end">
              <Button
                title={t('continue')}
                style={styles.enterBtn}
                disabled={!isOtpComplete || loadingButton === 'cancel'}
                onPress={verifyOtp}
                loading={loadingButton === 'verify'}
              />
              <HollowButton
                title={t('go_back')}
                style={styles.enterBtn}
                onPress={() => navigation.navigate('PickupSpot')}
              />
            </FlexContainer>
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default RidePin;
