import React, {useCallback, useEffect} from 'react';
import {View, Text, ImageBackground, StyleSheet} from 'react-native';
import wifi from '../../icons/wifi.svg';
import images from '../../constants/images';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import Button from '../../components/Button/Button';
import {GeistFont, colors, sizes} from '../../constants';
import {spacing} from '../../constants/theme';
import {useTranslation} from 'react-i18next';
import {useGeofence} from '../../hooks/useGeofence';
import {useRideDetails} from '../../hooks/useRideDetailsContext';
import {useFocusEffect} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import notifee from '@notifee/react-native';
import { stopRideSound } from '../../utils/Sound';

interface OfflineScreenProps {
  handleRefresh: () => void;
  retryCount: number;
}

const OfflineScreen: React.FC<OfflineScreenProps> = ({
  handleRefresh,
  retryCount,
}) => {
  const {t} = useTranslation();
  const {stopGeofencing} = useGeofence();
  const {setShowRideModal, setTripDetails} = useRideDetails();

  const clearLocalStorage = async () => {
    setShowRideModal(false);
    setTripDetails(null);
    stopRideSound();
    await AsyncStorage.removeItem('showRideModal');
    await AsyncStorage.removeItem('sentTime');

    const noti = await AsyncStorage.getItem('notificationId');
    noti && (await notifee.cancelDisplayedNotification(noti));
  };

  useFocusEffect(
    useCallback(() => {
      stopGeofencing();
      setShowRideModal(false);
      clearLocalStorage();
    }, []),
  );

  return (
    <ImageBackground source={images.map} style={styles.background}>
      <View style={styles.container}>
        <IconSvgView source={wifi} size={40} />
        <Text style={styles.offlineText}>{t('no_internet')}</Text>
        <Button
          title="Refresh"
          style={styles.refreshBtn}
          onPress={handleRefresh}
        />
      </View>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  background: {flex: 1, resizeMode: 'cover', justifyContent: 'center'},
  container: {flex: 1, justifyContent: 'center', alignItems: 'center'},
  offlineText: {
    color: colors.grey,
    textAlign: 'center',
    maxWidth: '80%',
    marginTop: spacing.lg,
    fontFamily: GeistFont.regular,
    fontSize: sizes.h6,
  },
  retryText: {
    color: colors.grey,
    textAlign: 'center',
    marginTop: spacing.md,
    fontFamily: GeistFont.regular,
    fontSize: sizes.h6,
  },
  refreshBtn: {width: '80%', marginTop: spacing.xxl},
});

export default OfflineScreen;
