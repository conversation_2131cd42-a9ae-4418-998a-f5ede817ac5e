import {Platform, StyleSheet, ViewStyle} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },
  safeArea: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: spacing.xl,
  },

  iconContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: spacing.xl,
  },

  icon: {
    marginTop: spacing.xl,
  },

  diamondIcon: {
    marginTop: spacing.xl,
    marginRight: spacing.xl,
  },

  title: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.largeTitle,
    color: colors.white,
    marginVertical: spacing.md,
  },

  note: {
    fontFamily: GeistFont.variable,
    fontSize: sizes.body,
    color: colors.grey,
  },

  list: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.h3,
    color: colors.white,
    marginVertical: spacing.lg,
  },

  continueBtn: Platform.select({
    android: {
      marginVertical: spacing.xl,
    },
  }) as ViewStyle,

  cancelBtn: {
    backgroundColor: 'rgba(192, 104, 104, 0.04)',
    marginBottom: spacing.xl,
  },
});
