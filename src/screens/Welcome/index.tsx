import React, {useState, useEffect, useCallback, useRef} from 'react';
import {
  ImageBackground,
  Text,
  ScrollView,
  View,
  TouchableOpacity,
  NativeModules,
  NativeEventEmitter,
} from 'react-native';
import messaging from '@react-native-firebase/messaging';
import {useTranslation} from 'react-i18next';
import {SafeAreaView} from 'react-native-safe-area-context';
import {colors, images, sizes} from '../../constants';
import styles from './WelcomeStyle';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import diamond from '../../icons/diamond.svg';
import chevronIcon from '../../icons/chevron-down.svg';
import Button from '../../components/Button/Button';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import {useDriver} from '../../hooks/useDriver';
import DriverService from '../../services/DriverService';
import Back from '../../icons/back.svg';
import {STATUS_CODE} from '../../constants/constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useToast} from '../../components/Toast/Toast';
import {useGeofence} from '../../hooks/useGeofence';
import {useFocusEffect} from '@react-navigation/native';
import RideService from '../../services/RideService';
import support from '../../icons/support.svg';
import SupportMenu from '../../components/SupportMenu/SupportMenu';
import {useLoader} from '../../hooks/useLoader';
import * as Sentry from '@sentry/react-native';

const {LocationModule} = NativeModules;
const locationEventEmitter = new NativeEventEmitter(LocationModule);

// Define the possible verification statuses
const VERIFICATION_STATUS = {
  VERIFIED: 'VERIFIED',
  REJECTED: 'REJECTED',
  PENDING_UPLOAD: 'PENDING_UPLOAD',
  VERIFICATION_PENDING: 'VERIFICATION_PENDING',
  UPLOADED: 'UPLOADED',
};

interface WelcomeProps {
  navigation: any;
}

const Welcome: React.FC<WelcomeProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {driver, fetchDriver} = useDriver();
  const [documentsVerification, setDocumentsVerification] = useState<any>(null);
  const [verificationStarted, setVerificationStarted] = useState<string>('');
  const [allDocumentsReady, setAllDocumentsReady] = useState<boolean>(false);
  const [allDocumentsVerified, setAllDocumentsVerified] =
    useState<boolean>(false);
  const {showToast} = useToast();
  const {showLoader, hideLoader} = useLoader();
  const {stopGeofencing} = useGeofence();
  const supportIconRef = useRef(null);
  const [supportModalVisible, setSupportModalVisible] = useState(false);
  const [iconPosition, setIconPosition] = useState(null);

  // Documents to track
  const requiredDocuments = {
    license_number: {
      title: t('dl_number'),
      navScreen: 'DlNumber',
    },
    license: {
      title: t('dl'),
      navScreen: 'Dl',
    },
    rc: {
      title: t('rc'),
      navScreen: 'Rc',
    },
    profile_photo: {
      title: t('pp'),
      navScreen: 'Pp',
    },
  };

  const toggleSupportMenu = () => {
    if (supportIconRef.current) {
      supportIconRef.current.measure(
        (
          _x: number,
          _y: number,
          width: number,
          height: number,
          pageX: number,
          pageY: number,
        ) => {
          setIconPosition({x: pageX, y: pageY, width, height});
          setSupportModalVisible(!supportModalVisible);
        },
      );
    } else {
      setSupportModalVisible(!supportModalVisible);
    }
  };

  useFocusEffect(
    useCallback(() => {
      const stopServices = async () => {
        try {
          stopGeofencing();
          await RideService.sendDriverStatus({isActive: 0});
          await AsyncStorage.removeItem('active');
          locationEventEmitter.removeAllListeners('locationUpdate');
          const LocationFetchTrigger = NativeModules.LocationModule;
          LocationFetchTrigger.stopService();
          await messaging().deleteToken();
        } catch (error: any) {
          console.error('Error stopping services:', error);
          Sentry.captureException(error, {
            tags: {
              driverId: driver?.id,
              action: 'stop_services_welcome_screen',
              status_code: error?.response?.status,
            },
          });
        }
      };

      stopServices();

      return () => {
        locationEventEmitter.removeAllListeners('locationUpdate');
      };
    }, []),
  );

  const fetchVerificationStatus = async () => {
    try {
      showLoader && showLoader();
      const response = await DriverService.getVerificationStatus();
      console.log('Verification status response:', response.data);
      if (response.status === STATUS_CODE.ok) {
        setDocumentsVerification(response.data.data);

        const allVerified =
          response.data.data.license_number_status ===
            VERIFICATION_STATUS.VERIFIED &&
          response.data.data.license_status === VERIFICATION_STATUS.VERIFIED &&
          response.data.data.profile_photo_status ===
            VERIFICATION_STATUS.VERIFIED &&
          response.data.data.rc_status === VERIFICATION_STATUS.VERIFIED;

        setAllDocumentsVerified(allVerified);
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err?.response?.data?.response?.code;
      console.log('Error code:', err.response?.data);
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        showToast(t('something_went_wrong'), 'failure');
      } else if (STATUS_CODE.bad_request === status) {
        code === 'no_verification_records' &&
          showToast(t('no_verification_records'), 'failure');
      }
      Sentry.captureException(err, {
        tags: {
          driverId: driver?.id,
          action: 'fetch_verification_status',
          status_code: status,
        },
        extra: {
          responseCode: code,
          responseData: err.response?.data
            ? JSON.stringify(err.response.data)
            : null,
        },
      });
    } finally {
      hideLoader && hideLoader();
    }
  };

  const getDocumentStatus = async () => {
    const documents = await AsyncStorage.getItem('documents');

    if (documents === 'VERIFICATION_PENDING') {
      setVerificationStarted('VERIFICATION_PENDING');
    }
  };

  const checkDocumentsReadiness = (verification: any) => {
    if (!verification) return false;

    const isUploaded = (status: string) =>
      status === VERIFICATION_STATUS.VERIFIED ||
      status === VERIFICATION_STATUS.UPLOADED ||
      status === VERIFICATION_STATUS.VERIFICATION_PENDING;

    const isReady =
      isUploaded(verification.license_number_status) &&
      isUploaded(verification.license_status) &&
      isUploaded(verification.profile_photo_status) &&
      isUploaded(verification.rc_status);

    const hasPendingUpload =
      verification.license_number_status ===
        VERIFICATION_STATUS.PENDING_UPLOAD ||
      verification.license_status === VERIFICATION_STATUS.PENDING_UPLOAD ||
      verification.profile_photo_status ===
        VERIFICATION_STATUS.PENDING_UPLOAD ||
      verification.rc_status === VERIFICATION_STATUS.PENDING_UPLOAD;

    return isReady && !hasPendingUpload;
  };

  useFocusEffect(
    useCallback(() => {
      const fetchData = async () => {
        await fetchDriver();
        await getDocumentStatus();
        await fetchVerificationStatus();
      };

      fetchData();
    }, []),
  );

  useEffect(() => {
    if (documentsVerification) {
      const isReady = checkDocumentsReadiness(documentsVerification);
      setAllDocumentsReady(isReady);
    }
  }, [documentsVerification]);

  const handleNavigation = (documentType: string) => {
    AsyncStorage.removeItem('documents');

    switch (documentType) {
      case 'license_number':
        navigation.navigate('DlNumber', {driver: 'Welcome'});
        break;
      case 'license':
        navigation.navigate('Dl', {driver: 'Welcome'});
        break;
      case 'rc':
        navigation.navigate('Rc', {driver: 'Welcome'});
        break;
      case 'profile_photo':
        navigation.navigate('Pp', {driver: 'Welcome'});
        break;
      default:
        break;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case VERIFICATION_STATUS.VERIFIED:
        return colors.green;
      case VERIFICATION_STATUS.REJECTED:
        return colors.red;
      case VERIFICATION_STATUS.PENDING_UPLOAD:
        return colors.red;
      case VERIFICATION_STATUS.UPLOADED:
        return colors.grey;
      default:
        return colors.yellow;
    }
  };

  const getStatusText = (status: string, docInfo: any = null) => {
    switch (status) {
      case VERIFICATION_STATUS.VERIFIED:
        return t('verified');
      case VERIFICATION_STATUS.REJECTED:
        return t('rejected');
      case VERIFICATION_STATUS.PENDING_UPLOAD:
        return docInfo
          ? `${t('please_upload')} ${t(docInfo.title)}`
          : t('pending_upload');
      case VERIFICATION_STATUS.VERIFICATION_PENDING:
        return t('verification_pending');
      default:
        return t('uploaded');
    }
  };

  const isDocumentClickable = (status: string) => {
    return (
      status === VERIFICATION_STATUS.REJECTED ||
      status === VERIFICATION_STATUS.PENDING_UPLOAD
    );
  };

  const startVerification = async () => {
    try {
      showLoader && showLoader();

      const response = await DriverService.startVerification();
      if (response.status === STATUS_CODE.created) {
        await AsyncStorage.setItem('documents', 'VERIFICATION_PENDING');
        setVerificationStarted(response.data.data.status);
        showToast(t('verification_started'), 'success');
      }

      await fetchVerificationStatus();
    } catch (err: any) {
      console.error('Error starting verification:', err);
      const status = err?.response?.status;
      const code = err?.response?.data?.response?.code;
      console.log('Error code:', code);

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        showToast(t('something_went_wrong'), 'failure');
      } else if (STATUS_CODE.bad_request === status) {
        code === 'no_verification_records'
          ? showToast(t('no_verification_records'), 'failure')
          : showToast(t('something_went_wrong'), 'failure');
      }
      Sentry.captureException(err, {
        tags: {
          driverId: driver?.id,
          action: 'start_verification',
          status_code: status,
        },
        extra: {
          responseCode: code,
          responseData: err.response?.data
            ? JSON.stringify(err.response.data)
            : null,
        },
      });
    } finally {
      hideLoader && hideLoader();
    }
  };

  const handleRefresh = async () => {
    try {
      showLoader && showLoader();
      await fetchDriver();
      await fetchVerificationStatus();
      await getDocumentStatus();
    } catch (err: any) {
      console.error('Error refreshing data:', err);
      Sentry.captureException(err, {
        tags: {
          driverId: driver?.id,
          action: 'refresh_welcome_screen',
          status_code: err?.response?.status,
        },
      });
    } finally {
      hideLoader && hideLoader();
    }
  };

  const renderDocumentRow = (docKey: string, docInfo: any) => {
    if (!documentsVerification) return null;

    const statusKey = `${docKey}_status`;
    const rejectionKey = `${docKey}_rejection_message`;
    const status = documentsVerification[statusKey];
    const rejectionMessage = documentsVerification[rejectionKey];
    const isClickable = isDocumentClickable(status);
    const isPendingUpload = status === VERIFICATION_STATUS.PENDING_UPLOAD;

    return (
      <TouchableOpacity
        key={docKey}
        activeOpacity={isClickable ? 0.7 : 1}
        onPress={() => isClickable && handleNavigation(docKey)}
        disabled={!isClickable}>
        <View style={styles.documentRow}>
          <View style={styles.documentInfo}>
            <Text
              style={[styles.list, isPendingUpload ? {color: colors.red} : {}]}>
              {t(docInfo.title)}
            </Text>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <Text
                style={[styles.verification, {color: getStatusColor(status)}]}>
                {getStatusText(status, isPendingUpload ? docInfo : null)}
              </Text>
            </View>
            {status === VERIFICATION_STATUS.REJECTED && rejectionMessage && (
              <Text style={styles.rejectionMessage}>
                {t('reason')}: {rejectionMessage}
              </Text>
            )}
          </View>
          {isClickable && <IconSvgView size={25} source={chevronIcon} />}
        </View>
        <FadingHorizontalLine />
      </TouchableOpacity>
    );
  };

  const renderFooterButton = () => {
    if (allDocumentsVerified) {
      return (
        <>
          <Text style={styles.tapText}>
            {t('tap_continue_to_proceed_to_the_main_dashboard')}
          </Text>
          <Button
            title={t('continue')}
            style={styles.continueBtn}
            onPress={() => {
              AsyncStorage.removeItem('documents');
              navigation.reset({
                index: 0,
                routes: [{name: 'BottomTab'}],
              });
            }}
          />
        </>
      );
    } else if (verificationStarted === 'VERIFICATION_PENDING') {
      return (
        <>
          <Text style={styles.tapText}>
            {t('tap_refresh_to_check_verification_status')}
          </Text>
          <Button
            title={t('refresh')}
            style={styles.continueBtn}
            onPress={handleRefresh}
          />
        </>
      );
    } else if (allDocumentsReady) {
      return (
        <>
          <Text style={styles.tapText}>
            {t('click_here_to_start_verification')}
          </Text>
          <Button
            title={t('start_verification')}
            style={styles.continueBtn}
            onPress={startVerification}
          />
        </>
      );
    } else {
      return (
        <>
          <Text
            style={[
              styles.tapText,
              {fontSize: sizes.h6, color: colors.lightGrey},
            ]}>
            {t('please_upload_documents_to_start_verification')}
          </Text>
        </>
      );
    }
  };

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.iconContainer}>
          {!verificationStarted && (
            <TouchableOpacity
              style={styles.icon}
              onPress={() =>
                navigation.navigate('Pp', {
                  fromWelcome: true,
                })
              }>
              <IconSvgView source={Back} />
            </TouchableOpacity>
          )}
          <FlexContainer direction="row" justifyContent="center">
            {[...Array(4)].map((_, index) => (
              <IconSvgView
                key={index}
                svgStyle={styles.diamondIcon}
                source={diamond}
              />
            ))}
          </FlexContainer>
          <TouchableOpacity
            ref={supportIconRef}
            style={styles.icon}
            onPress={toggleSupportMenu}
            accessibilityLabel="Get support">
            <IconSvgView size={30} source={support} />
          </TouchableOpacity>
        </View>

        {/* Main scrollable content */}
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled">
          <Text style={styles.title}>
            {t('welcome')} {driver?.name}
          </Text>
          <Text style={styles.subtitle}>{t('welcome_subtitle')}</Text>

          {documentsVerification && (
            <>
              {renderDocumentRow('rc', requiredDocuments.rc)}

              {renderDocumentRow(
                'license_number',
                requiredDocuments.license_number,
              )}
              {renderDocumentRow('license', requiredDocuments.license)}
              {renderDocumentRow(
                'profile_photo',
                requiredDocuments.profile_photo,
              )}
            </>
          )}

          <View style={{height: 80}} />
        </ScrollView>

        <View style={styles.fixedFooter}>{renderFooterButton()}</View>

        <SupportMenu
          visible={supportModalVisible}
          onClose={() => setSupportModalVisible(false)}
          position={iconPosition}
        />
      </SafeAreaView>
    </ImageBackground>
  );
};

export default Welcome;
