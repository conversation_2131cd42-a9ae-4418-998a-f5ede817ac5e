import {StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  safeArea: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: spacing.xl,
    position: 'relative',
  },

  scrollContent: {
    flexGrow: 1,
    paddingBottom: spacing.xxl,
  },

  fixedFooter: {
    width: '100%',
    position: 'absolute',
    bottom: 0,
    left: 20,
    right: 0,
    paddingTop: spacing.md,
    backgroundColor: colors.black,
    zIndex: 10,
  },

  iconContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: spacing.xxl,
  },

  icon: {
    marginTop: spacing.xl,
  },

  diamondIcon: {
    marginTop: spacing.xl,
    marginRight: spacing.xl,
  },

  title: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.largeTitle,
    color: colors.white,
    width: 300,
  },

  subtitle: {
    fontFamily: GeistFont.variable,
    fontSize: sizes.body,
    color: colors.grey,
    marginVertical: spacing.md,
  },

  list: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.h3,
    color: colors.white,
    marginTop: spacing.lg,
  },

  verification: {
    fontFamily: GeistFont.variable,
    marginBottom: spacing.lg,
    marginTop: spacing.sm,
    color: colors.red,
  },

  continueBtn: {
    marginBottom: spacing.xl,
    width: '100%',
  },

  rejectionMessage: {
    color: colors.red,
    marginBottom: spacing.md,
    fontFamily: GeistFont.bold,
  },

  documentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  documentInfo: {
    flex: 1,
  },

  noteText: {
    fontFamily: GeistFont.variable,
    fontSize: sizes.body,
    color: colors.yellow,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  tapText: {
    fontFamily: GeistFont.variable,
    fontSize: sizes.body / 1.1,
    color: colors.grey,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
});
