import React, {useEffect} from 'react';
import {
  Image,
  ImageBackground,
  NativeEventEmitter,
  NativeModules,
  Text,
  View,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import styles from './StartStyle';
import {images} from '../../constants';
import FadingLine from '../../components/FadingLine/FadingLine';
import Button from '../../components/Button/Button';
import {Trans, useTranslation} from 'react-i18next';
import HollowButton from '../../components/Button/HollowButton/HollowButton';
import {spacing} from '../../constants/theme';
import messaging from '@react-native-firebase/messaging';
import {useGeofence} from '../../hooks/useGeofence';

interface StartScreenProps {
  navigation: any;
  route: any;
}

const {LocationModule} = NativeModules;
const locationEventEmitter = new NativeEventEmitter(LocationModule);

const Start: React.FC<StartScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {stopGeofencing} = useGeofence();

  useEffect(() => {
    const performLogoutActivities = async () => {
      try {
        stopGeofencing();
        locationEventEmitter.removeAllListeners('locationUpdate');
        const LocationFetchTrigger = NativeModules.LocationModule;
        if (LocationFetchTrigger) {
          LocationFetchTrigger.stopService();
          LocationModule.stopEnhancedService();
          LocationModule.stopLocationService();
        }
        try {
          await messaging().deleteToken();
        } catch (err) {
          console.log('Error removing FCM token:', err);
        }
      } catch (error) {
        console.error('Error during logout activities:', error);
      }
    };

    performLogoutActivities();
  }, []);

  const handleRegister = () => {
    navigation.navigate('Language', {
      titleKey: 'choose_language',
      descriptionKey: 'set_your_preffered_language',
      buttonTextKey: 'continue',
      onConfirmAction: 'Phone',
      dropdownTitleKey: 'you_can_change_it_later',
    });
  };

  const handleLogin = () => {
    navigation.navigate('Phone', {action: 'login'});
  };

  return (
    <ImageBackground source={images.bg1} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <View style={{alignItems: 'center'}}>
          <View style={styles.titleContainer}>
            <FadingLine startX={1} startY={0} endX={0} endY={1} />
            <Text style={styles.welcomeText}>{t('welcome_to')}</Text>

            <FadingLine startX={0} startY={1} endX={1} endY={0} />
          </View>
          <Image
            resizeMode="contain"
            style={{width: 100, height: 100}}
            source={images.logo}></Image>
        </View>
        <View style={styles.imageContainer}>
          <Image
            resizeMode="contain"
            style={{width: 350, height: 350}}
            source={images.car}
            testID="car-image"
          />
        </View>
        {/* <Text>{tokens}</Text> */}
        <View>
          <Button onPress={handleLogin} title={t('login')} />
        </View>
        <View style={{marginVertical: spacing.lg}}>
          <HollowButton onPress={handleRegister} title={t('register')} />
          {/* <Trans
            i18nKey="terms_conditions"
            style={styles.footerText}
            parent={Text}>
            Trans <Text style={styles.termsText} />
          </Trans> */}
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default Start;
