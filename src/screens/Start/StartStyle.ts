import {StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, theme, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },
  safeArea: {
    flex: 1,
    paddingHorizontal: spacing.xl,
  },
  welcomeText: {
    fontFamily: GeistFont.regular,
    fontSize: sizes.body,
    textAlign: 'center',
    color: colors.white,
    margin: spacing.xl,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    marginHorizontal: spacing.xl,
    marginBottom: spacing.xl,
    fontSize: sizes.h5 * 3,
    color: colors.white,
    fontFamily: EBGaramondFont.EBbold,
  },
  imageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  footerText: {
    textAlign: 'center',
    color: colors.grey,
    fontSize: sizes.h3 / 2,
    fontFamily: GeistFont.regular,
    margin: spacing.xl,
  },
  termsText: {
    textAlign: 'center',
    textDecorationLine: 'underline',
  },
});
