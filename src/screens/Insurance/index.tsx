import React, {useCallback, useState, useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {useToast} from '../../components/Toast/Toast';
import FileUpload from '../../components/FileUpload/FileUpload';
import DocumentPicker, {
  DocumentPickerResponse,
  types,
} from 'react-native-document-picker';
import {Platform} from 'react-native';
import {useLoader} from '../../hooks/useLoader';
import DriverService from '../../services/DriverService';
import {useDriver} from '../../hooks/useDriver';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {STATUS_CODE} from '../../constants/constants';
import {useFocusEffect} from '@react-navigation/native';

interface DlProps {
  navigation: any;
  route: any;
}

const Insurance: React.FC<DlProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {showToast} = useToast();
  const {showLoader, hideLoader} = useLoader();
  const {fetchDriver} = useDriver();
  const {driver} = useDriver();
  const [insurance, setInsurance] = useState<DocumentPickerResponse[]>([]);
  const driverParam = route?.params?.driver || '';
  const fromWelcome = route?.params?.fromWelcome || false;
  const [isFileRemoved, setIsFileRemoved] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  const allowedMimeTypes = ['image/jpeg', 'image/png', 'application/pdf'];
  const allowedExtensions = ['jpg', 'jpeg', 'png', 'pdf'];
  const disallowedMimeTypes = ['video/mp4'];
  const disallowedExtensions = ['mp4'];

  useFocusEffect(
    useCallback(() => {
      (async () => {
        if (!isInitialized) {
          await fetchDriver();
          if (driver && driver.vehicle_insurance) {
            const updatedUri = `${
              driver.vehicle_insurance
            }?timestamp=${new Date().getTime()}`;
            const fileType = driver.vehicle_insurance
              .toLowerCase()
              .endsWith('.pdf')
              ? 'application/pdf'
              : 'image/jpeg';

            setInsurance([
              {
                uri: updatedUri,
                name:
                  fileType === 'application/pdf'
                    ? 'Insurance.pdf'
                    : 'Vehicle Insurance.jpg',
                type: fileType,
                fileCopyUri: updatedUri,
                size: 0,
              },
            ]);
            setIsInitialized(true);
          }
        }
      })();
    }, [isInitialized, driver?.vehicle_insurance]),
  );

  const isValidFile = (file: DocumentPickerResponse) => {
    const fileExtension = file.name
      ? file.name.split('.').pop()?.toLowerCase()
      : '';

    // Check if file is an MP4 or video file
    if (
      disallowedExtensions.includes(fileExtension || '') ||
      disallowedMimeTypes.includes(file.type || '')
    ) {
      showToast(t('mp4_not_supported'), 'failure');
      return false;
    }

    return (
      allowedExtensions.includes(fileExtension || '') &&
      allowedMimeTypes.includes(file.type || '')
    );
  };

  const handleDocumentSelection = useCallback(async () => {
    try {
      const response = await DocumentPicker.pick({
        presentationStyle: 'fullScreen',
        type: [types.images, types.pdf],
      });

      const validFiles = response.filter(isValidFile);

      if (validFiles.length === 0) {
        showToast(t('file_selection_error_invalid'), 'failure');
        return;
      }
      setIsFileRemoved(false);
      setInsurance(validFiles);
    } catch (err) {
      if (!DocumentPicker.isCancel(err)) {
        showToast(t('file_selection_error'), 'failure');
      }
    }
  }, []);

  // Camera functionality removed as requested

  const handleRemoveFile = useCallback((index: number) => {
    setIsFileRemoved(true);
    setInsurance(prevState => {
      const updatedFiles = [...prevState];
      updatedFiles.splice(index, 1);
      return updatedFiles;
    });
  }, []);

  const handleContinue = async () => {
    try {
      showLoader();
      if (insurance.length > 0) {
        const formData = new FormData();
        const uri =
          Platform.OS === 'android'
            ? insurance[0].uri
            : insurance[0].uri.replace('file://', '');
        formData.append('vi', {
          uri,
          name: insurance[0].name,
          type: insurance[0].type,
        });

        const response = await DriverService.update(formData);

        if (response) {
          if (driverParam) {
            await fetchDriver();
            AsyncStorage.removeItem('documents');
            navigation.reset({
              index: 0,
              routes: [{name: 'Welcome'}],
            });
            showToast(t('file_upload_success'), 'success');
          } else {
            showToast(t('file_upload_success'), 'success');
            navigation.navigate('Pp');
          }
        }
      } else {
        showToast(t('no_file_selected_error'), 'failure');
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err.response.data.response.code;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
      if (STATUS_CODE.bad_request === status) {
        code === 'update_driver_failed' &&
          showToast(t('file_size_exceeded'), 'failure');
      }
    } finally {
      hideLoader();
    }
  };

  const handleCancel = useCallback(() => {
    navigation.navigate('Document');
  }, [navigation]);

  const getBackButtonConfig = () => {
    if (fromWelcome || !driverParam) {
      return {
        backButtonAction: 'navigate' as const,
        backToScreen: 'Dl',
        backParams: {fromWelcome: true},
      };
    }
    return {
      backButtonAction: 'goBack' as const,
    };
  };

  useEffect(() => {
    return () => {
      setIsInitialized(false);
      setIsFileRemoved(false);
    };
  }, []);

  // Convert DocumentPickerResponse to FileResponse to fix type issues
  const convertToFileResponse = (docs: DocumentPickerResponse[]) => {
    return docs.map(doc => ({
      uri: doc.uri,
      type: doc.type,
      name: doc.name,
      size: doc.size,
      fileCopyUri: doc.fileCopyUri,
    }));
  };

  return (
    <FileUpload
      navigation={navigation}
      title={t('insurance')}
      subtitle={t('insurance_subtitle')}
      fileResponse={
        insurance.length > 0
          ? convertToFileResponse(insurance)
          : !isFileRemoved && driver?.vehicle_insurance
          ? [
              {
                uri: driver.vehicle_insurance,
                name: driver.vehicle_insurance.toLowerCase().endsWith('.pdf')
                  ? 'Insurance Document.pdf'
                  : 'Insurance Document.jpg',
                type: driver.vehicle_insurance.toLowerCase().endsWith('.pdf')
                  ? 'application/pdf'
                  : 'image/jpeg',
                fileCopyUri: driver.vehicle_insurance,
                size: 0,
              },
            ]
          : []
      }
      onContinue={handleContinue}
      onCancel={handleCancel}
      onDocumentSelection={handleDocumentSelection}
      // Camera option removed
      onRemoveFile={handleRemoveFile}
      driver={driverParam}
      validationMessage={t('file_validation_message', {
        formats: 'PNG, JPG, PDF',
        size: '5MB',
      })}
      {...getBackButtonConfig()}
    />
  );
};

export default Insurance;
