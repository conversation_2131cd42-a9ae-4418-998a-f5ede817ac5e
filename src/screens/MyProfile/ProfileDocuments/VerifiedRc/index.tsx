import React, {useState} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  Image,
  ImageBackground,
  ScrollView,
  Modal,
} from 'react-native';
import styles from './VerifiedRcStyle';
import {useTranslation} from 'react-i18next';
import back from '../../../../icons/back.svg';
import pdfIcon from '../../../../icons/pdf.svg';
import FlexContainer from '../../../../components/FlexContainer/FlexContainer';
import IconSvgView from '../../../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../../../components/FadingLine/FadingHorizontalLine';
import images from '../../../../constants/images';
import Button from '../../../../components/Button/Button';
import {useDriver} from '../../../../hooks/useDriver';
import {useLoader} from '../../../../hooks/useLoader';
import {useToast} from '../../../../components/Toast/Toast';
import {STATUS_CODE} from '../../../../constants/constants';
import DriverService from '../../../../services/DriverService';
import {useFocusEffect} from '@react-navigation/native';
import Input from '../../../../components/Input/Input';
import {SafeAreaView} from 'react-native-safe-area-context';

interface VerifiedRcScreenProps {
  navigation: any;
}

const VerifiedRc: React.FC<VerifiedRcScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {driver, fetchDriver} = useDriver();
  const [rc, setRc] = useState<string | null>(null);
  const [verifiedOn, setVerifiedOn] = useState<string | null>(null);
  const {showLoader, hideLoader} = useLoader();
  const [imageError, setImageError] = useState(false);
  const {showToast} = useToast();
  const [isPdf, setIsPdf] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const fetchRcDocument = async () => {
    showLoader();
    try {
      const response = await DriverService.getVerificationStatus();
      if (response.status === STATUS_CODE.ok) {
        const verifiedAt = response.data.data.rc_verified_at;
        setVerifiedOn(
          verifiedAt ? new Date(verifiedAt).toISOString().split('T')[0] : null,
        );
      }

      if (driver?.registration_certificate) {
        const baseUrl = driver.registration_certificate.split('?')[0];
        const isPdfFile = baseUrl.toLowerCase().endsWith('.pdf');
        const rcUrl = driver.registration_certificate.includes('?')
          ? `${
              driver.registration_certificate
            }&timestamp=${new Date().getTime()}`
          : `${
              driver.registration_certificate
            }?timestamp=${new Date().getTime()}`;
        setRc(rcUrl);
        setIsPdf(isPdfFile);
        setImageError(false);
      } else {
        setRc(null);
        setIsPdf(false);
        setImageError(true);
      }

    } catch (err: any) {
      console.error('Error fetching RC document:', err);
      setRc(null);
      setImageError(true);

      const status = err?.response?.status;
      if (![STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
      }
    } finally {
      hideLoader();
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      fetchRcDocument();
      return () => {
        setImageError(false);
      };
    }, [driver?.registration_certificate]),
  );

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  const renderRcPreview = () => {
    if (!rc) {
      return <Text style={styles.infoText}>{t('no_file_available')}</Text>;
    }

    if (isPdf) {
      return (
        <View style={styles.pdfContainer}>
          <IconSvgView source={pdfIcon} width={50} height={50} />
          <Text style={styles.fileName} numberOfLines={1}>
            {t('rc_document')}
          </Text>
        </View>
      );
    }

    if (imageError) {
      return <Text style={styles.infoText}>{t('error_displaying_rc')}</Text>;
    }

    return (
      <View style={styles.imageContainer}>
        {isLoading && (
          <Modal transparent>
            <View style={styles.loaderContainer}>
              <Image
                source={require('../../../../icons/LOADER.gif')}
                style={styles.loaderImage}
              />
            </View>
          </Modal>
        )}
        <Image
          source={{
            uri: rc,
            cache: 'reload',
            headers: {'Cache-Control': 'no-cache'},
          }}
          style={[styles.filePreviewImage, isLoading && styles.hiddenImage]}
          onError={handleImageError}
          onLoad={handleImageLoad}
          resizeMode="contain"
          accessibilityLabel={t('rc_image')}
        />
      </View>
    );
  };

  const handleChangeVehicle = () => {
    navigation.navigate('Rc', {driver});
  };

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.titleContainer}>
          <TouchableOpacity
            hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
            onPress={() => navigation.goBack()}
            accessibilityLabel={t('go_back')}>
            <IconSvgView source={back} />
          </TouchableOpacity>
          <Text style={styles.title}>{t('rc')}</Text>
        </View>

        <View style={{flex: 1}}>
          <View style={styles.fixedFadingLine}>
            <FadingHorizontalLine />
          </View>

          <ScrollView
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={true}>
            <View style={styles.card}>
              <Text style={styles.infoText}>{t('rc_verified')}</Text>
              <Text style={styles.infoText}>
                {t('verified_on')} {verifiedOn}
              </Text>
              <View style={styles.rcCard}>{renderRcPreview()}</View>
            </View>

            {/* <View style={styles.inputContainer}>
              <Input
                inputTitle={t('rc_expiry_date')}
                value={rcExpiry}
                editable={false}
                placeholder={t('expiry_date_placeholder')}
              />
            </View> */}
          </ScrollView>

          <View style={styles.fixedFooter}>
            <Button
              title={t('change_vehicle_details')}
              style={styles.continueBtn}
              onPress={handleChangeVehicle}
            />
          </View>
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default VerifiedRc;
