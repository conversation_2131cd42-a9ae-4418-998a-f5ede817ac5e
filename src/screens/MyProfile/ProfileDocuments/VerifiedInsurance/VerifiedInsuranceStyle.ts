import {Platform, StyleSheet, ViewStyle, Dimensions} from 'react-native';
import {spacing} from '../../../../constants/theme';
import {colors, EBGaramondFont, GeistFont, sizes} from '../../../../constants';

const {width, height} = Dimensions.get('window');
const isSmallDevice = width < 375;

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: spacing.xxl * 4,
  },

  backContainer: {
    position: 'absolute',
    top: 40,
    left: 20,
  },

  title: {
    position: 'absolute',
    top: 50,
    left: 20,
    marginVertical: spacing.xl,
    fontSize: sizes.h3,
    color: colors.white,
    fontFamily: EBGaramondFont.regular,
  },

  safeArea: {
    padding: spacing.md * 1.5,
    flex: 1,
  },

  card: {
    backgroundColor: colors.darkCharcoal,
    borderBottomLeftRadius: spacing.xs,
    borderBottomRightRadius: spacing.xs,
    padding: spacing.xl,
    marginTop: spacing.xxl,
    alignItems: 'center',
  },

  infoText: {
    color: colors.white,
    fontFamily: GeistFont.regular,
    fontSize: isSmallDevice ? sizes.h6 : sizes.h5,
    textAlign: 'center',
    marginVertical: spacing.xs,
  },

  continueBtn: Platform.select({
    ios: {
      marginBottom: spacing.xl,
      marginTop: spacing.xl,
    },
    android: {
      marginBottom: spacing.xl,
      marginTop: spacing.xl,
    },
  }) as ViewStyle,

  filePreviewImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },

  pdfContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.md,
    width: '100%',
    height: '100%',
  },

  fileName: {
    marginTop: spacing.xs,
    fontSize: sizes.body,
    color: colors.white,
    textAlign: 'center',
  },

  insuranceCard: {
    width: isSmallDevice ? 250 : 300,
    height: isSmallDevice ? 250 : 300,
    backgroundColor: colors.darkGrey,
    borderRadius: spacing.xs,
    marginTop: spacing.md,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },

  buttonContainer: {
    width: '100%',
    paddingHorizontal: spacing.xl,
    marginTop: spacing.xl,
  },
});
