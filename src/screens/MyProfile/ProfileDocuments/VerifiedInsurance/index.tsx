import React, {useState} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  Image,
  ImageBackground,
} from 'react-native';
import styles from './VerifiedInsuranceStyle';
import {useTranslation} from 'react-i18next';
import back from '../../../../icons/back.svg';
import pdfIcon from '../../../../icons/pdf.svg';
import FlexContainer from '../../../../components/FlexContainer/FlexContainer';
import IconSvgView from '../../../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../../../components/FadingLine/FadingHorizontalLine';
import images from '../../../../constants/images';
import {useDriver} from '../../../../hooks/useDriver';
import {useLoader} from '../../../../hooks/useLoader';
// import {useToast} from '../../../../components/Toast/Toast';
import {STATUS_CODE} from '../../../../constants/constants';
import DriverService from '../../../../services/DriverService';
import Button from '../../../../components/Button/Button';
import { useFocusEffect } from '@react-navigation/native';

interface VerifiedDlScreenProps {
  navigation: any;
}

const VerifiedInsurance: React.FC<VerifiedDlScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const [insurance, setInsurance] = useState<string | null>(null);
  const {driver} = useDriver();
  const {showLoader, hideLoader} = useLoader();
  // const {showToast} = useToast();
  const [verifiedOn, setVerifiedOn] = useState<string | null>(null);
  const [isPdf, setIsPdf] = useState(false);
  const [imageError, setImageError] = useState(false);

  useFocusEffect(
    React.useCallback(() => {
      const fetchInsurance = async () => {
        showLoader();
        try {
          if (driver?.vehicle_insurance) {
            setInsurance(driver.vehicle_insurance);
            setIsPdf(driver.vehicle_insurance.toLowerCase().endsWith('.pdf'));
          }

          const response = await DriverService.getVerificationStatus();
          if (response.status === STATUS_CODE.ok) {
            const verifiedAt = response.data.data.vehicle_insurance_verified_at;
            setVerifiedOn(
              verifiedAt
                ? new Date(verifiedAt).toISOString().split('T')[0]
                : null,
            );
          }
        } catch (err: any) {
          const status = err?.response?.status;
          const message = err?.response?.data?.message;
          if (
            [STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)
          ) {
            return;
          }
          if (message) {
            // showToast(message, 'failure');
          }
        } finally {
          hideLoader();
        }
      };
      fetchInsurance();
    }, [])
  );

  const handleImageError = () => {
    setImageError(true);
  };

  const renderInsurancePreview = () => {
    if (!insurance) {
      return <Text style={styles.infoText}>{t('no_insurance_available')}</Text>;
    }

    if (isPdf) {
      return (
        <View style={styles.pdfContainer}>
          <IconSvgView
            source={pdfIcon}
            width={50}
            height={50}
          />
          <Text style={styles.fileName} numberOfLines={1}>
            {t('insurance')}
          </Text>
        </View>
      );
    }

    if (!imageError) {
      return (
        <Image
          source={{uri: insurance}}
          style={styles.filePreviewImage}
          onError={handleImageError}
          resizeMode="contain"
        />
      );
    }

    return <Text style={styles.infoText}>{t('no_insurance_available')}</Text>;
  };

  return (
    <FlexContainer flex={1}>
      <ImageBackground source={images.bg2} style={styles.backgroundImage}>
        <View style={styles.titleContainer}>
          <View style={styles.backContainer}>
            <TouchableOpacity
              hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
              onPress={() => navigation.goBack()}>
              <IconSvgView source={back} />
            </TouchableOpacity>
          </View>
          <Text style={styles.title}>{t('insurance')}</Text>
        </View>
        <View style={styles.safeArea}>
          <FadingHorizontalLine />
          <View style={styles.card}>
            <Text style={styles.infoText}>{t('insurance_verified')}</Text>
            <Text style={styles.infoText}>
              {t('verified_on')} {verifiedOn}
            </Text>
            <View style={styles.insuranceCard}>
              {renderInsurancePreview()}
            </View>
          </View>
        </View>
        <View style={styles.buttonContainer}>
          <Button
            title={t('update')}
            style={styles.continueBtn}
            onPress={() => navigation.navigate('Insurance', {driver})}
          />
        </View>
      </ImageBackground>
    </FlexContainer>
  );
};


export default VerifiedInsurance;