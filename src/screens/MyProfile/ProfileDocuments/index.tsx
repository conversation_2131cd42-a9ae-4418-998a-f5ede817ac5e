import React from 'react';
import { View, TouchableOpacity, Text, ImageBackground, ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import FlexContainer from '../../../components/FlexContainer/FlexContainer';
import IconSvgView from '../../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../../components/FadingLine/FadingHorizontalLine';
import images from '../../../constants/images';
import back from '../../../icons/back.svg';
import chevronDown from '../../../icons/chevron-down.svg';
import useStyles from './ProfileDocumentsStyle'; 
import { useDriver } from '../../../hooks/useDriver';

interface ProfileDocumentsScreenProps {
  navigation: any;
}

const ProfileDocuments: React.FC<ProfileDocumentsScreenProps> = ({ navigation }) => {
  const { t } = useTranslation();
  const { driver } = useDriver();
  const styles = useStyles(); 

  // Format the ownership type to be more readable
  const formatOwnershipType = (type: string | undefined) => {
    if (!type) return '-';
    
    switch (type) {
      case 'SELF_OWNED':
        return t('self_owned');
      case 'RENTED':
        return t('rented');
      default:
        return type;
    }
  };

  // Format the fuel type to be more readable
  const formatFuelType = (type: string | undefined) => {
    if (!type) return '-';
    
    switch (type) {
      case 'PETROL':
        return t('petrol');
      case 'DIESEL':
        return t('diesel');
      case 'CNG':
        return t('cng');
      case 'ELECTRIC':
        return t('electric');
      default:
        return type;
    }
  };

  const handleNavigation = (screenName: string) => {
    if (screenName === 'DlNumber') {
      navigation.navigate(screenName, {
        driver,
        fromProfile: true,
      });
    } else {
      navigation.navigate(screenName, { driver });
    }
  };

  const renderListItem = (title: string, screenName: string) => (
    <TouchableOpacity onPress={() => handleNavigation(screenName)}>
      <View style={styles.listItem}>
        <Text style={styles.list}>{title}</Text>
        <IconSvgView source={chevronDown} />
      </View>
    </TouchableOpacity>
  );

  return (
    <FlexContainer flex={1}>
      <ImageBackground source={images.bg2} style={styles.backgroundImage}>
        <View style={styles.safeArea}>
          <View style={styles.titleContainer}>
            <TouchableOpacity
              hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
              onPress={() => navigation.goBack()}
            >
              <IconSvgView source={back} />
            </TouchableOpacity>
            <Text style={styles.title}>{t('documents')}</Text>
          </View>
          <FadingHorizontalLine />
          
          <ScrollView 
            showsVerticalScrollIndicator={true}
            contentContainerStyle={styles.scrollContent}
          >
            {renderListItem(t('rc_title'), 'VerifiedRc')}
            <FadingHorizontalLine />
            {renderListItem(t('dl_no'), 'DlNumber')}
            <FadingHorizontalLine />
            {renderListItem(t('dl'), 'VerifiedDl')}
            <FadingHorizontalLine />
            {renderListItem(t('pp'), 'ProfilePicture')}
            <FadingHorizontalLine />
            
            <View style={styles.detailsContainer}>
              <Text style={styles.detailsText}>{t('phone')}</Text>
              <Text style={styles.detailsText}>{driver?.phone ?? '-'}</Text>
            </View>
            <View style={styles.detailsContainer}>
              <Text style={styles.detailsText}>{t('gender')}</Text>
              <Text style={styles.detailsText}>{driver?.gender ?? '-'}</Text>
            </View>
            <View style={styles.detailsContainer}>
              <Text style={styles.detailsText}>{t('fuel_type')}</Text>
              <Text style={styles.detailsText}>
                {formatFuelType(driver?.vehicles?.[0]?.fuel_type)}
              </Text>
            </View>
            <View style={styles.detailsContainer}>
              <Text style={styles.detailsText}>{t('vehicle_ownership')}</Text>
              <Text style={styles.detailsText}>
                {formatOwnershipType(driver?.vehicles?.[0]?.ownership_type)}
              </Text>
            </View>
          </ScrollView>
        </View>
      </ImageBackground>
    </FlexContainer>
  );
};

export default ProfileDocuments;
