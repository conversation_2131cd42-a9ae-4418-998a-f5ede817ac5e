import { StyleSheet } from 'react-native';
import { spacing } from '../../../constants/theme';
import { EBGaramondFont, GeistFont, colors } from '../../../constants';
import { useFontSizes } from '../../../hooks/useFonts';

const useStyles = () => {
  const sizes = useFontSizes();

  return StyleSheet.create({
    backgroundImage: {
      flex: 1,
      width: '100%',
    },
    safeArea: {
      flex: 1,
      padding: spacing.xl,
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: spacing.xl,
    },
    title: {
      fontSize: sizes.h3,
      color: colors.lightGrey,
      fontFamily: EBGaramondFont.regular,
      marginLeft: spacing.xl,
    },
    list: {
      fontFamily: EBGaramondFont.regular,
      fontSize: sizes.h3, 
      fontWeight: '400',
      color: colors.white,
      marginVertical: spacing.lg,
      width: '80%',
    },
    listItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    detailsContainer: {
      backgroundColor: colors.davyGrey,
      marginTop: spacing.lg,
      padding: spacing.sm,
    },
    detailsText: {
      fontFamily: GeistFont.regular,
      fontSize: sizes.h6, 
      fontWeight: '400',
      color: colors.white,
      borderRadius: spacing.xs,
    },
    scrollContent: {
      flexGrow: 1,
      paddingBottom: spacing.xxl * 4,
      paddingTop: spacing.md,
    },
  });
};

export default useStyles;
