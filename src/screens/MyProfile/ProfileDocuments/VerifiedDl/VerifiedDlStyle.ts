import {Platform, StyleSheet, ViewStyle, Dimensions} from 'react-native';
import {spacing} from '../../../../constants/theme';
import {colors, EBGaramondFont, GeistFont, sizes} from '../../../../constants';

const {width, height} = Dimensions.get('window');
const isSmallDevice = width < 375;

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.xl,
  },
  title: {
    fontSize: sizes.h3,
    color: colors.lightGrey,
    fontFamily: EBGaramondFont.regular,
    marginLeft: spacing.xl,
    flexShrink: 1,
    flexWrap: 'wrap',
  },

  safeArea: {
    flex: 1,
    paddingHorizontal: spacing.xl,
  },

  card: {
    backgroundColor: colors.darkCharcoal,
    borderBottomLeftRadius: spacing.xs,
    borderBottomRightRadius: spacing.xs,
    padding: spacing.xl,
    marginTop: spacing.xxl,
    alignItems: 'center',
  },

  infoText: {
    color: colors.white,
    fontFamily: GeistFont.regular,
    fontSize: isSmallDevice ? sizes.h6 : sizes.h5,
    textAlign: 'center',
    marginVertical: spacing.xs,
  },

  continueBtn: Platform.select({
    ios: {
      marginBottom: spacing.xl,
      marginTop: spacing.xl,
    },
    android: {
      marginBottom: spacing.xl,
      marginTop: spacing.xl,
    },
  }) as ViewStyle,

  filePreviewImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },

  pdfContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.md,
    width: '100%',
    height: '100%',
  },

  fileName: {
    marginTop: spacing.xs,
    fontSize: sizes.body,
    color: colors.white,
    textAlign: 'center',
  },

  dlCard: {
    width: isSmallDevice ? 250 : 300,
    height: isSmallDevice ? 250 : 300,
    backgroundColor: colors.darkGrey,
    borderRadius: spacing.xs,
    marginTop: spacing.md,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },

  buttonContainer: {
    width: '100%',
    marginTop: spacing.xl,
    justifyContent: 'flex-end',
  },

  scrollContent: {
    flexGrow: 1,
    paddingBottom: spacing.xxl * 4,
    paddingTop: spacing.md,
  },

  fixedFadingLine: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 5,
    backgroundColor: colors.black,
  },

  fixedFooter: {
    width: '100%',
    backgroundColor: colors.black,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 10,
  },

  imageContainer: {
    position: 'relative',
    width: '100%',
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderImage: {
    width: 30,
    height: 30,
  },
  hiddenImage: {
    opacity: 0,
  },
});
