import React, {useEffect, useState} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  Image,
  ImageBackground,
  ScrollView,
  Modal,
} from 'react-native';
import styles from './VerifiedDlStyle';
import {useTranslation} from 'react-i18next';
import back from '../../../../icons/back.svg';
import pdfIcon from '../../../../icons/pdf.svg';
import IconSvgView from '../../../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../../../components/FadingLine/FadingHorizontalLine';
import images from '../../../../constants/images';
import {useDriver} from '../../../../hooks/useDriver';
import {useLoader} from '../../../../hooks/useLoader';
import {useToast} from '../../../../components/Toast/Toast';
import {STATUS_CODE} from '../../../../constants/constants';
import DriverService from '../../../../services/DriverService';
import Button from '../../../../components/Button/Button';
import {useFocusEffect} from '@react-navigation/native';
import {SafeAreaView} from 'react-native-safe-area-context';

interface VerifiedDlScreenProps {
  navigation: any;
}

const VerifiedDl: React.FC<VerifiedDlScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const [dl, setDl] = useState<string | null>(null);
  const {driver} = useDriver();
  const {showLoader, hideLoader} = useLoader();
  const {showToast} = useToast();
  const [verifiedOn, setVerifiedOn] = useState<string | null>(null);
  const [isPdf, setIsPdf] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useFocusEffect(
    React.useCallback(() => {
      const fetchDl = async () => {
        showLoader();
        try {
          if (driver?.driving_license) {
            const dlUrl = driver.driving_license.includes('?') 
              ? `${driver.driving_license}&timestamp=${new Date().getTime()}`
              : `${driver.driving_license}?timestamp=${new Date().getTime()}`;
          
            setDl(dlUrl);
            setIsPdf(driver.driving_license.toLowerCase().endsWith('.pdf'));
            setImageError(false); 
          } else {
            setDl(null);
            setImageError(true);
          }

          const response = await DriverService.getVerificationStatus();
          if (response.status === STATUS_CODE.ok) {
            const verifiedAt = response.data.data.license_verified_at;
            setVerifiedOn(
              verifiedAt
                ? new Date(verifiedAt).toISOString().split('T')[0]
                : null,
            );
          }
        } catch (err: any) {
          console.error('Error fetching driving license:', err);
          setDl(null);
          setImageError(true);
          
          const status = err?.response?.status;
          const message = err?.response?.data?.message;
          if (
            [STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)
          ) {
            return;
          }
          if (message) {
            // showToast(message, 'failure');
          }
        } finally {
          if (!dl) {
            hideLoader();
          }
        }
      };
      fetchDl();
      
      return () => {
        setImageError(false);
      };
    }, [driver?.driving_license]), 
  );

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  const renderDlPreview = () => {
    if (!dl) {
      return <Text style={styles.infoText}>{t('no_dl_available')}</Text>;
    }

    if (isPdf) {
      return (
        <View style={styles.pdfContainer}>
          <IconSvgView source={pdfIcon} width={50} height={50} />
          <Text style={styles.fileName} numberOfLines={1}>
            {t('dl_document')}
          </Text>
        </View>
      );
    }

    if (imageError) {
      return <Text style={styles.infoText}>{t('error_displaying_dl')}</Text>;
    }

    return (
      <View style={styles.imageContainer}>
        {isLoading && (
          <Modal transparent>
            <View style={styles.loaderContainer}>
              <Image
                source={require('../../../../icons/LOADER.gif')}
                style={styles.loaderImage}
              />
            </View>
          </Modal>
        )}
        <Image
          source={{
            uri: dl,
            cache: 'reload',
            headers: { 'Cache-Control': 'no-cache' }
          }}
          style={[styles.filePreviewImage, isLoading && styles.hiddenImage]}
          onError={handleImageError}
          onLoad={handleImageLoad}
          resizeMode="contain"
          accessibilityLabel={t('dl_image')}
        />
      </View>
    );
  };

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.titleContainer}>
          <View>
            <TouchableOpacity
              hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
              onPress={() => navigation.goBack()}>
              <IconSvgView source={back} />
            </TouchableOpacity>
          </View>
          <Text style={styles.title}>{t('dl')}</Text>
        </View>
        
        <View style={{flex: 1}}>
          <View style={styles.fixedFadingLine}>
            <FadingHorizontalLine />
          </View>
          
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={true}>
            <View style={styles.card}>
              <Text style={styles.infoText}>{t('dl_verified')}</Text>
              <Text style={styles.infoText}>
                {t('verified_on')} {verifiedOn}
              </Text>
              <View style={styles.dlCard}>{renderDlPreview()}</View>
            </View>
          </ScrollView>
          
          <View style={styles.fixedFooter}>
            <Button
              title={t('update')}
              style={styles.continueBtn}
              onPress={() => navigation.navigate('Dl', {driver})}
            />
          </View>
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default VerifiedDl;
