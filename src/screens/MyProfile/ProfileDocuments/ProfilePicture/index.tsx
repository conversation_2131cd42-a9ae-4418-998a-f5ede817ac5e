import React, {useState} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  Image,
  ImageBackground,
  ScrollView,
  Modal,
} from 'react-native';
import styles from './ProfilePictureStyle';
import {useTranslation} from 'react-i18next';
import back from '../../../../icons/back.svg';
import FlexContainer from '../../../../components/FlexContainer/FlexContainer';
import IconSvgView from '../../../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../../../components/FadingLine/FadingHorizontalLine';
import images from '../../../../constants/images';
import Button from '../../../../components/Button/Button';
import {useDriver} from '../../../../hooks/useDriver';
import {useLoader} from '../../../../hooks/useLoader';
import {useToast} from '../../../../components/Toast/Toast';
import {STATUS_CODE} from '../../../../constants/constants';
import DriverService from '../../../../services/DriverService';
import {useFocusEffect} from '@react-navigation/native';
import {SafeAreaView} from 'react-native-safe-area-context';

interface VerifiedRcScreenProps {
  navigation: any;
}

const ProfilePicture: React.FC<VerifiedRcScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {driver} = useDriver();
  const [profilePicture, setProfilePicture] = useState<string | null>(null);
  const {showLoader, hideLoader} = useLoader();
  const [imageError, setImageError] = useState(false);
  const {showToast} = useToast();
  const [verifiedOn, setVerifiedOn] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useFocusEffect(
    React.useCallback(() => {
      const fetchRc = async () => {
        showLoader();
        try {
          if (driver?.profile_photo) {
            const updatedUri = `${driver.profile_photo}?timestamp=${new Date().getTime()}`;
            setProfilePicture(updatedUri);
            
            const response = await DriverService.getVerificationStatus();
            console.log(response.data.data);

            if (response.status === STATUS_CODE.ok) {
              const verifiedAt = response.data.data.profile_photo_verified_at;
              setVerifiedOn(
                verifiedAt
                  ? new Date(verifiedAt).toISOString().split('T')[0]
                  : null,
              );
            }
          } else {
            setProfilePicture(null);
            setImageError(true); 
          }
        } catch (err: any) {
          console.error('Error fetching profile picture:', err);
          setImageError(true); 
          setProfilePicture(null);
          
          const status = err?.response?.status;
          const message = err?.response?.data?.message;
          if (
            [STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)
          ) {
            return;
          }
          if (message) {
            showToast(message, 'failure');
          }
        } finally {
          hideLoader();
        }
      };
      fetchRc();
      
      return () => {
        setImageError(false);
      };
    }, [driver?.profile_photo]), 
  );

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleImageError = () => {
    console.log('Profile image failed to load');
    setImageError(true);
    setIsLoading(false);
  };

  return (
    <FlexContainer flex={1}>
      <ImageBackground source={images.bg2} style={styles.backgroundImage}>
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.titleContainer}>
            <View>
              <TouchableOpacity
                hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
                onPress={() => navigation.goBack()}>
                <IconSvgView source={back} />
              </TouchableOpacity>
            </View>
            <Text style={styles.title}>{t('pp')}</Text>
          </View>

          <View style={{flex: 1}}>
            <View style={styles.fixedFadingLine}>
              <FadingHorizontalLine />
            </View>

            <ScrollView
              contentContainerStyle={styles.scrollContent}
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={true}>
              <View style={styles.card}>
                <Text style={styles.infoText}>{t('pp_verified')}</Text>
                <Text style={styles.infoText}>
                  {t('verified_on')} {verifiedOn}
                </Text>
                <View style={styles.rcCard}>
                  {profilePicture && !imageError ? (
                    <View style={styles.imageContainer}>
                      {isLoading && (
                        <Modal transparent>
                          <View style={styles.loaderContainer}>
                            <Image
                              source={require('../../../../icons/LOADER.gif')}
                              style={styles.loaderImage}
                            />
                          </View>
                        </Modal>
                      )}
                      <Image
                        source={{
                          uri: profilePicture,
                          cache: 'reload',
                          headers: { 'Cache-Control': 'no-cache' }
                        }}
                        style={[styles.filePreviewImage, isLoading && styles.hiddenImage]}
                        onError={handleImageError}
                        onLoad={handleImageLoad}
                        resizeMode="contain"
                      />
                    </View>
                  ) : (
                    <Text style={styles.infoText}>
                      {imageError ? t('error_displaying_image') : t('no_file_available')}
                    </Text>
                  )}
                </View>
              </View>
            </ScrollView>

            <View style={styles.fixedFooter}>
              <Button
                title={t('change_pp')}
                style={styles.continueBtn}
                onPress={() => navigation.navigate('Pp', {driver})}
              />
            </View>
          </View>
        </SafeAreaView>
      </ImageBackground>
    </FlexContainer>
  );
};

export default ProfilePicture;
