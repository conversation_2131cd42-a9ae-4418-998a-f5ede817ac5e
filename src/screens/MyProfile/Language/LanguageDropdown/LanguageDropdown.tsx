import React, {useCallback, useEffect, useState} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  ImageBackground,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import Dropdown from '../../../../components/DropDown/DropDown';
import FadingHorizontalLine from '../../../../components/FadingLine/FadingHorizontalLine';
import FlexContainer from '../../../../components/FlexContainer/FlexContainer';
import IconSvgView from '../../../../components/IconSvgView/IconSvgView';
import {images} from '../../../../constants';
import Button from '../../../../components/Button/Button';
import styles from '../LanguageStyle';
import back from '../../../../icons/back.svg';
import {useFocusEffect} from '@react-navigation/native';

interface DropdownItem {
  label: string;
  value: string;
}

interface GenericDropdownScreenProps {
  navigation: any;
  title: string;
  description: string;
  dropdownTitle: string;
  data: DropdownItem[];
  initialValue?: string;
  onConfirm: (selectedValue: string) => void;
  buttonText: string;
  backgroundImage?: any;
}

const LanguageDropdown: React.FC<GenericDropdownScreenProps> = ({
  navigation,
  title,
  description,
  dropdownTitle,
  data,
  initialValue,
  onConfirm,
  buttonText,
}) => {
  const [selectedValue, setSelectedValue] = useState<string | undefined>(
    initialValue,
  );

  useFocusEffect(
    useCallback(() => {
      setSelectedValue(initialValue);
    }, [initialValue]),
  );

  const selectedItem = data.find(item => item.value === selectedValue);

  return (
    <>
      <ImageBackground source={images.bg2} style={styles.backgroundImage}>
        <SafeAreaView style={styles.safeArea}>
          <ScrollView
            contentContainerStyle={{flexGrow: 1}}
            keyboardShouldPersistTaps="handled">
            <View style={styles.titleContainer}>
              <TouchableOpacity
                hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
                onPress={() => navigation.goBack()}>
                <IconSvgView source={back} />
              </TouchableOpacity>
              <Text style={styles.title}>{title}</Text>
            </View>
            <FadingHorizontalLine />
            <Text style={styles.list}>{description}</Text>
            <View>
              <Dropdown
                label={selectedItem?.label || ''}
                data={data}
                onSelect={(item: DropdownItem) => setSelectedValue(item.value)}
                title={dropdownTitle}
                dropdownStyle={{width: '100%'}}
                defaultValue={selectedValue || ''}
              />
            </View>
            <FlexContainer justifyContent="flex-end">
              <Button
                title={buttonText}
                disabled={!selectedValue}
                onPress={() => selectedValue && onConfirm(selectedValue)}
              />
            </FlexContainer>
          </ScrollView>
        </SafeAreaView>
      </ImageBackground>
    </>
  );
};

export default LanguageDropdown;
