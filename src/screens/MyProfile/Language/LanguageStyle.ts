import {Platform, StyleSheet, ViewStyle} from 'react-native';
import {spacing} from '../../../constants/theme';
import {EBGaramondFont, GeistFont, colors, sizes} from '../../../constants';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  titleContainer: {
    marginVertical: spacing.xxl,
    flexDirection: 'row',
    alignItems: 'center',
  },

  title: {
    fontSize: sizes.h3,
    color: colors.lightGrey,
    fontFamily: EBGaramondFont.regular,
    marginLeft: spacing.xl,
    flexShrink: 1,
    flexWrap: 'wrap',
  },

  safeArea: {
    flex: 1,
    padding: spacing.xl,
  },

  list: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.h3,
    fontWeight: '400',
    color: colors.white,
    marginTop: spacing.lg,
    minHeight: 40,
  },
});
