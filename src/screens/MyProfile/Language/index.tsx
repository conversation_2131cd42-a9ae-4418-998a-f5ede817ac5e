import React, {useCallback, useEffect, useState} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useTranslation} from 'react-i18next';
import LanguageDropdown from './LanguageDropdown/LanguageDropdown';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {useFocusEffect} from '@react-navigation/native';
const LANGUAGE_KEY = '@app_language';

type RootStackParamList = {
  Language: {
    titleKey?: string;
    descriptionKey?: string;
    buttonTextKey?: string;
    dropdownTitleKey?: string;
    onConfirmAction?: string;
  };
};

type Props = NativeStackScreenProps<RootStackParamList, 'Language'>;

const Language = ({navigation, route}: Props) => {
  const {t, i18n} = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState<string>(i18n.language);
  const {
    titleKey = 'language_settings',
    descriptionKey = 'app_language',
    buttonTextKey = 'update',
    onConfirmAction,
    dropdownTitleKey = 'set_language',
  } = route?.params || {};

  const title = t(titleKey);
  const description = t(descriptionKey);
  const buttonText = t(buttonTextKey);
  const dropdownTitle = t(dropdownTitleKey);

  const languageOptions = [
    {label: t('english'), value: 'en'},
    {label: t('malayalam'), value: 'ma'},
    {label: t('tamil'), value: 'ta'},
  ];

  useFocusEffect(
    useCallback(() => {
      const loadLanguage = async () => {
        try {
          const savedLang = await AsyncStorage.getItem(LANGUAGE_KEY);
          if (savedLang) {
            setCurrentLanguage(savedLang);
            i18n.changeLanguage(savedLang);
          }
        } catch (error) {
          console.error('Failed to load language:', error);
        }
      };
      loadLanguage();
    }, [i18n]), // Add i18n as a dependency
  );

  const handleConfirm = async (value: string) => {
    try {
      await AsyncStorage.setItem(LANGUAGE_KEY, value);
      await i18n.changeLanguage(value);
      
      if (onConfirmAction && typeof onConfirmAction === 'string') {
        navigation.navigate(onConfirmAction);
      } else {
        setTimeout(() => {
          navigation.goBack();
        }, 100);
      }
    } catch (error) {
      console.error('Failed to set language:', error);
    }
  };

  return (
    <LanguageDropdown
      key={`language-dropdown-${i18n.language}`} 
      navigation={navigation}
      title={title}
      description={description}
      dropdownTitle={dropdownTitle}
      data={languageOptions}
      initialValue={currentLanguage}
      onConfirm={handleConfirm}
      buttonText={buttonText}
    />
  );
};

export default Language;
