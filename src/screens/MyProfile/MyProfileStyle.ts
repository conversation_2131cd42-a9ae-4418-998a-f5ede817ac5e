import {Platform, StyleSheet, ViewStyle} from 'react-native';
import {sizes, EBGaramondFont, colors, theme, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },
  safeArea: {
    paddingHorizontal: spacing.md * 1.5,
  },
  headerContainer: {
    width: '100%',
    zIndex: 10,
  },
  titleContainer: {
    paddingTop: Platform.OS === 'ios' ? 50 : 20,
    paddingBottom: spacing.md,
    paddingHorizontal: spacing.md,
  },
  profileInfoContainer: {
    alignItems: 'center',
  },
  scrollContainer: {
    flex: 1,
  },
  title: {
    marginVertical: spacing.xl,
    fontSize: sizes.h3,
    color: colors.white,
    fontFamily: EBGaramondFont.regular,
  },
  imageContainer: {
    resizeMode: 'cover',
    borderWidth: spacing.xxs,
    borderColor: colors.white,
    marginTop: spacing.md,
    width: spacing.xl * 3,
    height: spacing.xl * 3,
  },
  driverName: {
    color: colors.white,
    fontSize: sizes.h5,
    fontFamily: GeistFont.regular,
    marginLeft: spacing.sm,
    marginBottom: spacing.md,
    marginTop: spacing.md,
  },
  subtitle: {
    backgroundColor: colors.darkGrey,
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  ratingText: {
    color: colors.white,
    fontSize: sizes.h3 / 2,
    fontFamily: GeistFont.regular,
    marginLeft: spacing.xs,
  },
  listTxt: {
    fontFamily: GeistFont.regular,
    fontSize: sizes.h5,
    color: colors.white,
    marginVertical: spacing.lg,
  },
  listItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  continueBtn: {
    marginHorizontal: spacing.xl,
    marginVertical: spacing.xl,
  },
  versionText: {
    color: colors.lightGrey,
    fontSize: sizes.body,
    fontFamily: GeistFont.regular,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  icon: {
    padding: spacing.sm,
  },
});
