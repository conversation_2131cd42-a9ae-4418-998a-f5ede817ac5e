import React, {useEffect, useState} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  Image,
  ImageBackground,
  Share,
} from 'react-native';
import styles from './IDCardStyle';
import {useTranslation} from 'react-i18next';
import back from '../../../icons/back.svg';
import FlexContainer from '../../../components/FlexContainer/FlexContainer';
import IconSvgView from '../../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../../components/FadingLine/FadingHorizontalLine';
import images from '../../../constants/images';
import HollowButton from '../../../components/Button/HollowButton/HollowButton';
import {useDriver} from '../../../hooks/useDriver';

interface IDCardScreenProps {
  navigation: {
    goBack: () => void;
  };
}

const IDCard: React.FC<IDCardScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {driver} = useDriver();
  const [imageError, setImageError] = useState(false);

  const handleSharePress = async () => {
    const message = `
      Here are the details of the driver:

      Name: ${driver?.name ?? '-'}
      Mobile Number: ${driver?.phone ?? '-'}
      Driving License No: ${driver?.driving_license_no ?? '-'}
      License Validity: ${driver?.license_expiry ?? '-'}
    `;

    try {
      await Share.share({
        message,
        title: t('id_card'),
      });
    } catch (error: any) {
      console.log('Error sharing:', error.message);
    }
  };

  return (
    <FlexContainer flex={1}>
      <ImageBackground source={images.bg2} style={styles.backgroundImage}>
        <View style={styles.safeArea}>
          <View style={styles.titleContainer}>
            <TouchableOpacity
              hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
              onPress={() => navigation.goBack()}>
              <IconSvgView source={back} />
            </TouchableOpacity>
            <Text style={styles.title}>{t('id_card')}</Text>
          </View>
          <FadingHorizontalLine />
          <View>
            <View style={styles.coverImagePlaceholder}>
              {/* Placeholder for cover image */}
            </View>
            <View style={styles.card}>
              <View style={styles.profileSection}>
                <Image
                  source={
                    imageError || !driver?.profile_photo
                      ? images.user
                      : {uri: driver.profile_photo}
                  }
                  style={styles.profileImage}
                  resizeMode="cover"
                  onError={() => setImageError(true)}
                />
                <Text style={styles.nameText}>{driver?.name ?? '-'}</Text>
              </View>

              <Text style={styles.label}>{t('mobile_number')}</Text>
              <Text style={styles.infoText}>{driver?.phone ?? '-'}</Text>

              <View style={styles.licenseContainer}>
                <View style={{width: '50%'}}>
                  <Text style={styles.label}>{t('dl_no')}</Text>
                  <Text style={styles.infoText}>
                    {driver?.driving_license_no ?? '-'}
                  </Text>
                </View>
                <View style={{width: '50%'}}>
                  <Text numberOfLines={2} style={styles.label}>
                    {t('license_validity')}
                  </Text>
                  <Text style={styles.infoText}>
                    {driver?.license_expiry
                      ? new Date(driver.license_expiry)
                          .toISOString()
                          .split('T')[0]
                      : '-'}
                  </Text>
                </View>
              </View>

              <HollowButton
                onPress={handleSharePress}
                title={t('share')}
                style={styles.shareBtn}
              />
            </View>
          </View>
        </View>
      </ImageBackground>
    </FlexContainer>
  );
};

export default IDCard;
