import {StyleSheet} from 'react-native';
import {spacing} from '../../../constants/theme';
import {EBGaramondFont, GeistFont, colors, sizes} from '../../../constants';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.xl,
  },
  title: {
    fontSize: sizes.h3,
    color: colors.lightGrey,
    fontFamily: EBGaramondFont.regular,
    marginLeft: spacing.xl,
    flexShrink: 1,
    flexWrap: 'wrap',
  },

  safeArea: {
    flex: 1,
    padding: spacing.xl,
  },

  coverImagePlaceholder: {
    borderTopLeftRadius: spacing.xs,
    borderTopRightRadius: spacing.xs,
    marginTop: spacing.xxl,
    height: spacing.xl * 6,
    backgroundColor: colors.lightGrey,
  },

  card: {
    backgroundColor: colors.darkCharcoal,
    borderBottomLeftRadius: spacing.xs,
    borderBottomRightRadius: spacing.xs,
    padding: spacing.xl,
  },

  profileSection: {
    marginTop: -spacing.xxl * 2,
  },

  profileImage: {
    width: spacing.xl * 4,
    height: spacing.xl * 4,
    borderRadius: spacing.xxs,
    borderColor: colors.grey,
    borderWidth: spacing.xxs,
    backgroundColor: colors.darkCharcoal,
  },

  nameText: {
    fontSize: sizes.h3,
    fontFamily: EBGaramondFont.regular,
    color: colors.lightGrey,
    marginBottom: spacing.md,
  },

  label: {
    color: colors.lightGrey,
    fontFamily: GeistFont.regular,
    fontSize: sizes.body,
    flexWrap: 'wrap',
  },

  infoText: {
    color: colors.grey,
    fontFamily: GeistFont.regular,
    fontSize: sizes.body,
    alignSelf: 'flex-start',
  },

  licenseContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: spacing.sm,
  },

  shareBtn: {
    marginTop: spacing.md,
  },
});
