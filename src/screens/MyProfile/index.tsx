import React, {useEffect, useState, useRef} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  Image,
  ImageBackground,
  NativeModules,
  NativeEventEmitter,
} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import images from '../../constants/images';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import styles from './MyProfileStyle';
import Button from '../../components/Button/Button';
import {useTranslation} from 'react-i18next';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import chevronDown from '../../icons/chevron-down.svg';
import support from '../../icons/support.svg';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import {spacing} from '../../constants/theme';
import {useDriver} from '../../hooks/useDriver';
import {useLoader} from '../../hooks/useLoader';
import DriverService from '../../services/DriverService';
import ConfirmationModal from '../../components/ConfirmationModal';
import {useToast} from '../../components/Toast/Toast';
import TripService from '../../services/TripService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ScrollView} from 'react-native-gesture-handler';
import messaging from '@react-native-firebase/messaging';
import {STATUS_CODE} from '../../constants/constants';
import {useAuth} from '../../hooks/useAuth';
import {useGeofence} from '../../hooks/useGeofence';
import RideService from '../../services/RideService';
import SupportMenu from '../../components/SupportMenu/SupportMenu';

interface MyProfileScreenProps {
  navigation: any;
}

const {LocationModule} = NativeModules;
const locationEventEmitter = new NativeEventEmitter(LocationModule);

const MyProfile: React.FC<MyProfileScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {driver, setDriver} = useDriver();
  const {showLoader, hideLoader} = useLoader();
  const {showToast} = useToast();
  const {stopGeofencing} = useGeofence();
  const {setIsAuthenticated} = useAuth();
  const [year, setYear] = useState<string>('0');
  const [orders, setOrders] = useState<string>('0');
  const [appVersion, setAppVersion] = useState<string>('');
  const [modalVisible, setModalVisible] = useState(false);
  const [modalAction, setModalAction] = useState<'logout' | 'delete'>();
  const [imageError, setImageError] = useState(false);
  const [supportModalVisible, setSupportModalVisible] = useState(false);
  const [iconPosition, setIconPosition] = useState<{
    x?: number;
    y?: number;
    width?: number;
    height?: number;
  } | null>(null);
  const supportIconRef = useRef(null);
  const [profileImageError, setProfileImageError] = useState(false);

  useEffect(() => {
    const getAppVersion = async () => {
      try {
        const version = await DeviceInfo.getVersion();
        setAppVersion(`${version}`);
      } catch (error) {
        console.error('Error getting app version:', error);
        setAppVersion('1.0.0');
      }
    };

    getAppVersion();

    const fetchDriverData = async () => {
      try {
        showLoader();
        if (driver?.created_at) {
          calculateYearsSince(driver?.created_at);
        }
        const response = await TripService.getDriverTrips('completed');
        if (response.status === STATUS_CODE.ok) {
          setOrders(response.data.data.trips.length);
        }
      } catch (err: any) {
        const status = err?.response?.status;
        if (
          [STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)
        ) {
          return;
        }
      } finally {
        hideLoader();
      }
    };

    fetchDriverData();
  }, []);

  const handleLogout = async () => {
    try {
      showLoader();
      stopGeofencing();
      const response = await RideService.sendDriverStatus({
        isActive: 0,
      });
      locationEventEmitter.removeAllListeners('locationUpdate');
      const LocationFetchTrigger = NativeModules.LocationModule;
      LocationFetchTrigger.stopService();
      LocationModule.stopEnhancedService();
      LocationModule.stopLocationService();
      await messaging().deleteToken();
      await AsyncStorage.clear();
      setModalVisible(false);
      setDriver(null);
      setIsAuthenticated(false);
      navigation.reset({
        index: 0,
        routes: [{name: 'Start'}],
      });
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err.response.data.response.code;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
      if (STATUS_CODE.bad_request === status) {
        code === 'status_update_failed' &&
          showToast(t('status_update_failed'), 'failure');
      }
    } finally {
      hideLoader();
    }
  };

  const handleDeleteAccount = async () => {
    try {
      showLoader();
      if (!driver?.id) {
        return;
      }
      const response = await DriverService.deleteDriver(driver?.id ?? null);
      if (response.status === STATUS_CODE.ok) {
        setModalVisible(false);
        // showToast(response.data.data.message, 'success');
        await AsyncStorage.clear();
        setDriver(null);
        setIsAuthenticated(false);
        navigation.reset({
          index: 0,
          routes: [{name: 'Start'}],
        });
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const message = err?.response?.data?.message;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
      if (message) {
        // showToast(message, 'failure');
      }
    } finally {
      hideLoader();
    }
  };

  const handleCancel = () => {
    setModalVisible(false);
  };

  const renderListItem = (title: string, onPress: () => void) => (
    <TouchableOpacity style={styles.listItem} onPress={onPress}>
      <Text style={styles.listTxt}>{title}</Text>
      <IconSvgView source={chevronDown} />
    </TouchableOpacity>
  );

  const calculateYearsSince = (dateString: string | number) => {
    if (!dateString) return;

    const createdAt = new Date(dateString);
    const now = new Date();
    const differenceInMilliseconds = now.getTime() - createdAt.getTime();
    const millisecondsPerYear = 1000 * 60 * 60 * 24 * 365.25;
    const years = differenceInMilliseconds / millisecondsPerYear;
    setYear(years.toFixed(1));
  };

  const toggleSupportMenu = () => {
    if (supportIconRef.current) {
      supportIconRef.current.measure(
        (
          _x: number,
          _y: number,
          width: number,
          height: number,
          pageX: number,
          pageY: number,
        ) => {
          setIconPosition({x: pageX, y: pageY, width, height});
          setSupportModalVisible(!supportModalVisible);
        },
      );
    } else {
      setSupportModalVisible(!supportModalVisible);
    }
  };

  const handleProfileImageError = () => {
    setProfileImageError(true);
  };

  return (
    <>
      <ImageBackground source={images.bg2} style={styles.backgroundImage}>
        <View style={styles.headerContainer}>
          <View style={styles.titleContainer}>
            <View style={styles.titleRow}>
              <Text style={styles.title}>{t('profile')}</Text>
              <TouchableOpacity
                ref={supportIconRef}
                style={styles.icon}
                onPress={toggleSupportMenu}
                accessibilityLabel="Get support">
                <IconSvgView size={30} source={support} />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.profileInfoContainer}>
            {driver?.profile_photo && !profileImageError ? (
              <Image
                source={{
                  uri: `${
                    driver.profile_photo
                  }?timestamp=${new Date().getTime()}`,
                  cache: 'reload',
                  headers: {'Cache-Control': 'no-cache'},
                }}
                style={styles.imageContainer}
                onError={handleProfileImageError}
                resizeMode="cover"
              />
            ) : (
              <View style={styles.profileImagePlaceholder}>
                <Text style={styles.profileImagePlaceholderText}>
                  {driver?.name ? driver.name.charAt(0).toUpperCase() : ''}
                </Text>
              </View>
            )}
            <Text style={styles.driverName}>{driver?.name ?? '-'}</Text>
          </View>

          <View style={styles.subtitle}>
            <FlexContainer
              flex={1}
              direction="row"
              justifyContent="space-between"
              style={{paddingHorizontal: spacing.lg}}>
              <FlexContainer alignItems="center">
                <FlexContainer alignItems="center" direction="row">
                  {driver?.average_rating == '0' ? (
                    ''
                  ) : (
                    <Image source={images.star} />
                  )}
                  <Text style={styles.ratingText}>
                    {driver?.average_rating == '0'
                      ? t('no')
                      : driver?.average_rating}
                  </Text>
                </FlexContainer>
                <Text style={styles.ratingText}>{t('rating')}</Text>
              </FlexContainer>
              <FlexContainer alignItems="center">
                <Text style={styles.ratingText}>{orders ?? 0}</Text>
                <Text style={styles.ratingText}>{t('rides')}</Text>
              </FlexContainer>
              <FlexContainer alignItems="center">
                <Text style={styles.ratingText}>{year}</Text>
                <Text style={styles.ratingText}>{t('years')}</Text>
              </FlexContainer>
            </FlexContainer>
          </View>
        </View>

        <ScrollView style={styles.scrollContainer}>
          <View style={styles.safeArea}>
            {renderListItem(t('id_card'), () => navigation.navigate('IDCard'))}
            <FadingHorizontalLine />
            {renderListItem(t('documents'), () =>
              navigation.navigate('ProfileDocuments'),
            )}
            <FadingHorizontalLine />
            {renderListItem(t('my_performance'), () =>
              navigation.navigate('Performance'),
            )}
            <FadingHorizontalLine />
            {renderListItem(t('language_settings'), () =>
              navigation.navigate('Language'),
            )}
            <FadingHorizontalLine />
            {renderListItem(t('wallet'), () => navigation.navigate('Wallet'))}
            <FadingHorizontalLine />
            {renderListItem(t('add_upi'), () =>
              navigation.navigate('AddQr', {MyProfile: 'MyProfile'}),
            )}
            <FadingHorizontalLine />
          </View>

          <View style={{justifyContent: 'flex-end'}}>
            <Button
              title={t('logout')}
              style={styles.continueBtn}
              onPress={() => {
                setModalAction('logout');
                setModalVisible(true);
              }}
            />
            <Text style={styles.versionText}>
              {t('version')} {appVersion}
            </Text>
          </View>
        </ScrollView>
      </ImageBackground>

      <ConfirmationModal
        title={modalAction === 'delete' ? t('delete_account') : t('logout')}
        visible={modalVisible}
        message={
          modalAction === 'delete'
            ? 'Are you sure you want to delete this account?'
            : t('sure_logout')
        }
        onConfirm={
          modalAction === 'delete' ? handleDeleteAccount : handleLogout
        }
        onCancel={handleCancel}
      />

      <SupportMenu
        visible={supportModalVisible}
        onClose={() => setSupportModalVisible(false)}
        position={iconPosition}
      />
    </>
  );
};

export default MyProfile;
