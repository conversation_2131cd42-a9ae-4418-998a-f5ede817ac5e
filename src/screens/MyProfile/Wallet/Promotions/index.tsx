import React, {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ImageBackground,
  StyleSheet,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import back from '../../../../icons/back.svg';
import styles from './PromotionsStyle';
import {SafeAreaView} from 'react-native-safe-area-context';
interface PromotionItem {
  cashbackEarned: string;
  type: string;
  endDate: string;
  name: string;
  id: string;
  title: string;
  description: string;
  code: string;
  expiryDate: string;
}

interface DriverConfigOffer {
  title: string;
  subtitle: string;
  description?: string;
  timer?: {
    startTime?: string;
    endTime?: string;
  };
  isDisabled?: boolean;
  timeActive?: boolean; // Indicates if offer is currently within time range
}

import {NativeStackScreenProps} from '@react-navigation/native-stack';
import images from '../../../../constants/images';
import IconSvgView from '../../../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../../../components/FadingLine/FadingHorizontalLine';
import FlexContainer from '../../../../components/FlexContainer/FlexContainer';
import {colors, GeistFont} from '../../../../constants';
import LinearGradient from 'react-native-linear-gradient';
import {spacing} from '../../../../constants/theme';

type RootStackParamList = {
  Promotions: {
    promotions?: PromotionItem[];
    driverConfigOffer?: DriverConfigOffer;
  };
};

type PromotionsProps = NativeStackScreenProps<RootStackParamList, 'Promotions'>;

const Promotions = ({navigation, route}: PromotionsProps) => {
  const {t} = useTranslation();
  const [loading, setLoading] = useState(true);
  const promotions = route.params.promotions;
  const driverConfigOffer = route.params.driverConfigOffer;
  console.log("Promotions:", promotions);
  console.log("Driver config offer:", driverConfigOffer);

  const formatDate = (isoString: string | number | Date) => {
    const date = new Date(isoString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const year = date.getFullYear();

    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12 || 12;

    return `${day}-${month}-${year} ${hours}:${minutes} ${ampm}`;
  };
  const renderPromotion = (item: PromotionItem) => (
    <View key={item.id} style={styles.promotionCard}>
      <Text style={styles.promotionTitle}>
        {item.type === 'driver_referral'
          ? t('driver_referral_title')
          : t('welcome_bonus_title')}
      </Text>
      <Text style={styles.promotionDescription}>
        {item.type === 'driver_referral'
          ? t('driver_referral_description')
          : t('welcome_bonus_description')}
      </Text>
      {/* <View style={styles.codeContainer}>
        <Text style={styles.codeText}>{item.code}</Text>
      </View> */}
      <Text style={styles.expiryText}>
        {t('expires')}: {formatDate(item.endDate)}
      </Text>
    </View>
  );

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.titleContainer}>
          <TouchableOpacity
            hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
            onPress={() => navigation.goBack()}>
            <IconSvgView source={back} width={24} height={24} />
          </TouchableOpacity>
          <Text style={styles.title}>{t('promotions')}</Text>
        </View>
        <FadingHorizontalLine />

        <ScrollView style={styles.content}>
          {/* Render driver config offer if available */}
          {driverConfigOffer && (
            <View 
              key="driver-config-offer" 
              style={[
                styles.promotionCard,
                driverConfigOffer.isDisabled && { backgroundColor: '#3A3A3A' }
              ]}>
              <Text 
                style={[
                  styles.promotionTitle,
                  driverConfigOffer.isDisabled && { color: '#CCCCCC' }
                ]}>
                {driverConfigOffer.title}
              </Text>
              {driverConfigOffer.subtitle && (
                <Text 
                  style={[
                    styles.promotionDescription,
                    driverConfigOffer.isDisabled && { color: '#BBBBBB' }
                  ]}>
                  {driverConfigOffer.subtitle}
                </Text>
              )}
              {driverConfigOffer.description && (
                <Text 
                  style={[
                    styles.promotionDescription,
                    driverConfigOffer.isDisabled && { color: '#BBBBBB' }
                  ]}>
                  {driverConfigOffer.description}
                </Text>
              )}
              {(driverConfigOffer.timer || driverConfigOffer.isDisabled) && (
                <Text 
                  style={[
                    styles.expiryText,
                    driverConfigOffer.isDisabled && { color: '#BBBBBB' }
                  ]}>
                  {/* Show "Starts at" when outside time range but "Valid time" when disabled by backend */}
                  {driverConfigOffer.timeActive === false ? `${t('starts_at')}: ` : 
                   driverConfigOffer.isDisabled ? `${t('starts_at')}: ` : `${t('valid_time')}: `}
                  {`${driverConfigOffer.timer?.startTime || ''} ${driverConfigOffer.timer?.startTime && driverConfigOffer.timer?.endTime ? ' - ' : ''} ${driverConfigOffer.timer?.endTime || ''}`}
                </Text>
              )}
            </View>
          )}

          {/* Render regular promotions */}
          {promotions && promotions.length > 0 ? (
            promotions.map(renderPromotion)
          ) : !driverConfigOffer ? (
            <FlexContainer justifyContent="center" alignItems="center">
              <Text
                style={{
                  color: colors.lightGrey,
                  fontFamily: GeistFont.regular,
                }}>
                {t('no_promotions_available')}
              </Text>
            </FlexContainer>
          ) : null}
        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
};

// No need for additional styles as we're using the same styles as regular promotions

export default Promotions;
