import {StyleSheet} from 'react-native';
import { spacing } from '../../../../constants/theme';
import { colors, EBGaramondFont, GeistFont, sizes } from '../../../../constants';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },
  
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.15)',
    padding: 8,
    borderRadius: 20,
    marginRight: spacing.sm,
  },

  titleContainer: {
    marginBottom: spacing.xl,
    flexDirection: 'row',
    alignItems: 'center',
  },

  title: {
    fontSize: sizes.h3,
    color: colors.lightGrey,
    fontFamily: EBGaramondFont.regular,
    marginLeft: spacing.xl,
    flex: 1,
  },

  safeArea: {
    flex: 1,
    padding: spacing.xl,
  },

  content: {
    flex: 1,
    marginTop: spacing.lg,
  },
  promotionCard: {
    backgroundColor: colors.darkCharcoal,
    borderRadius: spacing.xs,
    padding: spacing.lg,
    marginBottom: spacing.lg,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  promotionTitle: {
    fontSize: sizes.h5,
    fontFamily: GeistFont.regular,
    color: colors.white,
    marginBottom: spacing.sm,
  },
  promotionDescription: {
    fontSize: sizes.body,
    fontFamily: GeistFont.bold,
    color: colors.white,
    marginBottom: spacing.md,
  },
  codeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  codeText: {
    fontSize: sizes.h5,
    fontFamily: GeistFont.regular,
    color: colors.green,
  },
  applyButton: {
    width: 100,
  },
  expiryText: {
    fontSize: sizes.h6,
    fontFamily: GeistFont.regular,
    color: colors.lightGrey,
  },
});
