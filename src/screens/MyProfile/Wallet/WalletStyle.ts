import {StyleSheet} from 'react-native';
import {
  colors,
  EBGaramondFont,
  GeistFont,
  sizes,
} from '../../../constants/fonts';
import {spacing} from '../../../constants/theme';

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: '100%',
  },
  safeArea: {
    flex: 1,
    padding: spacing.xl,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: spacing.xl,
    marginTop: spacing.xxl * 2,
    marginBottom: spacing.xl,
  },
  title: {
    fontSize: sizes.h3,
    color: colors.lightGrey,
    fontFamily: EBGaramondFont.regular,
    marginLeft: spacing.xl,
  },
  walletContainer: {
    backgroundColor: colors.darkCharcoal,
    borderRadius: spacing.xs,
    padding: spacing.md,
    alignItems: 'center',
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  walletLabel: {
    fontSize: sizes.h5,
    color: colors.white,
    fontFamily: GeistFont.regular,
    marginBottom: spacing.lg,
  },
  balanceDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.xl,
  },
  strikethroughAmount: {
    fontSize: sizes.h1,
    color: colors.lightGrey,
    fontFamily: GeistFont.regular,
    textDecorationLine: 'line-through',
    marginRight: spacing.lg,
  },
  actualAmount: {
    fontSize: sizes.h1,
    color: colors.white,
    fontFamily: GeistFont.regular,
  },
  walletInfoContainer: {
    backgroundColor: colors.white,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    width: '100%',
    marginBottom: 16,
  },
  walletInfoText: {
    fontSize: 14,
    color: colors.white,
    fontFamily: GeistFont.regular,
  },
  noteTxt: {
    fontSize: sizes.body,
    marginVertical: spacing.md,
    fontFamily: GeistFont.regular,
    color: colors.lightGrey,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.xl,
  },
  transactionsTitle: {
    fontSize: sizes.h4,
    color: colors.white,
    marginVertical: spacing.lg,
    fontFamily: GeistFont.bold,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#2C2C2E',
  },
  transactionDetails: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: sizes.body,
    color: colors.white,
    marginBottom: 4,
  },
  transactionDate: {
    fontSize: sizes.body - 2,
    color: colors.grey,
    marginBottom: 2,
  },
  transactionExpiry: {
    fontSize: sizes.body,
    color: colors.lightGrey,
  },
  transactionAmount: {
    fontSize: sizes.body,
    fontWeight: '600',
    color: colors.white,
  },
  positiveAmount: {
    color: '#00C853',
  },
  seeAllButton: {
    alignItems: 'center',
    paddingVertical: spacing.md,
    marginTop: spacing.md,
  },
  seeAllText: {
    fontSize: sizes.body,
    color: colors.white,
    fontFamily: GeistFont.bold,
  },
  paymentMethodText: {
    fontSize: sizes.body,
    color: colors.white,
    fontFamily: GeistFont.regular,
  },
  chevronRight: {
    fontSize: 24,
    color: colors.grey,
  },
  transactionsCard: {
    backgroundColor: colors.darkCharcoal,
    borderRadius: spacing.xs,
    padding: spacing.md,
    marginBottom: spacing.xl,
  },
  paymentMethodsSection: {
    marginVertical: spacing.md,
  },
  paymentMethodsTitle: {
    fontSize: sizes.h4,
    fontFamily: GeistFont.bold,
    color: colors.white,
    marginBottom: spacing.md,
  },
  paymentMethod: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.darkCharcoal,
    borderRadius: spacing.xs,
    padding: spacing.md,
    marginBottom: spacing.md,
  },
  promotionsCard: {
    backgroundColor: colors.darkCharcoal,
    borderRadius: spacing.xs,
    marginBottom: spacing.lg,
    overflow: 'hidden',
    padding: spacing.md,
  },
  promotionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.md,
  },
  promotionsListInner: {
    padding: spacing.md,
  },
  promotionItem: {
    paddingVertical: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  noPromotionsText: {
    fontSize: sizes.h5,
    fontFamily: GeistFont.regular,
    color: colors.lightGrey,
    textAlign: 'center',
    padding: spacing.md,
  },
  promotionTitle: {
    fontSize: sizes.body,
    fontFamily: GeistFont.regular,
    color: colors.white,
  },
  disabledPromotion: {
    opacity: 0.7,
  },
  disabledText: {
    color: colors.lightGrey,
  },
  startTimeText: {
    fontSize: sizes.body,
    fontFamily: GeistFont.regular,
    color: colors.white,
    marginLeft: spacing.sm,
  },
});

export default styles;
