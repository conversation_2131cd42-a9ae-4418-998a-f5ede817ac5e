import React, {useEffect, useState} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  ImageBackground,
  ScrollView,
  Image,
  StyleSheet,
} from 'react-native';
import styles from './WalletStyle';
import {useTranslation} from 'react-i18next';
import back from '../../../icons/back.svg';
import chevronDown from '../../../icons/dropChevron.svg';
import chevronSide from '../../../icons/chevron-down.svg';
import FlexContainer from '../../../components/FlexContainer/FlexContainer';
import IconSvgView from '../../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../../components/FadingLine/FadingHorizontalLine';
import images from '../../../constants/images';
import HollowButton from '../../../components/Button/HollowButton/HollowButton';
import {useDriver} from '../../../hooks/useDriver';
import DriverService from '../../../services/DriverService';
import WalletService, {
  WalletTransaction,
} from '../../../services/WalletService';
import {spacing} from '../../../constants/theme';
import {useLoader} from '../../../hooks/useLoader';
import {useToast} from '../../../components/Toast/Toast';
import {colors} from '../../../constants/fonts';
import LinearGradient from 'react-native-linear-gradient';

interface IDCardScreenProps {
  navigation: {
    goBack: () => void;
    navigate: (screenName: string, params?: any) => void;
  };
}

const Wallet: React.FC<IDCardScreenProps> = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const {driver} = useDriver();
  const [walletTransactions, setWalletTransactions] = useState<
    WalletTransaction[] | null
  >(null);
  const [dailyCommission, setDailyCommission] = useState<number>(0);
  const [walletBalance, setWalletBalance] = useState<number>(0);
  const {showLoader, hideLoader} = useLoader();
  const {showToast} = useToast();
  const [promotions, setPromotions] = useState<any>();
  const [showPromotions, setShowPromotions] = useState<boolean>(false);
  const [driverConfigOffers, setDriverConfigOffers] = useState<any>(null);

  useEffect(() => {
    // Initial fetch
    getMinimumWalletBalance();
    fetchWalletTransactions();
    fetchPromotions();

    // Set up a periodic refresh of offer data (every 2 minutes)
    const refreshInterval = setInterval(() => {
      getMinimumWalletBalance(); // This function also fetches and processes offer data
    }, 120000); // 2 minutes
    
    return () => {
      clearInterval(refreshInterval);
    };
  }, []);

  const fetchPromotions = async () => {
    const response = await WalletService.getPromotions();
    setPromotions(response.data.data);
  };

  const fetchWalletTransactions = async () => {
    try {
      showLoader();
      const response = await WalletService.getWalletTransactions();
      if (response.data && response.data.data) {
        setWalletTransactions(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching wallet balance:', error);
    } finally {
      hideLoader();
    }
  };

  const getMinimumWalletBalance = async () => {
    try {
      const response = await DriverService.getDriverConfig();
      const walletResponse = await WalletService.getWallet();

      // Get daily commission
      const minimumWallet = response.data.data.find(
        (item: any) => item.key === 'driver_daily_commission',
      );

      // Get offers from driver config
      const offerConfig = response.data.data.find(
        (item: any) => item.key === 'offers',
      );

      const minValue = parseFloat(minimumWallet.value);
      const currentBalance = parseFloat(walletResponse.data.data.totalBalance);

      setDailyCommission(minValue);
      setWalletBalance(currentBalance);

      if (offerConfig && offerConfig.value) {
        processOfferData(offerConfig.value);
      }
    } catch (error) {
      console.error('Error fetching driver config:', error);
    }
  };

  const processOfferData = (offerData: any) => {
    try {
      const data =
        typeof offerData === 'string' ? JSON.parse(offerData) : offerData;

      // Check if offers are enabled
      const isOfferEnabled = data && data.enabled;

      // Get the current language from i18n
      const currentLanguage = i18n.language;
      let langKey = currentLanguage;

      // Fallback to English if the current language isn't supported
      if (!data[langKey]) {
        langKey = 'en';
      }

      // Get language-specific offer content
      const offerContent = data[langKey];
      if (!offerContent) {
        setDriverConfigOffers(null);
        return;
      }

      // Get timer data from either the language-specific content or the root data
      const timerData = offerContent.timer || data.timer;

      // Set the offer details with isDisabled flag
      setDriverConfigOffers({
        ...offerContent,
        isDisabled: !isOfferEnabled,
        timer: timerData
      });

      // If offer is disabled from backend, don't show it in the list
      if (!isOfferEnabled) {
        setDriverConfigOffers(null);
        return;
      }
      
      // If we have timer data, check if the offer is currently active or not
      if (timerData && timerData.startTime && timerData.endTime) {
        const isActive = checkIfOfferTimeIsActive(timerData);
        
        // Store offer with timeActive property (outside time range = disabled style)
        setDriverConfigOffers({
          ...offerContent,
          isDisabled: !isActive, // Use disabled style when outside time range
          timer: timerData,
          timeActive: isActive
        });
      }

      // If we have timer data, check if the offer is currently active or not
      if (timerData && timerData.startTime && timerData.endTime) {
        const isActive = checkIfOfferTimeIsActive(timerData);
        
        // Store offer with timeActive property (outside time range = disabled style)
        setDriverConfigOffers({
          ...offerContent,
          isDisabled: !isActive, // Use disabled style when outside time range
          timer: timerData,
          timeActive: isActive
        });
      }
    } catch (error) {
      console.error('Error processing offer data:', error);
      setDriverConfigOffers(null);
    }
  };

  const navigateToDriverConfigOffer = () => {
    if (driverConfigOffers) {
      navigation.navigate('Promotions', {
        driverConfigOffer: driverConfigOffers,
      });
    }
  };

  const formatDate = (isoString: string | number | Date) => {
    const date = new Date(isoString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12 || 12;

    return `${day}-${month}-${year} ${hours}:${minutes} ${ampm}`;
  };

  const renderTransaction = (transaction: any) => {
    let title = '';
    let amountText = '';

    if (transaction.type === 'credit') {
      if (transaction.source === 'driver_referral') {
        title = t('driver_referral');
      } else if (transaction.source === 'welcome') {
        title = t('welcome');
      } else {
        title = `#${transaction.source}`;
      }
      amountText = `+₹${transaction.amount}`;
    } else if (transaction.type === 'debit') {
      if (transaction.source === 'commission') {
        title = t('commission');
      } else {
        title = `#${transaction.source}`;
      }
      amountText = `- ₹${transaction.amount}`;
    }

    return (
      <View key={transaction.id} style={styles.transactionItem}>
        <View style={styles.transactionDetails}>
          <Text style={styles.transactionTitle}>{title}</Text>
          <Text style={styles.transactionDate}>
            {formatDate(transaction.createdAt)}
          </Text>
        </View>
        <Text
          style={[
            styles.transactionAmount,
            transaction.type === 'credit' ? styles.positiveAmount : {},
          ]}>
          {amountText}
        </Text>
      </View>
    );
  };

  const togglePromotions = () => {
    setShowPromotions(!showPromotions);
  };

  // Navigate to Promotions screen with the specific promotion
  const navigateToPromotion = (promotion: any) => {
    navigation.navigate('Promotions', {promotions: [promotion]});
  };

  // Helper function to check if the current time is within the offer time range
  const checkIfOfferTimeIsActive = (timerData: {
    startTime: string;
    endTime: string;
  }): boolean => {
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinutes = now.getMinutes();
    const currentTimeMinutes = currentHour * 60 + currentMinutes;

    // Parse start and end time to minutes since midnight
    const startTimeParts = timerData.startTime.split(':');
    const startHour = parseInt(startTimeParts[0], 10);
    const startMinutes = parseInt(startTimeParts[1], 10);
    const startTimeMinutes = startHour * 60 + startMinutes;

    const endTimeParts = timerData.endTime.split(':');
    const endHour = parseInt(endTimeParts[0], 10);
    const endMinutes = parseInt(endTimeParts[1], 10);
    const endTimeMinutes = endHour * 60 + endMinutes;

    let isTimeToShowOffer = false;

    // Handle both cases: normal time range (e.g., 9:00-17:00) and overnight time range (e.g., 22:00-05:00)
    if (startTimeMinutes < endTimeMinutes) {
      // Normal time range
      isTimeToShowOffer =
        currentTimeMinutes >= startTimeMinutes &&
        currentTimeMinutes < endTimeMinutes;
    } else {
      // Overnight time range
      isTimeToShowOffer =
        currentTimeMinutes >= startTimeMinutes ||
        currentTimeMinutes < endTimeMinutes;
    }

    return isTimeToShowOffer;
  };

  return (
    <FlexContainer flex={1}>
      <ImageBackground source={images.bg2} style={styles.backgroundImage}>
        <View style={styles.titleContainer}>
          <View>
            <TouchableOpacity
              hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
              onPress={() => navigation.goBack()}>
              <IconSvgView source={back} />
            </TouchableOpacity>
          </View>
          <Text style={styles.title}>{t('wallet')}</Text>
        </View>
        <FadingHorizontalLine />

        <ScrollView
          style={styles.safeArea}
          showsVerticalScrollIndicator={false}>
          <View style={styles.walletContainer}>
            <Text style={styles.walletLabel}>{t('daily_commission')}</Text>
            <View style={styles.balanceDisplay}>
              {/* <Text style={styles.strikethroughAmount}>₹10</Text> */}
              <Text style={styles.actualAmount}>₹{dailyCommission}</Text>
            </View>
            <Text style={styles.walletLabel}>{t('balance')}</Text>
            <Text style={styles.actualAmount}>₹{walletBalance}</Text>
          </View>
          <Text style={styles.noteTxt}>{t('wallet_note')}</Text>

          <View>
            <HollowButton
              style={{marginVertical: spacing.xl}}
              title={t('add_money')}
              disabled={true}
              onPress={() => {}}
            />
          </View>
          <FadingHorizontalLine />

          {walletTransactions && walletTransactions.length > 0 && (
            <>
              <Text style={styles.transactionsTitle}>
                {t('recent_transactions')}
              </Text>
              <View style={styles.transactionsCard}>
                {walletTransactions?.slice(0, 3).map(renderTransaction)}
                <FadingHorizontalLine />
                <TouchableOpacity
                  style={styles.seeAllButton}
                  onPress={() =>
                    navigation.navigate('AllTransactions', {walletTransactions})
                  }>
                  <Text style={styles.seeAllText}>{t('see_all')}</Text>
                </TouchableOpacity>
              </View>
            </>
          )}

          <View style={styles.paymentMethodsSection}>
            <Text style={styles.paymentMethodsTitle}>{t('promotions')}</Text>

            <View style={styles.promotionsCard}>
              {/* Show driver config offers if available */}
              {driverConfigOffers && (
                <>
                  {driverConfigOffers.isDisabled ? (
                    <TouchableOpacity
                      onPress={navigateToDriverConfigOffer}
                      style={[styles.promotionItem, styles.disabledPromotion]}>
                      <Text
                        style={[styles.promotionTitle, styles.disabledText]}>
                        {driverConfigOffers.title}
                      </Text>
                      <IconSvgView
                        source={chevronSide}
                        svgStyle={{opacity: 0.6}}
                      />
                    </TouchableOpacity>
                  ) : (
                    <TouchableOpacity
                      onPress={navigateToDriverConfigOffer}
                      style={styles.promotionItem}>
                      <Text style={styles.promotionTitle}>
                        {driverConfigOffers.title}
                      </Text>
                      <IconSvgView source={chevronSide} />
                    </TouchableOpacity>
                  )}
                  {promotions &&
                    Array.isArray(promotions) &&
                    promotions.length > 0 && <FadingHorizontalLine />}
                </>
              )}

              {/* Show regular promotions */}
              {promotions && Array.isArray(promotions) && promotions.length > 0
                ? promotions.map((item: any, index: number) => (
                    <React.Fragment key={item.id}>
                      <TouchableOpacity
                        onPress={() => navigateToPromotion(item)}
                        style={styles.promotionItem}>
                        <Text style={styles.promotionTitle}>
                          {item.type === 'driver_referral'
                            ? t('driver_referral_title')
                            : t('welcome_bonus_title')}
                        </Text>
                        <IconSvgView source={chevronSide} />
                      </TouchableOpacity>
                      {index < promotions.length - 1 && (
                        <FadingHorizontalLine />
                      )}
                    </React.Fragment>
                  ))
                : !driverConfigOffers && (
                    <Text style={styles.noPromotionsText}>
                      {t('no_promotions')}
                    </Text>
                  )}
            </View>
          </View>
          <View style={{height: 20}} />
        </ScrollView>
      </ImageBackground>
    </FlexContainer>
  );
};

// Additional styles for the offer card
const offerStyles = StyleSheet.create({
  offerContainer: {
    marginVertical: spacing.md,
  },
  offerCardGradient: {
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.lg,
  },
  offerCardTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  offerCardDescription: {
    color: '#FFFFFF',
    fontSize: 14,
    marginBottom: spacing.sm,
  },
  offerCardFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: spacing.xs,
  },
  offerCardButton: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  offerCardButtonText: {
    color: '#E5574D',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default Wallet;
