import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ImageBackground,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import back from '../../../icons/back.svg';
import styles from './WalletStyle';
import {SafeAreaView} from 'react-native-safe-area-context';
import IconSvgView from '../../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../../components/FadingLine/FadingHorizontalLine';
import images from '../../../constants/images';
import {spacing} from '../../../constants/theme';

const AllTransactions: React.FC<{navigation: any; route: any}> = ({
  navigation,
  route,
}) => {
  const {t} = useTranslation();
  const transactions = route.params.walletTransactions;

  const formatDate = (isoString: string | number | Date) => {
    const date = new Date(isoString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12 || 12;

    return `${day}-${month}-${year} ${hours}:${minutes} ${ampm}`;
  };

  const renderTransaction = (transaction: any) => {
    let title = '';
    let amountText = '';

    if (transaction.type === 'credit') {
      if (transaction.source === 'driver_referral') {
        title = t('driver_referral');
      } else if (transaction.source === 'welcome') {
        title = t('welcome');
      } else {
        title = `#${transaction.source}`;
      }
      amountText = `+₹${transaction.amount}`;
    } else if (transaction.type === 'debit') {
      if (transaction.source === 'commission') {
        title = t('commission');
      } else {
        title = `#${transaction.source}`;
      }
      amountText = `- ₹${transaction.amount}`;
    }

    return (
      <View key={transaction.id} style={styles.transactionItem}>
        <View style={styles.transactionDetails}>
          <Text style={styles.transactionTitle}>{title}</Text>
          {transaction.createdAt && (
            <Text style={styles.transactionExpiry}>
              {t('credited')}: {formatDate(transaction.createdAt)}
            </Text>
          )}
          {transaction.remarks && title !== t('commission') && (
            <Text style={styles.transactionExpiry}>{transaction.remarks}</Text>
          )}
        </View>
        <Text
          style={[
            styles.transactionAmount,
            transaction.type === 'credit' ? styles.positiveAmount : {},
          ]}>
          {amountText}
        </Text>
      </View>
    );
  };

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      {/* Header */}
      <View style={styles.titleContainer}>
        <TouchableOpacity
          style={styles.backButton}
          hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
          onPress={() => navigation.goBack()}>
          <IconSvgView source={back} width={24} height={24} />
        </TouchableOpacity>
        <Text style={styles.title}>{t('transactions')}</Text>
      </View>
      <FadingHorizontalLine />
      <ScrollView style={styles.content}>
        {transactions.map((item: any, index: any) => (
          <React.Fragment key={item.id}>
            {renderTransaction(item)}
            {index < transactions.length - 1 && <FadingHorizontalLine />}
          </React.Fragment>
        ))}
      </ScrollView>
    </ImageBackground>
  );
};

export default AllTransactions;
