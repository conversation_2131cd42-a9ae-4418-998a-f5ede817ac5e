import React, {useState} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  Image,
  ImageBackground,
} from 'react-native';
import styles from './PerformanceStyle';
import {useTranslation} from 'react-i18next';
import back from '../../../icons/back.svg';
import FlexContainer from '../../../components/FlexContainer/FlexContainer';
import IconSvgView from '../../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../../components/FadingLine/FadingHorizontalLine';
import images from '../../../constants/images';
import Dropdown from '../../../components/DropDown/DropDown';
import Button from '../../../components/Button/Button';

interface PerformanceScreenProps {
  navigation: any;
}

const Performance: React.FC<PerformanceScreenProps> = ({navigation}) => {
  const {t} = useTranslation();

  return (
    <>
      <FlexContainer flex={1}>
        <ImageBackground source={images.bg2} style={styles.backgroundImage}>
          <View style={styles.safeArea}>
            <View style={styles.titleContainer}>
              <TouchableOpacity
                hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
                onPress={() => navigation.goBack()}>
                <IconSvgView source={back} />
              </TouchableOpacity>
              <Text style={styles.title}>{t('my_performance')}</Text>
            </View>
            <FadingHorizontalLine />
            <View style={styles.performanceCard}>
              <Text style={styles.acceptText}>{t('accept_20_orders')}</Text>
            </View>
          </View>
        </ImageBackground>
      </FlexContainer>
    </>
  );
};

export default Performance;
