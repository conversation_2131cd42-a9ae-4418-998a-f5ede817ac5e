import {Platform, StyleSheet, ViewStyle} from 'react-native';
import {spacing} from '../../../constants/theme';
import {EBGaramondFont, GeistFont, colors, sizes} from '../../../constants';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: '100%',
  },
  safeArea: {
    flex: 1,
    padding: spacing.xl,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.xxl,
  },
  title: {
    fontSize: sizes.h3,
    color: colors.lightGrey,
    fontFamily: EBGaramondFont.regular,
    marginLeft: spacing.xl,
  },
  performanceCard: {
    backgroundColor: colors.white,
    padding: spacing.xxl * 3,
    marginTop: spacing.xl,
  },
  acceptText: {
    fontFamily: GeistFont.regular,
    fontSize: sizes.h6,
    fontWeight: '400',
    color: colors.grayishBlack,
    marginTop: spacing.sm,
    textAlign: 'center',
  },
  list: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.h3,
    fontWeight: '400',
    color: colors.white,
    marginTop: spacing.lg,
  },
  detailsContainer: {
    backgroundColor: colors.davyGrey,
    marginTop: spacing.lg,
    padding: spacing.sm,
  },

  continueBtn: {
    marginBottom: spacing.xl,
  },
});
