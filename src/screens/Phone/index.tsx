import React, {useState, useEffect, useReducer, useCallback} from 'react';
import {
  ImageBackground,
  Text,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  View,
  Keyboard,
  TouchableWithoutFeedback,
  Linking,
  TouchableOpacity,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {images} from '../../constants';
import styles from './PhoneStyle';
import Input from '../../components/Input/Input';
import Button from '../../components/Button/Button';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import diamond from '../../icons/diamond.svg';
import diamondInactive from '../../icons/diamondBlack.svg';
import {useTranslation} from 'react-i18next';
import {SafeAreaView} from 'react-native-safe-area-context';
import DriverService from '../../services/DriverService';
import {STATUS_CODE} from '../../constants/constants';
import {useLoader} from '../../hooks/useLoader';
import {spacing} from '../../constants/theme';
import PolicyService, {Policy} from '../../services/PolicyService';
import {useToast} from '../../components/Toast/Toast';

interface PhoneScreenProps {
  navigation: any;
  route?: any;
}

const Phone: React.FC<PhoneScreenProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {showToast} = useToast();
  const [mobile, setMobile] = useState('+91 ');
  const login = route?.params?.action;
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [, forceUpdate] = useReducer(x => x + 1, 0);
  const [policies, setPolicies] = useState<Policy[]>([]);
  const [receiveWhatsAppUpdates, setReceiveWhatsAppUpdates] = useState(true);

  const mobileToSend = mobile.replace(/\s/g, '');
  const {showLoader, hideLoader} = useLoader();

  const handleTermsPress = useCallback(async () => {
    try {
      const response = await PolicyService.getPolicies();
      if (response.data && response.data.data) {
        const policies = response.data.data;

        const termsItem = policies.find(
          policy => policy.name.toLowerCase() === 'terms',
        );

        if (termsItem && termsItem.link) {
          Linking.openURL(termsItem.link).catch(() => {
            showToast(t('could_not_open_link'), 'failure');
          });
        } else {
          navigation.navigate('Performance');
        }
      }
    } catch (err) {
      console.error('Error fetching policies:', err);
      showToast(t('could_not_open_link'), 'failure');
    }
  }, [navigation, showToast, t]);

  const checkPhoneNumber = async (phoneNumber: string) => {
    try {
      showLoader();
      const response = await DriverService.findDriver(phoneNumber);
      if (response.data.data) {
        setErrorMessage('');
        navigation.reset({
          index: 0,
          routes: [
            {
              name: 'Otp',
              params: {
                mobile: mobileToSend,
                whatsappPermission: receiveWhatsAppUpdates,
              },
            },
          ],
        });
      } else {
        setErrorMessage(t('user_not_registered'));
      }
    } catch (err: any) {
      const status = err?.response?.status;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        navigation.replace('Fallback');
      }
    } finally {
      hideLoader();
    }
  };

  const handlePress = async () => {
    if (login) {
      await checkPhoneNumber(mobileToSend);
    } else {
      navigation.reset({
        index: 0,
        routes: [
          {
            name: 'Otp',
            params: {
              mobile: mobileToSend,
              whatsappPermission: receiveWhatsAppUpdates,
            },
          },
        ],
      });
    }
  };

  const handleChange = (input: string) => {
    if (!input.startsWith('+91 ')) {
      setMobile('+91 ');
      setErrorMessage(''); // clear error
      return;
    }

    const numberPart = input.substring(4);
    const digitsOnly = numberPart.replace(/[^0-9]/g, '');

    if (digitsOnly.length <= 10) {
      setMobile('+91 ' + digitsOnly);
      setErrorMessage(''); // clear error
    }
  };

  const isMobileNumberValid = /^\+91\s[0-9]{10}$/.test(mobile);

  useEffect(() => {
    const loadWhatsAppPreference = async () => {
      try {
        const savedPreference = await AsyncStorage.getItem(
          'receiveWhatsAppUpdates',
        );
        if (savedPreference !== null) {
          setReceiveWhatsAppUpdates(JSON.parse(savedPreference));
        }
      } catch (error) {
        console.error('Error loading WhatsApp preference:', error);
      }
    };

    loadWhatsAppPreference();
  }, []);

  const toggleWhatsAppUpdates = useCallback(async () => {
    const newValue = !receiveWhatsAppUpdates;
    setReceiveWhatsAppUpdates(newValue);

    try {
      await AsyncStorage.setItem(
        'receiveWhatsAppUpdates',
        JSON.stringify(newValue),
      );
    } catch (error) {
      console.error('Error saving WhatsApp preference:', error);
    }
  }, [receiveWhatsAppUpdates]);

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 20}
            style={{flex: 1}}>
            <View style={styles.iconContainer}>
              <IconSvgView svgStyle={styles.diamondIcon} source={diamond} />
              <IconSvgView
                svgStyle={styles.diamondIcon}
                source={diamondInactive}
              />
              {!login && (
                <>
                  <IconSvgView
                    svgStyle={styles.diamondIcon}
                    source={diamondInactive}
                  />
                  <IconSvgView
                    svgStyle={styles.diamondIcon}
                    source={diamondInactive}
                  />
                </>
              )}
            </View>

            <ScrollView
              contentContainerStyle={{flexGrow: 1, paddingBottom: 100}}
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}>
              <View>
                <Text style={styles.title}>{t('trip_ready')}</Text>
              </View>
              <Input
                inputTitle={t('enter_mobile')}
                onChange={handleChange}
                value={mobile}
                keyboardType="numeric"
                error={errorMessage}
                maxLength={14}
                blurOnSubmit={false}
                autoFocus={false}
              />
            </ScrollView>

            <View style={styles.buttonContainer}>
              {!login && (
                <View style={styles.whatsappCheckboxContainer}>
                  <TouchableOpacity
                    style={styles.checkbox}
                    onPress={toggleWhatsAppUpdates}>
                    <View
                      style={[
                        styles.checkboxInner,
                        receiveWhatsAppUpdates
                          ? styles.checkboxChecked
                          : styles.checkboxUnchecked,
                      ]}>
                      {receiveWhatsAppUpdates && (
                        <Text style={styles.checkmark}>✓</Text>
                      )}
                    </View>
                  </TouchableOpacity>
                  <Text style={styles.checkboxLabel}>
                    {t('receive_whatsapp_updates')}
                  </Text>
                </View>
              )}

              <Button
                title={t('continue')}
                onPress={handlePress}
                disabled={!isMobileNumberValid}
              />
              <View style={styles.termsContainer}>
                <Text
                  style={styles.termsText}
                  adjustsFontSizeToFit={true}
                  numberOfLines={2}>
                  {t('by_clicking_continue_you_agree_to_our')}{' '}
                  <Text
                    style={styles.termsHighlight}
                    onPress={handleTermsPress}>
                    {t('terms_conditions')}
                  </Text>
                </Text>
              </View>
            </View>
          </KeyboardAvoidingView>
        </TouchableWithoutFeedback>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default Phone;
