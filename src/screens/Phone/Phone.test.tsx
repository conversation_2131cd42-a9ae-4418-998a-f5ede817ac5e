import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import Phone from '.';

describe('Phone component', () => {
    it('renders the Phone component correctly', async () => {
        const { getByText, getByPlaceholderText } = render(<Phone navigation={{}} />);

        await waitFor(() => {
            expect(getByText("let’s get you")).toBeTruthy();
            expect(getByText("trip-ready")).toBeTruthy();
        });
    });


    it('does not navigate when "Continue" button is pressed with an invalid mobile number', async () => {
        const navigationMock = { navigate: jest.fn() };
        const { getByText } = render(<Phone navigation={navigationMock} />);

        fireEvent.press(getByText("Continue"));

        await waitFor(() => {
            expect(navigationMock.navigate).not.toHaveBeenCalled();
        });
    });
});
