import {StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  safeArea: {
    flex: 1,
    justifyContent: 'flex-start',
    padding: spacing.xl,
    paddingTop: spacing.xxl,
  },

  iconContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: spacing.xxl,
  },

  diamondIcon: {
    marginRight: spacing.xl,
  },

  title: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.largeTitle,
    fontWeight: '400',
    color: colors.white,
  },

  continueBtn: {
    marginTop: spacing.xl,
  },

  referralTitle: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.h5,
    color: colors.white,
    marginTop: spacing.lg,
  },

  successMessage: {
    fontFamily: GeistFont.regular,
    fontSize: sizes.body,
    color: colors.green,
    marginTop: spacing.sm,
    padding: spacing.sm,
    backgroundColor: 'rgba(0, 255, 0, 0.1)',
    borderRadius: spacing.xs,
  },
  termsContainer: {
    marginTop: spacing.md,
    alignItems: 'center',
  },
  termsText: {
    fontFamily: GeistFont.regular,
    fontSize: sizes.body,
    color: colors.grey,
    textAlign: 'center',
  },
  termsHighlight: {
    fontFamily: GeistFont.bold,
    fontSize: sizes.body,
    color: colors.lightGrey,
    textDecorationLine: 'underline',
  },
  buttonContainer: {
    marginTop: spacing.xl,
  },
  whatsappCheckboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    justifyContent: 'center',
    alignSelf: 'center',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  checkbox: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxInner: {
    width: 20,
    height: 20,
    borderRadius: spacing.xs,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.white,
  },
  checkboxUnchecked: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.grey,
  },
  checkmark: {
    color: colors.black,
    fontSize: sizes.body,
    fontWeight: 'bold',
  },
  checkboxLabel: {
    marginLeft: spacing.sm,
    color: colors.white,
    fontFamily: 'GeistVariableVF',
    fontSize: sizes.body,
    textAlign: 'center',
  },
});
