import { Platform, StyleSheet, TextStyle, ViewStyle } from 'react-native';
import { sizes, EBGaramondFont, colors, GeistFont } from '../../constants';
import { spacing } from '../../constants/theme';

export default StyleSheet.create({
    backgroundImage: {
        flex: 1,
        resizeMode: 'cover',
    },
    title: {
        margin: spacing.md,
        fontSize: sizes.h4,
        color: colors.white,
        fontFamily: GeistFont.regular,
        textAlign: 'center'
    },
    rateBtn: {
        marginVertical: spacing.xl,
    },
    safeArea: {
        flex: 1,
        paddingHorizontal: spacing.xl,
    },
    starContainer: {
        flexDirection: 'row',
    },
    star: {
        fontSize: sizes.h4 * 2,
        color: colors.white,
        marginHorizontal: spacing.sm,
    },
    riderName: {
        margin: spacing.md,
        fontSize: sizes.h2,
        color: colors.white,
        fontFamily: EBGaramondFont.EBbold,
        textAlign: 'center'
    }
});
