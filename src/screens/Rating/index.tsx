import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ImageBackground } from 'react-native';
import styles from './RatingStyle.ts'
import { SafeAreaView } from 'react-native-safe-area-context';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import images from '../../constants/images';
import close from '../../icons/close.svg';
import Button from '../../components/Button/Button.tsx';
import { useTranslation } from 'react-i18next';


interface RatingScreenProps {
  navigation: any;
  route: any
}

const Rating: React.FC<RatingScreenProps> = ({ navigation }) => {
  const { t } = useTranslation();
  const [rating, setRating] = useState<number>(0);
  const maxRating = 5;

  const handleRating = (rate: number) => {
    setRating(rate);
  };

  const renderStars = () => {
    return Array.from({ length: maxRating }, (_, index) => {
      return (
        <TouchableOpacity
          key={index}
          onPress={() => handleRating(index + 1)}
        >
          <Text style={styles.star}>
            {index < rating ? '★' : '☆'}
          </Text>
        </TouchableOpacity>
      );
    });
  };

  return (
    <View style={{ flex: 1, justifyContent: 'flex-end' }}>
      <FlexContainer flex={0.4}>
        <ImageBackground source={images.bg2} style={styles.backgroundImage}>
          <SafeAreaView style={styles.safeArea}>
            <FlexContainer direction='row' alignItems='center' justifyContent='space-between'>
              <View style={{ flex: 1, alignItems: 'center' }}>
                <Text style={styles.title}>{t('rate_rider_title')}</Text>
              </View>
              {/* <View style={{ position: 'absolute', top: 50, right: 20 }}> */}
              <TouchableOpacity onPress={() => navigation.reset({
                index: 0,
                routes: [{ name: 'BottomTab' }],
              })}>
                <IconSvgView width={14} source={close} />
              </TouchableOpacity>
              {/* </View> */}
            </FlexContainer>
            <Text style={styles.riderName}>John</Text>
            <View style={styles.starContainer}>{renderStars()}</View>
            <Button
              style={styles.rateBtn}
              title={t('rate_rider')}
            // onPress={handleRideAccepted}
            />
          </SafeAreaView>
        </ImageBackground>
      </FlexContainer>
    </View >
  );
};

export default Rating;
