import React, {useCallback, useEffect, useMemo, useState, useRef} from 'react';
import {
  Image,
  ImageBackground,
  Text,
  TouchableOpacity,
  View,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Keyboard,
  Dimensions,
} from 'react-native';
import {colors, images} from '../../constants';
import styles from './VehicleStyle';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import Input from '../../components/Input/Input';
import Button from '../../components/Button/Button';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import Back from '../../icons/back.svg';
import diamond from '../../icons/diamond.svg';
import support from '../../icons/support.svg';
import {useTranslation} from 'react-i18next';
import {SafeAreaView} from 'react-native-safe-area-context';
import {STATUS_CODE, vehiclesData} from '../../constants/constants';
import DriverService from '../../services/DriverService';
import {useLoader} from '../../hooks/useLoader';
import {useToast} from '../../components/Toast/Toast';
import {useDriver} from '../../hooks/useDriver';
import {useFocusEffect} from '@react-navigation/native';
import SupportMenu from '../../components/SupportMenu/SupportMenu';
import Dropdown from '../../components/DropDown/DropDown';

interface VehicleScreenProps {
  navigation: any;
  route: any;
}

interface Vehicle {
  id: string;
  name: string;
  backendName: string;
  image: any;
  seats: string;
  rate: string;
  disabled?: boolean;
}

const Vehicle: React.FC<VehicleScreenProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {showLoader, hideLoader} = useLoader();
  const {showToast} = useToast();
  const {driver, fetchDriver} = useDriver();
  const [error, setError] = useState<string>('');
  const [vehicleNo, setVehicleNo] = useState('');
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [supportModalVisible, setSupportModalVisible] = useState(false);
  const [iconPosition, setIconPosition] = useState(null);
  const supportIconRef = useRef(null);
  const [ownership, setOwnership] = useState<string>('');
  const [fuelType, setFuelType] = useState<string>('');

  const toggleSupportMenu = () => {
    if (supportIconRef.current) {
      supportIconRef.current.measure(
        (
          _x: number,
          _y: number,
          width: number,
          height: number,
          pageX: number,
          pageY: number,
        ) => {
          setIconPosition({x: pageX, y: pageY, width, height});
          setSupportModalVisible(!supportModalVisible);
        },
      );
    } else {
      setSupportModalVisible(!supportModalVisible);
    }
  };

  useFocusEffect(
    useCallback(() => {
      const loadDriverData = async () => {
        setIsLoading(true);
        try {
          await fetchDriver();
        } catch (err) {
          console.error('Error fetching driver data:', err);
        } finally {
          setIsLoading(false);
        }
      };
      loadDriverData();
    }, []),
  );

  useEffect(() => {
    if (driver?.vehicles?.[0]) {
      const vehicleType = driver.vehicles[0].vehicle_type;
      const vehicleNumber = driver.vehicles[0].vehicle_no;

      const matchingVehicle = vehiclesData.find(
        (item: Vehicle) => item.backendName === vehicleType,
      );

      if (matchingVehicle) {
        setSelectedVehicle(matchingVehicle);
      }

      if (vehicleNumber) {
        setVehicleNo(vehicleNumber);
      }
      if (driver.vehicles[0]?.ownership_type) {
        setOwnership(driver.vehicles[0].ownership_type);
      }

      if (driver.vehicles[0].fuel_type) {
        setFuelType(driver.vehicles[0].fuel_type);
      }
    }
  }, [driver]);

  useEffect(() => {
    if (!selectedVehicle && !isLoading) {
      const autoVehicle = vehiclesData.find(
        (item: Vehicle) => item.backendName === 'AUTORICKSHAW'
      );
      
      if (autoVehicle) {
        setSelectedVehicle(autoVehicle);
      }
    }
  }, [isLoading]);

  const handleVehicleSelect = (vehicle: Vehicle) => {
    if (selectedVehicle?.id === vehicle.id) {
      setSelectedVehicle(null);
    } else {
      setSelectedVehicle(vehicle);
    }
  };

  const renderVehicleCard = (vehicle: Vehicle) => {
    const isDisabled = vehicle.disabled;
    const isSelected = selectedVehicle?.id === vehicle.id;

    return (
      <TouchableOpacity
        style={[
          styles.vehicleCard,
          isSelected && styles.selectedVehicleCard,
          isDisabled && styles.disabledVehicleCard,
        ]}
        onPress={() => !isDisabled && handleVehicleSelect(vehicle)}
        disabled={isDisabled}
        key={vehicle.id}>
        {isDisabled && (
          <View style={styles.comingSoonContainer}>
            <Text style={styles.comingSoonText}>{t('coming_soon')}</Text>
          </View>
        )}
        <Image
          source={vehicle.image}
          style={[styles.vehicleImage, isDisabled && {opacity: 0.5}]}
        />
        <Text
          numberOfLines={1}
          style={[styles.vehicleTypeTitle, isDisabled && styles.disabledText]}>
          {vehicle.name}
        </Text>
      </TouchableOpacity>
    );
  };

  const screenWidth = Dimensions.get('window').width;
  const isSmallScreen = screenWidth < 310; 

  const groupedVehicles = useMemo(() => {
    // For small screens, show 1 card per row
    const cardsPerRow = isSmallScreen ? 1 : 2;
    
    return vehiclesData.reduce((resultArray, item, index) => {
      const chunkIndex = Math.floor(index / cardsPerRow);
      if (!resultArray[chunkIndex]) {
        resultArray[chunkIndex] = [];
      }
      resultArray[chunkIndex].push(item);
      return resultArray;
    }, [] as Vehicle[][]);
  }, [isSmallScreen]);

  const ownershipData = [
    {label: t('self_owned'), value: 'SELF_OWNED'},
    {label: t('rented'), value: 'RENTED'},
  ];

  const fuelTypeData = [
    {label: t('petrol'), value: 'PETROL'},
    {label: t('diesel'), value: 'DIESEL'},
    {label: t('cng'), value: 'CNG'},
    {label: t('electric'), value: 'ELECTRIC'},
  ];

  const updateVehicle = async () => {
    if (!selectedVehicle || !vehicleNo || !ownership || !fuelType) return;

    try {
      showLoader();
      Keyboard.dismiss();
      const response = await DriverService.update({
        vehicleNo: vehicleNo,
        vehicleType: selectedVehicle.backendName,
        ownershipType: ownership,
        fuelType: fuelType,
      });

      if (response) {
        await fetchDriver();
        navigation.navigate('Document', {vehicleNo});
        showToast(t('vehicle_updated_successfull'), 'success');
      }
    } catch (err: any) {
      const status = err?.response?.status;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
      if (err?.response?.data?.message) {
        // showToast(err.response.data.message, 'failure');
        console.log(err.response.data.message);
      }
    } finally {
      hideLoader();
    }
  };

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{flex: 1}}>
          <View style={styles.headerContainer}>
            <View style={styles.iconContainer}>
              <TouchableOpacity
                style={styles.icon}
                onPress={() => navigation.replace('UserSetup')}
                accessibilityLabel="Go back">
                <IconSvgView source={Back} />
              </TouchableOpacity>
              <FlexContainer direction="row" justifyContent="center">
                {[...Array(4)].map((_, index) => (
                  <IconSvgView
                    key={index}
                    svgStyle={styles.diamondIcon}
                    source={diamond}
                  />
                ))}
              </FlexContainer>
              <TouchableOpacity
                ref={supportIconRef}
                style={styles.icon}
                onPress={toggleSupportMenu}
                accessibilityLabel="Get support">
                <IconSvgView size={30} source={support} />
              </TouchableOpacity>
            </View>
          </View>

          <ScrollView style={styles.scrollableContent}>
            <Text style={styles.title}>{t('vehicle_details_title')}</Text>

            <Text style={styles.vehicleLabel}>{t('vehicle_type')}</Text>
            <ScrollView
              style={{flexGrow: 1}}
              showsVerticalScrollIndicator={true}
              contentContainerStyle={styles.vehicleContainer}>
              {groupedVehicles.map((vehicleGroup, groupIndex) => (
                <View style={styles.vehicleRow} key={groupIndex}>
                  {vehicleGroup.map(renderVehicleCard)}
                </View>
              ))}
            </ScrollView>

            <Input
              inputTitle={t('vehicle_no_title')}
              placeholder="eg: TN23SZ1098"
              onChange={text => setVehicleNo(text)}
              value={vehicleNo}
              error={error}
              autoCapitalize={'characters'}
            />

            <View style={styles.dropdownContainer}>
              <Dropdown
                label={t('select')}
                data={ownershipData}
                onSelect={item => setOwnership(item.value)}
                title={t('vehicle_ownership')}
                defaultValue={ownership}
                selectedValue={ownership}
              />
            </View>

            {/* Add Fuel Type dropdown */}
            <View style={styles.dropdownContainer}>
              <Dropdown
                label={t('select')}
                data={fuelTypeData}
                onSelect={item => setFuelType(item.value)}
                title={t('fuel_type')}
                defaultValue={fuelType}
                selectedValue={fuelType}
              />
            </View>

            {/* Add bottom padding for scrolling past the button */}
            <View style={{height: 20}} />
          </ScrollView>

          <View style={styles.buttonContainer}>
            <Button
              title={t('continue')}
              style={styles.continueBtn}
              disabled={
                !vehicleNo ||
                !selectedVehicle ||
                !ownership ||
                !fuelType ||
                !!error ||
                isLoading
              }
              onPress={updateVehicle}
            />
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>

      <SupportMenu
        visible={supportModalVisible}
        onClose={() => setSupportModalVisible(false)}
        position={iconPosition}
      />
    </ImageBackground>
  );
};

export default Vehicle;
