import React, {useCallback, useState, useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {useToast} from '../../components/Toast/Toast';
import FileUpload from '../../components/FileUpload/FileUpload';
import DocumentPicker, {
  DocumentPickerResponse,
  types,
} from 'react-native-document-picker';
import {Platform} from 'react-native';
import {launchCamera} from 'react-native-image-picker';
import {useLoader} from '../../hooks/useLoader';
import DriverService from '../../services/DriverService';
import {useDriver} from '../../hooks/useDriver';
import {STATUS_CODE} from '../../constants/constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useFocusEffect} from '@react-navigation/native';

interface PpProps {
  navigation: any;
  route: any;
}

const Pp: React.FC<PpProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {showToast} = useToast();
  const {showLoader, hideLoader} = useLoader();
  const {fetchDriver} = useDriver();
  const {driver} = useDriver();
  const [pp, setPp] = useState<DocumentPickerResponse[]>([]);
  const driverParam = route?.params?.driver ?? null;
  const fromWelcome = route?.params?.fromWelcome || false;
  const [isFileRemoved, setIsFileRemoved] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Only allow image files - JPG, JPEG, PNG
  const allowedExtensions = ['jpg', 'jpeg', 'png'];
  const allowedMimeTypes = ['image/jpeg', 'image/png'];
  // Explicitly exclude other file types
  const disallowedMimeTypes = ['video/mp4', 'application/pdf'];
  const disallowedExtensions = ['mp4', 'pdf', 'psf'];

  useFocusEffect(
    useCallback(() => {
      (async () => {
        if (!isInitialized) {
          await fetchDriver();
          if (driver && driver.profile_photo) {
            const updatedUri = `${
              driver.profile_photo
            }?timestamp=${new Date().getTime()}`;

            setPp([
              {
                uri: updatedUri,
                name: 'Profile Picture.jpg',
                type: 'image/jpeg',
                fileCopyUri: updatedUri,
                size: 0,
              },
            ]);
            setIsInitialized(true);
          }
        }
      })();
    }, [isInitialized]),
  );

  const isValidImageFile = (file: DocumentPickerResponse) => {
    const fileExtension = file.name
      ? file.name.split('.').pop()?.toLowerCase()
      : '';

    if (
      disallowedExtensions.includes(fileExtension || '') ||
      disallowedMimeTypes.includes(file.type || '')
    ) {
      showToast(t('please_select_valid_image'), 'failure');
      return false;
    }

    const isAllowed =
      allowedExtensions.includes(fileExtension || '') &&
      allowedMimeTypes.includes(file.type || '');

    if (!isAllowed) {
      showToast(t('please_select_valid_image'), 'failure');
      return false;
    }

    return true;
  };

  const handleDocumentSelection = useCallback(async () => {
    try {
      const response = await DocumentPicker.pick({
        presentationStyle: 'fullScreen',
        type: [types.images], // Only allow image types
      });

      const validFiles = response.filter(isValidImageFile);

      if (validFiles.length === 0) {
        showToast(t('file_validation_message_pp'), 'failure');
        return;
      }
      setIsFileRemoved(false);
      setPp(validFiles);
    } catch (err) {
      if (!DocumentPicker.isCancel(err)) {
        showToast(t('file_selection_error'), 'failure');
      }
    }
  }, []);

  const handleCameraCapture = useCallback(async () => {
    try {
      const result = await launchCamera({
        mediaType: 'photo',
        quality: 0.8,
        maxWidth: 1280,
        maxHeight: 1280,
        saveToPhotos: false,
      });

      if (result.didCancel) {
        return;
      }

      if (result.errorCode) {
        showToast(result.errorMessage || t('file_selection_error'), 'failure');
        return;
      }

      if (result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        const capturedImage: DocumentPickerResponse = {
          uri: asset.uri || '',
          name: asset.fileName || 'profile_picture.jpg',
          type: asset.type || 'image/jpeg',
          size: asset.fileSize || 0,
          fileCopyUri: asset.uri || null,
        };

        if (!isValidImageFile(capturedImage)) {
          showToast(t('please_select_valid_image'), 'failure');
          return;
        }

        setIsFileRemoved(false);
        setPp([capturedImage]);
      }
    } catch (err) {
      showToast(t('file_selection_error'), 'failure');
      console.error('Camera capture error:', err);
    }
  }, []);

  const convertToFileResponse = (docs: DocumentPickerResponse[]) => {
    return docs.map(doc => ({
      uri: doc.uri,
      type: doc.type,
      name: doc.name,
      size: doc.size,
      fileCopyUri: doc.fileCopyUri,
    }));
  };

  const handleRemoveFile = useCallback((index: number) => {
    setIsFileRemoved(true);
    setPp(prevState => {
      const updatedFiles = [...prevState];
      updatedFiles.splice(index, 1);
      return updatedFiles;
    });
  }, []);

  const handleContinue = async () => {
    try {
      showLoader();
      if (pp.length > 0) {
        const fileExt = pp[0].name
          ? pp[0].name.split('.').pop()?.toLowerCase()
          : '';

        if (
          !isValidImageFile(pp[0]) ||
          !allowedExtensions.includes(fileExt || '')
        ) {
          hideLoader();
          showToast(t('please_select_valid_image'), 'failure');
          return;
        }

        const formData = new FormData();
        const uri =
          Platform.OS === 'android'
            ? pp[0].uri
            : pp[0].uri.replace('file://', '');

        formData.append('pp', {
          uri,
          name: pp[0].name,
          type: pp[0].type,
        });

        const response = await DriverService.update(formData);
        if (response) {
          await fetchDriver();
          AsyncStorage.removeItem('documents');
          navigation.reset({
            index: 0,
            routes: [{name: 'Welcome'}],
          });
          showToast(t('file_upload_success'), 'success');
        }
      } else {
        showToast(t('no_file_selected'), 'failure');
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err.response?.data.response.code;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
      if (STATUS_CODE.bad_request === status) {
        code === 'update_driver_failed' &&
          showToast(t('file_size_exceeded'), 'failure');
      }
    } finally {
      hideLoader();
    }
  };

  const handleCancel = useCallback(() => {
    navigation.navigate('Document');
  }, [navigation]);

  const getBackButtonConfig = () => {
    console.log(fromWelcome, driverParam, 'fromWelcomepp');

    if (fromWelcome || !driverParam) {
      return {
        backButtonAction: 'navigate' as const,
        backToScreen: 'Dl',
        backParams: {fromWelcome: true},
      };
    }
    return {
      backButtonAction: 'goBack' as const,
    };
  };

  useEffect(() => {
    return () => {
      setIsInitialized(false);
      setIsFileRemoved(false);
    };
  }, []);

  return (
    <FileUpload
      validationMessage={t('file_validation_message_pp')}
      navigation={navigation}
      title={t('pp')}
      subtitle={t('pp_subtitle')}
      fileResponse={
        pp.length > 0
          ? convertToFileResponse(pp)
          : !isFileRemoved && driver?.profile_photo
          ? [
              {
                uri: driver.profile_photo,
                name: 'Profile Picture.jpg',
                type: 'image/jpeg',
                fileCopyUri: driver.profile_photo,
                size: 0,
              },
            ]
          : []
      }
      onContinue={handleContinue}
      onCancel={handleCancel}
      onDocumentSelection={handleDocumentSelection}
      onCameraCapture={handleCameraCapture}
      onRemoveFile={handleRemoveFile}
      driver={driverParam}
      {...getBackButtonConfig()}
    />
  );
};

export default Pp;
