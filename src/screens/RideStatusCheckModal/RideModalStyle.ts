import {Platform, StyleSheet, TextStyle, ViewStyle} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    resizeMode: 'cover',
  },

  modalContainer: {
    position: 'absolute',
    top: 18,
    marginHorizontal: spacing.xl,
    flex: 1,
  },

  modalCard: {
    backgroundColor: colors.darkCharcoal,
    padding: spacing.xl,
    borderRadius: 5,
  },

  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: spacing.md,
    flexWrap: 'wrap',
    width: '100%',
  },

  title: {
    marginHorizontal: spacing.md,
    fontSize: sizes.h3,
    color: colors.white,
    fontFamily: EBGaramondFont.regular,
    textTransform: 'lowercase',
  },

  fareText: {
    fontSize: sizes.h6,
    color: colors.white,
    fontFamily: GeistFont.regular,
  },

  locationContainer: {
    width: '100%',
    padding: spacing.lg,
    height: '100%',
  },

  location: {
    color: colors.lightGrey,
    fontSize: sizes.body,
    fontFamily: GeistFont.regular,
    marginHorizontal: spacing.md,
  },

  confirmBtn: {
    width: '45%',
    marginBottom: spacing.sm,
  },

  pickup: {
    color: colors.lightGrey,
    fontSize: sizes.h6,
    fontFamily: GeistFont.variable,
    marginTop: spacing.xl,
  },

  footerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.xxs * 3,
  },

  countDownContainer: {
    position: 'absolute',
    top: 25,
    right: 20,
    backgroundColor: colors.white,
    borderRadius: spacing.xxl * 2,
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    width: 35,
    height: 35,
  },

  countDownTxt: {
    color: colors.black,
    fontSize: sizes.h6,
  },
});
