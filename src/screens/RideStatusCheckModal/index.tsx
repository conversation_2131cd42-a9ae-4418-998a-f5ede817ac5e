import React, {useEffect, useState} from 'react';
import {View, Text, Modal} from 'react-native';
import styles from './RideModalStyle';
import Button from '../../components/Button/Button';
import {useTranslation} from 'react-i18next';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import {STATUS_CODE} from '../../constants/constants';
import {spacing} from '../../constants/theme';
import ellipseActive from '../../icons/ellipseActive.svg';
import diamondInactive from '../../icons/diamondGrey.svg';
import HollowButton from '../../components/Button/HollowButton/HollowButton';
import {useRideDetails} from '../../hooks/useRideDetailsContext';
import {useLoader} from '../../hooks/useLoader';
import {useDriver} from '../../hooks/useDriver';
import fadingLine from '../../icons/fading_line.svg';
import RideService from '../../services/RideService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import notifee from '@notifee/react-native';
import {
  playRideStatusSound,
  stopAllSounds,
  stopRideStatusSound,
} from '../../utils/Sound';
import * as Sentry from '@sentry/react-native';

const RideStatusCheckModal: React.FC = () => {
  const {t} = useTranslation();
  const {
    tripDetails,
    setTripDetails,
    showRideStatusModal,
    setShowRideStatusModal,
  } = useRideDetails();
  const {driver} = useDriver();
  const {loading} = useLoader();
  const [attemptNumber, setAttemptNumber] = useState<number>(0);
  const [modalTitle, setModalTitle] = useState<string>('');

  useEffect(() => {
    if (showRideStatusModal) {
      const getAttemptNumber = async () => {
        try {
          const attemptValue = await AsyncStorage.getItem('attempt');
          const attempt = parseInt(attemptValue || '0', 10);
          setAttemptNumber(attempt);

          if (attempt === 0 || attempt === 1) {
            setModalTitle(t('please_respond_to_avoid_cancellation'));
          } else if (attempt === 2) {
            setModalTitle(t('final_chance_to_respond'));
          }
        } catch (error: any) {
          console.error('Error reading attempt from AsyncStorage:', error);
          setModalTitle(t('ride_status_check'));
          Sentry.captureException(error, {
            tags: {
              driverId: driver?.id,
              tripId: tripDetails?.id,
              action: 'get_attempt_number',
            },
          });
        }
      };

      getAttemptNumber();

      const timer = setTimeout(async () => {
        await AsyncStorage.removeItem('showRideStatusCheck');
        setShowRideStatusModal(false);
        stopRideStatusSound();
      }, 22000);

      return () => {
        clearTimeout(timer);
        stopRideStatusSound();
      };
    }
  }, [showRideStatusModal, t]);

  const clearLocalStorage = async () => {
    try {
      setTripDetails(null);
      await AsyncStorage.multiRemove([
        'showRideModal',
        'showRideStatusCheck',
        'tripId',
        'rideStatus',
        'userCanceled',
        'timeout',
        'sentTime',
        'attempt',
      ]);

      const noti = await AsyncStorage.getItem('notificationId');
      noti && (await notifee.cancelDisplayedNotification(noti));
    } catch (error: any) {
      console.error('Error clearing local storage:', error);
      Sentry.captureException(error, {
        tags: {
          driverId: driver?.id,
          tripId: tripDetails?.id,
          action: 'clear_local_storage',
        },
      });
    }
  };

  const handleRideAccepted = async () => {
    try {
      stopRideStatusSound();
      setShowRideStatusModal(false);
      stopAllSounds();
      const response = await RideService.statusCheck(tripDetails?.id, true);
      if (response.status === STATUS_CODE.created || tripDetails.user.phone) {
        await AsyncStorage.removeItem('showRideStatusCheck');
      }
    } catch (err: any) {
      Sentry.captureException(err, {
        tags: {
          driverId: driver?.id,
          tripId: tripDetails?.id,
          action: 'ride_status_check_accept',
          status_code: err?.response?.status,
        },
        extra: {
          message: err?.response?.data?.message,
        },
      });
    }
  };

  const handleRideDeclined = async () => {
    try {
      stopRideStatusSound();
      setShowRideStatusModal(false);
      stopAllSounds();
      await RideService.statusCheck(tripDetails?.id, false);
      await clearLocalStorage();
    } catch (err: any) {
      const status = err?.response?.status;
      const message = err?.response?.data?.message;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
      Sentry.captureException(err, {
        tags: {
          driverId: driver?.id,
          tripId: tripDetails?.id,
          action: 'ride_status_check_decline',
          status_code: status,
        },
        extra: {
          message: message,
        },
      });
    }
  };

  return (
    !loading &&
    tripDetails && (
      <Modal
        animationType="slide"
        visible={showRideStatusModal}
        transparent={true}>
        <View style={styles.modalContainer}>
          <View style={styles.modalCard}>
            <View style={styles.titleContainer}>
              <Text style={styles.fareText}>{modalTitle}</Text>
            </View>
            <FadingHorizontalLine />
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <View style={styles.locationContainer}>
                <View style={{flexDirection: 'row'}}>
                  <View style={{alignItems: 'flex-start'}}>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                      <IconSvgView
                        width={16}
                        source={diamondInactive}
                        svgStyle={{marginLeft: -spacing.xs}}
                      />
                      <Text style={styles.location} numberOfLines={2}>
                        {tripDetails.source_address}
                      </Text>
                    </View>
                    <IconSvgView
                      svgStyle={{marginLeft: spacing.xxs}}
                      source={fadingLine}
                      size={5}
                      height={30}
                    />
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                      <IconSvgView width={12} source={ellipseActive} />
                      <Text style={styles.location} numberOfLines={2}>
                        {tripDetails.destination_address}
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
            </View>

            <View style={styles.footerContainer}>
              <HollowButton
                style={styles.confirmBtn}
                title={t('end_ride')}
                onPress={handleRideDeclined}
                disabled={!driver}
              />
              <Button
                style={styles.confirmBtn}
                title={t('continue')}
                onPress={handleRideAccepted}
                disabled={!driver}
              />
            </View>
          </View>
        </View>
      </Modal>
    )
  );
};

export default RideStatusCheckModal;
