import {StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  safeArea: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: spacing.xl,
  },

  iconContainer: {
    flexDirection: 'row',
    marginBottom: spacing.lg,
  },

  icon: {
    marginTop: spacing.xl * 2,
  },

  diamondIcon: {
    marginTop: spacing.xl * 2,
    marginRight: spacing.xl,
  },

  title: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.h3,
    color: colors.white,
    marginVertical: spacing.xxl,
  },

  subtitle: {
    fontFamily: GeistFont.variable,
    fontSize: sizes.body,
    color: colors.grey,
    marginVertical: spacing.md,
  },

  list: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.h4,
    color: colors.white,
    marginTop: spacing.lg,
  },

  verification: {
    fontFamily: GeistFont.variable,
    marginBottom: spacing.lg,
    marginTop: spacing.sm,
    color: colors.red,
  },

  continueBtn: {
    marginBottom: spacing.xl,
  },
  subtitleText: {
    color: colors.lightGrey,
    fontSize: sizes.h3 / 2,
    marginTop: 5,
    marginEnd: 30,
    fontFamily: GeistFont.regular,
  },
});
