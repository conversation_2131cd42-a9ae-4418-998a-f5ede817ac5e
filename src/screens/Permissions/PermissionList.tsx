import React, {useState} from 'react';
import {
  ImageBackground,
  Text,
  ScrollView,
  View,
  TouchableOpacity,
  NativeModules,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import {SafeAreaView} from 'react-native-safe-area-context';
import {images} from '../../constants';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import chevronIcon from '../../icons/chevron-down.svg';
import Background from '../../icons/background.svg';
import Overlay from '../../icons/overlay.svg';
import Notification from '../../icons/notification.svg';
import Battery from '../../icons/battery.svg';
import tickIcon from '../../icons/done.svg';
import Button from '../../components/Button/Button';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import {
  checkLocationPermission,
  checkNotificationPermission,
  checkOverlayPermission,
} from '../../constants/permissions';
import styles from './PermissionListStyle';
import {spacing} from '../../constants/theme';
import {useFocusEffect} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

const {PermissionModule} = NativeModules;

const PermissionList: React.FC<{navigation: any}> = ({navigation}) => {
  const {t} = useTranslation();
  const [locationGranted, setLocationGranted] = useState<boolean>(false);
  const [notificationGranted, setNotificationGranted] =
    useState<boolean>(false);
  const [overlayGranted, setOverlayGranted] = useState<boolean>(false);
  const [batteryGranted, setBatteryGranted] = useState<boolean>(false);

  useFocusEffect(() => {
    (async () => {
      const locationPermission = await checkLocationPermission();
      const notificationPermission = await checkNotificationPermission();
      const overlayPermission = await checkOverlayPermission();
      const isEnabled = await PermissionModule.checkBatteryOptimization();

      setLocationGranted(locationPermission);
      setOverlayGranted(overlayPermission);
      setNotificationGranted(notificationPermission);
      setBatteryGranted(isEnabled);
    })();
  });

  const permissionsData = [
    {
      key: 1,
      title: t('battery_usage'),
      subtitle: t('helps_run_background'),
      status: batteryGranted ? 1 : 0,
      navigation: 'BatteryPermission',
      icon: Battery,
    },
    {
      key: 2,
      title: t('background_location'),
      subtitle: t('helps_find_location'),
      status: locationGranted ? 1 : 0,
      navigation: 'LocationPermission',
      icon: Background,
    },

    {
      key: 3,
      title: t('display_over_others'),
      subtitle: t('permit_app_display'),
      status: overlayGranted ? 1 : 0,
      navigation: 'OverlayPermission',
      icon: Overlay,
    },
    {
      key: 4,
      title: t('notification'),
      subtitle: t('helps_receive_notification'),
      status: notificationGranted ? 1 : 0,
      navigation: 'NotificationPermission',
      icon: Notification,
    },
  ];

  const handleNavigation = (navigateTo: string) => {
    navigation.navigate(navigateTo);
  };

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          contentContainerStyle={{flexGrow: 1}}
          keyboardShouldPersistTaps="handled">
          <Text style={styles.title}>{t('give_permissions')}</Text>
          {permissionsData.map(item => (
            <TouchableOpacity
              key={item.key}
              activeOpacity={0.7}
              disabled={item.status === 1}
              style={{
                opacity: item.status === 1 ? 0.9 : 1,
              }}
              onPress={() => handleNavigation(item.navigation)}>
              <FadingHorizontalLine />
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  marginVertical: spacing.sm,
                }}>
                <View style={{width: '90%'}}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      marginBottom: spacing.sm,
                    }}>
                    <IconSvgView source={item.icon} />
                    <View style={{marginLeft: spacing.md}}>
                      <Text style={styles.list}>{item.title}</Text>
                      <Text style={styles.subtitleText}>{item.subtitle}</Text>
                    </View>
                  </View>
                </View>
                <TouchableOpacity
                  onPress={() => handleNavigation(item.navigation)}>
                  <IconSvgView
                    size={item.status ? 20 : 28}
                    source={item.status ? tickIcon : chevronIcon}
                  />
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          ))}

          <FlexContainer justifyContent="flex-end">
            <Button
              title={t('continue')}
              style={styles.continueBtn}
              disabled={
                !locationGranted ||
                !overlayGranted ||
                !notificationGranted ||
                !batteryGranted
              }
              onPress={async () => {
                await AsyncStorage.setItem('active', JSON.stringify(true));
                navigation.reset({
                  index: 0,
                  routes: [{name: 'BottomTab'}],
                });
              }}
            />
          </FlexContainer>
        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default PermissionList;
