import React, {useEffect} from 'react';
import Permissions from './Permissions';
import {
  checkNotificationPermission,
  requestNotificationPermission,
} from '../../constants/permissions';
import {images} from '../../constants';
import {useToast} from '../../components/Toast/Toast';
import {getFcmToken} from '../../../firebase.ts';
import {useTranslation} from 'react-i18next';

interface NotificationPermissionScreenProps {
  navigation?: any;
}

export const NotificationPermission: React.FC<
  NotificationPermissionScreenProps
> = ({navigation}) => {
  const {showToast} = useToast();
  const {t} = useTranslation();

  const points = [t('notification_permission_subtitle')];

  useEffect(() => {
    (async () => {
      const granted = await checkNotificationPermission();
      if (granted) {
        navigation?.replace('PermissionList');
        showToast(t('notification_permission_granted'), 'success');
      }
    })();
  }, []);

  const handlePress = async () => {
    const permissionGranted = await requestNotificationPermission();
    console.log(permissionGranted);

    if (permissionGranted) {
      await getFcmToken();
      navigation?.replace('PermissionList');
      showToast(t('notification_permission_granted'), 'success');
    } else {
      console.log('Notification permission was denied');
    }
  };

  return (
    <Permissions
      title={t('notification_permission_title')}
      points={points}
      handlePress={handlePress}
      image={images.notification}
      back={() => navigation.goBack()}
      width={400}
      height={300}
    />
  );
};

export default NotificationPermission;
