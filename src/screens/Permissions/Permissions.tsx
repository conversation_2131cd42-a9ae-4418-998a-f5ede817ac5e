import React from 'react';
import {
  ImageBackground,
  Text,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  View,
  TouchableOpacity,
  Image,
} from 'react-native';
import {images} from '../../constants';
import styles from './PermissionsStyle';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import Button from '../../components/Button/Button';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import {SafeAreaView} from 'react-native-safe-area-context';
import Back from '../../icons/back.svg';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import {t} from 'i18next';

interface PhoneScreenProps {
  title: string;
  handlePress?: any;
  back: any;
  points: string[];
  image: any;
  width: number;
  height: number;
}

const Permissions: React.FC<PhoneScreenProps> = ({
  title,
  handlePress,
  points,
  back,
  image,
  width,
  height,
}) => {
  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          style={{flex: 1}}>
          <View style={styles.iconContainer}>
            <TouchableOpacity style={styles.icon} onPress={back}>
              <IconSvgView source={Back} />
            </TouchableOpacity>
          </View>

          <ScrollView
            contentContainerStyle={{flexGrow: 1}}
            keyboardShouldPersistTaps="handled">
            <View style={{alignItems: 'center'}}>
              <Image
                style={[styles.imageContainer, {width: width, height: height}]}
                source={image}
              />
            </View>
            <FadingHorizontalLine />
            <View>
              <Text style={styles.title}>{title}</Text>
            </View>
            {points?.map((item: string, index: number) => (
              <View key={index} style={styles.pointContainer}>
                <Text style={styles.pointText}>{item}</Text>
              </View>
            ))}
            <View style={{height: 80}} />
          </ScrollView>

          <View style={styles.buttonContainer}>
            <Button
              title={t('allow')}
              style={styles.continueBtn}
              onPress={handlePress}
            />
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default Permissions;
