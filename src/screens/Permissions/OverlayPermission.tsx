import React, {useEffect} from 'react';
import Permissions from './Permissions';
import {
  checkOverlayPermission,
  navigateToSettings,
} from '../../constants/permissions';
import {AppState, NativeModules} from 'react-native';
import {images} from '../../constants';
import {useFocusEffect} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {useToast} from '../../components/Toast/Toast';

interface PhoneScreenProps {
  navigation?: any;
}

const {FloatingWindow} = NativeModules;
export const OverlayPermission: React.FC<PhoneScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {showToast} = useToast();

  const points = [t('overlay_permission_subtitle')];


  useEffect(() => {
    const subscription = AppState.addEventListener(
      'change',
      async nextAppState => {
        const granted = await checkOverlayPermission();
        if (granted) {
          navigation?.replace('PermissionList');
          showToast(t('overlay_permission_granted'), 'success');
        }
      },
    );
    return () => {
      subscription.remove();
    };
  }, []);

  const handlePress = async () => {
    const granted = await checkOverlayPermission();
    console.log(granted);
    if (granted) {
      navigation?.replace('PermissionList');
      showToast(t('overlay_permission_granted'), 'success');
    } else await FloatingWindow.requestPermission();
  };

  return (
    <Permissions
      title={t('overlay_permission_title')}
      points={points}
      handlePress={handlePress}
      image={images.overlay}
      back={() => navigation.goBack()}
      width={400}
      height={300}
    />
  );
};

export default OverlayPermission;
