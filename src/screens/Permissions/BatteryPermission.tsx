import React, {useEffect} from 'react';
import {Linking, NativeModules, Platform} from 'react-native';
import Permissions from './Permissions';
import {images} from '../../constants';
import {useTranslation} from 'react-i18next';
import {useToast} from '../../components/Toast/Toast';
import {checkLocationPermission} from '../../constants/permissions';
import {useFocusEffect} from '@react-navigation/native';

interface PhoneScreenProps {
  navigation?: any;
}

export const BatteryPermission: React.FC<PhoneScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {showToast} = useToast();
  const {PermissionModule} = NativeModules;

  const points = [t('battery_permission_subtitle')];

  useFocusEffect(() => {
    (async () => {
      const isEnabled = await PermissionModule.checkBatteryOptimization();
      if (isEnabled) {
        navigation?.replace('PermissionList');
        showToast(t('battery_permission_granted'), 'success');
      }
    })();
  });

  const handlePermission = async () => {
    const askBatteryPermission = () => {
      if (PermissionModule) {
        PermissionModule.askBatteryPermission();
      } else {
        console.log('skt-- The permission trigger module is null');
        console.log(NativeModules);
      }
    };
    const isEnabled = await PermissionModule.checkBatteryOptimization();
    console.log('BATTERY', isEnabled);
    if (!isEnabled) {
      askBatteryPermission();
    } else if (isEnabled) {
      showToast(t('battery_permission_granted'), 'success');
      navigation?.replace('PermissionList');
    }
  };

  return (
    <Permissions
      title={t('battery_permission_title')}
      points={points}
      handlePress={handlePermission}
      image={images.battery}
      back={() => navigation.goBack()}
      width={400}
      height={380}
    />
  );
};

export default BatteryPermission;
