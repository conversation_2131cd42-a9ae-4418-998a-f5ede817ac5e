import React, {useEffect} from 'react';
import Permissions from './Permissions';
import {
  checkLocationPermission,
  requestLocationPermission,
} from '../../constants/permissions';
import {images} from '../../constants';
import {Linking} from 'react-native';
import {useToast} from '../../components/Toast/Toast';
import {useTranslation} from 'react-i18next';

interface LocationPermissionScreenProps {
  navigation?: any;
}

export const LocationPermission: React.FC<LocationPermissionScreenProps> = ({
  navigation,
}) => {
  const {showToast} = useToast();
  const {t} = useTranslation();

  const points = [t('location_permission_subtitle')];

  useEffect(() => {
    (async () => {
      const granted = await checkLocationPermission();
      if (granted) {
        navigation?.replace('PermissionList');
        showToast(t('location_permission_granted'), 'success');
      }
    })();
  }, []);

  const handlePermission = async () => {
    const locationGranted = await requestLocationPermission();
    if (locationGranted) {
      navigation?.replace('PermissionList');
      showToast(t('location_permission_granted'), 'success');
    } else {
      Linking.openSettings();
    }
  };

  return (
    <Permissions
      title={t('location_permission_title')}
      points={points}
      handlePress={handlePermission}
      image={images.backgroundLocation}
      back={() => navigation.goBack()}
      width={400}
      height={380}
    />
  );
};

export default LocationPermission;
