import {StyleSheet} from 'react-native';
import {colors, EBGaramondFont, GeistFont, sizes} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  safeArea: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: spacing.xl,
  },

  iconContainer: {
    flexDirection: 'row',
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 10,
  },

  icon: {
    marginTop: spacing.xl,
  },

  diamondIcon: {
    marginTop: spacing.xl * 2,
    marginRight: spacing.xl,
  },

  title: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.h2,
    fontWeight: '400',
    color: colors.white,
    marginTop: spacing.xxl,
  },

  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: -spacing.xl, // Compensate for safeArea padding
    right: -spacing.xl, // Compensate for safeArea padding
    backgroundColor: colors.black, 
    paddingTop: spacing.sm,
    paddingHorizontal: spacing.xl, // Add padding back to the content inside
  },

  continueBtn: {
    marginVertical: spacing.xl,
  },

  pointContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.md,
  },
  bulletIcon: {
    marginRight: spacing.md,
  },
  pointText: {
    fontSize: sizes.body,
    color: colors.lightGrey,
    fontFamily: GeistFont.regular,
  },
  imageContainer: {
    resizeMode: 'center',
  },
});
