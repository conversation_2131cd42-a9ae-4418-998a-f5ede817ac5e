import React, {useCallback, useEffect, useRef, useState, useMemo} from 'react';
import {View, Text, Image, Modal, TouchableOpacity} from 'react-native';
import styles from './RideModalStyle';
import Button from '../../components/Button/Button';
import {useTranslation} from 'react-i18next';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import {STATUS_CODE, vehiclesData} from '../../constants/constants';
import {spacing} from '../../constants/theme';
import ellipseActive from '../../icons/ellipseActive.svg';
import diamondInactive from '../../icons/diamondGrey.svg';
import closeIcon from '../../icons/close.svg';
import {useToast} from '../../components/Toast/Toast';
import {useRideDetails} from '../../hooks/useRideDetailsContext';
import {useLoader} from '../../hooks/useLoader';
import {useDriver} from '../../hooks/useDriver';
import notifee from '@notifee/react-native';
import fadingLine from '../../icons/fading_line.svg';
import RideService from '../../services/RideService';
import DriverService from '../../services/DriverService';
import {stopRideSound, playAcceptSound} from '../../utils/Sound';
import * as Sentry from '@sentry/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Default timeout in seconds
const DEFAULT_TIMEOUT_SECONDS = 18;

const RideModal: React.FC = () => {
  const {t} = useTranslation();
  const {tripDetails, showRideModal, setShowRideModal} = useRideDetails();
  const {driver} = useDriver();
  const {showToast} = useToast();
  const {loading} = useLoader();
  const [countdown, setCountdown] = useState(DEFAULT_TIMEOUT_SECONDS);
  const [timeoutSeconds, setTimeoutSeconds] = useState(DEFAULT_TIMEOUT_SECONDS);
  const [loadingButton, setLoadingButton] = useState<
    'accept' | 'decline' | null
  >(null);
  const [showPickupFare, setShowPickupFare] = useState(true);
  const [bonusAmount, setBonusAmount] = useState<number>(0);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const resetAndStartCountdown = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    if (timeoutSeconds <= 0) return;
    setCountdown(timeoutSeconds); 

    const expiryTimeInMs = Date.now() + timeoutSeconds * 1000;

    intervalRef.current = setInterval(() => {
      const remainingTime = expiryTimeInMs - Date.now();
      const remainingSeconds = Math.floor(remainingTime / 1000);

      if (remainingSeconds <= 0) {
        setShowRideModal(false);
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
        setCountdown(0);
      } else {
        setCountdown(remainingSeconds);
      }
    }, 1000);
  }, [timeoutSeconds, setShowRideModal]);

  const startCountdownWithRemainingTime = useCallback(
    (remainingSeconds: number) => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }

      if (remainingSeconds <= 0) {
        setShowRideModal(false);
        return;
      }

      setCountdown(remainingSeconds);

      const expiryTimeInMs = Date.now() + remainingSeconds * 1000;

      intervalRef.current = setInterval(() => {
        const currentRemainingTime = expiryTimeInMs - Date.now();
        const currentRemainingSeconds = Math.floor(currentRemainingTime / 1000);

        if (currentRemainingSeconds <= 0) {
          setShowRideModal(false);
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          setCountdown(0);
        } else {
          setCountdown(currentRemainingSeconds);
        }
      }, 1000);
    },
    [setShowRideModal],
  );

  useEffect(() => {
    if (tripDetails) {
      if (tripDetails.bonus && parseFloat(tripDetails.bonus) > 0) {
        setBonusAmount(parseFloat(tripDetails.bonus));
        console.log('Setting bonus amount from tripDetails:', tripDetails.bonus);
      } else {
        setBonusAmount(0);
        console.log('No valid bonus found in tripDetails, setting to 0');
      }
    }
  }, [tripDetails]);

  useEffect(() => {
    const fetchDriverConfig = async () => {
      try {
        const sentTimeStr = await AsyncStorage.getItem('sentTime');
        const sentTime = sentTimeStr ? Number(sentTimeStr) : null;
        const response = await DriverService.getDriverConfig();

        let timeoutValue = DEFAULT_TIMEOUT_SECONDS;

        if (
          response.status === STATUS_CODE.ok &&
          response.data &&
          response.data.data
        ) {
          const configItems = response.data.data;

          const timeoutConfig = configItems.find(
            (item: {key: string}) =>
              item.key === 'driver_request_timeout_seconds',
          );

          if (timeoutConfig && timeoutConfig.value) {
            const timeout = Number(timeoutConfig.value);
            if (!isNaN(timeout)) {
              timeoutValue = timeout;
              setTimeoutSeconds(timeout);
            }
          }
          
          // Handle show_pickup_fare configuration
          const showPickupFareConfig = configItems.find(
            (item: {key: string}) => item.key === 'show_pickup_fare',
          );
          
          if (showPickupFareConfig && showPickupFareConfig.value) {
            const showPickup = showPickupFareConfig.value.toLowerCase() === 'true';
            setShowPickupFare(showPickup);
          }
        }

        if (sentTime && !isNaN(sentTime)) {
          const ttl = timeoutValue * 1000;
          const expiryTime = sentTime + ttl;
          const remainingTimeMs = expiryTime - Date.now();
          const remainingSeconds = Math.floor(remainingTimeMs / 1000);

          if (remainingSeconds <= 0) {
            setShowRideModal(false);
            return;
          }

          setCountdown(remainingSeconds);
          startCountdownWithRemainingTime(remainingSeconds);
        } else {
          resetAndStartCountdown();
        }
      } catch (error) {
        resetAndStartCountdown();
      }
    };

    if (showRideModal) {
      fetchDriverConfig();
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [showRideModal, resetAndStartCountdown]);

  const handleRideAccepted = async () => {
    setLoadingButton('accept');
    try {
      const response = await RideService.acceptRide(tripDetails?.id, true);
      if (response.status === STATUS_CODE.created) {
        stopRideSound();
        playAcceptSound();
        await notifee.cancelAllNotifications();
        console.log('RideModal-Accepted', response.data.data);
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err?.response?.data?.response?.code;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
      console.log('Error-RideModal-Accepted', err.response?.data);

      if (STATUS_CODE.bad_request === status) {
        code === 'invalid_or_expired_request' &&
          showToast(t('invalid_or_expired_request'), 'failure');
        code === 'request_already_given' &&
          showToast(t('request_already_given'), 'failure');
        code === 'trip_already_cancelled' &&
          showToast(t('trip_already_cancelled'), 'failure');
      }
      Sentry.captureException(err, {
        tags: {
          tripId: tripDetails?.id,
          driverId: driver?.id,
          action: 'ride_acceptance',
          status_code: status,
          error_code: code,
        },
      });
    } finally {
      setLoadingButton(null);
    }
  };

  const handleRideDeclined = async () => {
    setLoadingButton('decline');
    try {
      const response = await RideService.acceptRide(tripDetails?.id, false);
      if (response) {
        console.log('RideModal-Denied', response.data.data);
      }
      stopRideSound();
    } catch (err: any) {
      console.log('Error-RideModal-Deny', err.response?.data);
      const status = err?.response?.status;
      const code = err.response.data.response.code;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
      if (STATUS_CODE.bad_request === status) {
        code === 'invalid_or_expired_request' &&
          showToast(t('invalid_or_expired_request'), 'failure');
        code === 'request_already_given' &&
          showToast(t('request_already_given'), 'failure');
        code === 'trip_already_cancelled' &&
          showToast(t('trip_already_cancelled'), 'failure');
      }
    } finally {
      setLoadingButton(null);
    }
  };

  const selectedVehicle = useMemo(() => {
    if (tripDetails) {
      return vehiclesData.find(
        vehicle => vehicle.backendName === tripDetails?.vehicleType,
      );
    }
  }, [tripDetails?.vehicleType]);

  const renderLoader = () => (
    <View style={styles.loaderContainer}>
      <Image
        source={require('../../icons/LOADER.gif')}
        style={styles.loaderImage}
      />
    </View>
  );

  return (
    <Modal animationType="slide" visible={showRideModal} transparent={true}>
      <View style={styles.modalContainer}>
        <View style={styles.modalCard}>
          {loading || !tripDetails ? (
            renderLoader()
          ) : (
            <>
              <View style={styles.countDownContainer}>
                <Text style={styles.countDownTxt}>{String(countdown)}</Text>
              </View>
              <View style={styles.titleContainer}>
                {selectedVehicle?.image && (
                  <Image
                    source={selectedVehicle.image}
                    style={{width: 50, height: 50}}
                  />
                )}
                <View>
                  <Text style={styles.title}>
                    {tripDetails?.vehicleType || ''}
                  </Text>
                  <Text style={styles.fareText}>
                    {t('rupee')} {tripDetails?.fare || ''}
                  </Text>
                  {tripDetails?.bonus && parseFloat(tripDetails.bonus) > 0 && (
                    <Text style={styles.bonusText}>
                      + {t('rupee')} {tripDetails?.bonus} {t('bonus')}
                    </Text>
                  )}
                </View>
              </View>
              <FadingHorizontalLine />

              <View style={{width: '100%'}}>
                <View style={styles.locationContainer}>
                  <View style={{flexDirection: 'row'}}>
                    <View style={{alignItems: 'flex-start'}}>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                        }}>
                        <IconSvgView
                          width={16}
                          source={diamondInactive}
                          svgStyle={{marginLeft: -spacing.xs}}
                        />
                        <Text style={styles.location}>
                          {tripDetails.source_address || ''}
                        </Text>
                      </View>
                      <View>
                        <IconSvgView
                          svgStyle={{marginLeft: spacing.xxs}}
                          source={fadingLine}
                          size={5}
                          height={30}
                        />
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                        }}>
                        <IconSvgView width={12} source={ellipseActive} />
                        <Text style={styles.location}>
                          {tripDetails.destination_address || ''}
                        </Text>
                      </View>
                    </View>
                  </View>

                  <View style={styles.tripDetailsContainer}>
                    <View style={styles.tripDetailRow}>
                      <Text style={styles.tripDetailLabel}>{t('pickup')}</Text>
                      <View style={styles.tripDetailValues}>
                        <Text style={styles.tripDetailValue}>
                          {tripDetails?.pickupDistance || ''} {t('km')}
                        </Text>
                        {showPickupFare && (
                          <>
                            <Text style={styles.divider}>|</Text>
                            <Text style={styles.tripDetailValue}>
                              {t('rupee')} {tripDetails?.pickupFare || ''}
                            </Text>
                          </>
                        )}
                      </View>
                    </View>

                    <View style={styles.tripDetailRow}>
                      <Text style={styles.tripDetailLabel}>{t('drop')}</Text>
                      <View style={styles.tripDetailValues}>
                        <Text style={styles.tripDetailValue}>
                          {tripDetails?.distance || ''} {t('km')}
                        </Text>
                        <Text style={styles.divider}>|</Text>
                        <Text style={styles.tripDetailValue}>
                          {t('rupee')} {tripDetails?.dropFare || ''}
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>
              </View>

              <View style={styles.footerContainer}>
                <TouchableOpacity
                  style={styles.declineBtn}
                  onPress={handleRideDeclined}
                  disabled={!driver}>
                  <IconSvgView source={closeIcon} width={20} height={20} />
                </TouchableOpacity>
                <Button
                  style={styles.acceptBtn}
                  title={t('accept')}
                  onPress={handleRideAccepted}
                  disabled={!driver}
                  loading={loadingButton === 'accept'}
                />
              </View>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default RideModal;
