import React, {useCallback, useEffect, useRef, useState, useMemo} from 'react';
import {View, Text, Image, Modal, TouchableOpacity} from 'react-native';
import styles from './RideModalStyle';
import Button from '../../components/Button/Button';
import {useTranslation} from 'react-i18next';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import {STATUS_CODE, vehiclesData} from '../../constants/constants';
import {spacing} from '../../constants/theme';
import ellipseActive from '../../icons/ellipseActive.svg';
import diamondInactive from '../../icons/diamondGrey.svg';
import closeIcon from '../../icons/close.svg';
import {useToast} from '../../components/Toast/Toast';
import {useRideDetails} from '../../hooks/useRideDetailsContext';
import {useLoader} from '../../hooks/useLoader';
import {useDriver} from '../../hooks/useDriver';
import notifee from '@notifee/react-native';
import {useFocusEffect} from '@react-navigation/native';
import fadingLine from '../../icons/fading_line.svg';
import RideService from '../../services/RideService';
import DriverService from '../../services/DriverService';
import {stopRideSound, playAcceptSound} from '../../utils/Sound';
import * as Sentry from '@sentry/react-native';

// Default timeout in seconds
const DEFAULT_TIMEOUT_SECONDS = 18;

const RideModal: React.FC = () => {
  const {t} = useTranslation();
  const {tripDetails, showRideModal, setShowRideModal} = useRideDetails();
  const {driver} = useDriver();
  const {showToast} = useToast();
  const {loading} = useLoader();
  const [countdown, setCountdown] = useState(DEFAULT_TIMEOUT_SECONDS);
  const [timeoutSeconds, setTimeoutSeconds] = useState(DEFAULT_TIMEOUT_SECONDS);
  const [loadingButton, setLoadingButton] = useState<
    'accept' | 'decline' | null
  >(null);

  // Add a ref to track the interval
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Function to reset and start the countdown
  const resetAndStartCountdown = useCallback(() => {
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    if (timeoutSeconds <= 0) return;
    setCountdown(timeoutSeconds); // Reset countdown to the configured value

    const expiryTimeInMs = Date.now() + timeoutSeconds * 1000;

    intervalRef.current = setInterval(() => {
      const remainingTime = expiryTimeInMs - Date.now();
      const remainingSeconds = Math.floor(remainingTime / 1000);

      if (remainingSeconds <= 0) {
        setShowRideModal(false);
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
        setCountdown(0);
      } else {
        setCountdown(remainingSeconds);
      }
    }, 1000);
  }, [timeoutSeconds, setShowRideModal]);

  // Fetch driver config when modal opens
  useEffect(() => {
    const fetchDriverConfig = async () => {
      try {
        console.log('Fetching driver config...');
        const response = await DriverService.getDriverConfig();

        if (
          response.status === STATUS_CODE.ok &&
          response.data &&
          response.data.data
        ) {
          const configItems = response.data.data;

          // Find the timeout config item
          const timeoutConfig = configItems.find(
            (item: {key: string}) =>
              item.key === 'driver_request_timeout_seconds',
          );

          if (timeoutConfig && timeoutConfig.value) {
            const timeout = Number(timeoutConfig.value);
            if (!isNaN(timeout)) {
              console.log('Found driver request timeout in config:', timeout);
              setTimeoutSeconds(timeout);
              // Start the countdown after setting the timeout
              setTimeout(() => resetAndStartCountdown(), 0);
            } else {
              console.log(
                'Invalid timeout value in config, using default:',
                DEFAULT_TIMEOUT_SECONDS,
              );
              resetAndStartCountdown();
            }
          } else {
            console.log(
              'No timeout config found, using default:',
              DEFAULT_TIMEOUT_SECONDS,
            );
            resetAndStartCountdown();
          }
        } else {
          resetAndStartCountdown();
        }
      } catch (error) {
        console.error('Failed to fetch driver config:', error);
        resetAndStartCountdown();
      }
    };

    if (showRideModal) {
      fetchDriverConfig();
    }

    // Clean up interval when component unmounts
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [showRideModal, resetAndStartCountdown]);

  const handleRideAccepted = async () => {
    setLoadingButton('accept');
    try {
      const response = await RideService.acceptRide(tripDetails?.id, true);
      if (response.status === STATUS_CODE.created) {
        stopRideSound();
        playAcceptSound();
        await notifee.cancelAllNotifications();
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err?.response?.data?.response?.code;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
      if (STATUS_CODE.bad_request === status) {
        code === 'invalid_or_expired_request' &&
          showToast(t('invalid_or_expired_request'), 'failure');
        code === 'request_already_given' &&
          showToast(t('request_already_given'), 'failure');
        code === 'trip_already_cancelled' &&
          showToast(t('trip_already_cancelled'), 'failure');
      }
      Sentry.captureException(err, {
        tags: {
          tripId: tripDetails?.id,
          driverId: driver?.id,
          action: 'ride_acceptance',
          status_code: status,
          error_code: code
        },
      });
    } finally {
      setLoadingButton(null);
    }
  };

  const handleRideDeclined = async () => {
    setLoadingButton('decline');
    try {
      const response = await RideService.acceptRide(tripDetails?.id, false);
      stopRideSound();
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err.response.data.response.code;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
      if (STATUS_CODE.bad_request === status) {
        code === 'invalid_or_expired_request' &&
          showToast(t('invalid_or_expired_request'), 'failure');
        code === 'request_already_given' &&
          showToast(t('request_already_given'), 'failure');
        code === 'trip_already_cancelled' &&
          showToast(t('trip_already_cancelled'), 'failure');
      }
    } finally {
      setLoadingButton(null);
    }
  };

  const selectedVehicle = useMemo(() => {
    if (tripDetails) {
      return vehiclesData.find(
        vehicle => vehicle.backendName === tripDetails?.vehicleType,
      );
    }
  }, [tripDetails?.vehicleType]);

  const renderLoader = () => (
    <View style={styles.loaderContainer}>
      <Image
        source={require('../../icons/LOADER.gif')}
        style={styles.loaderImage}
      />
    </View>
  );

  return (
    <Modal animationType="slide" visible={showRideModal} transparent={true}>
      <View style={styles.modalContainer}>
        <View style={styles.modalCard}>
          {loading || !tripDetails ? (
            renderLoader()
          ) : (
            <>
              <View style={styles.countDownContainer}>
                <Text style={styles.countDownTxt}>{countdown}</Text>
              </View>
              <View style={styles.titleContainer}>
                <Image
                  source={selectedVehicle?.image ?? ''}
                  style={{width: 50, height: 50}}
                />
                <View>
                  <Text style={styles.title}>{tripDetails?.vehicleType}</Text>
                  <Text style={styles.fareText}>
                    {t('rupee')} {tripDetails?.fare}
                  </Text>
                </View>
              </View>
              <FadingHorizontalLine />

              {/* Content container with auto height */}
              <View style={{width: '100%'}}>
                <View style={styles.locationContainer}>
                  <View style={{flexDirection: 'row'}}>
                    <View style={{alignItems: 'flex-start'}}>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                        }}>
                        <IconSvgView
                          width={16}
                          source={diamondInactive}
                          svgStyle={{marginLeft: -spacing.xs}}
                        />
                        <Text style={styles.location}>
                          {tripDetails.source_address}
                        </Text>
                      </View>
                      <View>
                        <IconSvgView
                          svgStyle={{marginLeft: spacing.xxs}}
                          source={fadingLine}
                          size={5}
                          height={30}
                        />
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                        }}>
                        <IconSvgView width={12} source={ellipseActive} />
                        <Text style={styles.location}>
                          {tripDetails.destination_address}
                        </Text>
                      </View>
                    </View>
                  </View>

                  {/* Updated layout for pickup/drop details */}
                  <View style={styles.tripDetailsContainer}>
                    {/* Pickup details */}
                    <View style={styles.tripDetailRow}>
                      <Text style={styles.tripDetailLabel} numberOfLines={1}>
                        {t('pickup')}
                      </Text>
                      <View style={styles.tripDetailValues}>
                        <Text style={styles.tripDetailValue} numberOfLines={1}>
                          {tripDetails?.pickupDistance} {t('km')}
                        </Text>
                        <Text style={styles.divider}>|</Text>
                        <Text style={styles.tripDetailValue} numberOfLines={1}>
                          {t('rupee')} {tripDetails?.pickupFare}
                        </Text>
                      </View>
                    </View>

                    <View style={styles.tripDetailRow}>
                      <Text style={styles.tripDetailLabel} numberOfLines={1}>
                        {t('drop')}
                      </Text>
                      <View style={styles.tripDetailValues}>
                        <Text style={styles.tripDetailValue} numberOfLines={1}>
                          {tripDetails?.distance} {t('km')}
                        </Text>
                        <Text style={styles.divider}>|</Text>
                        <Text style={styles.tripDetailValue} numberOfLines={1}>
                          {t('rupee')} {tripDetails?.dropFare}
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>
              </View>

              <View style={styles.footerContainer}>
                <TouchableOpacity
                  style={styles.declineBtn}
                  onPress={handleRideDeclined}
                  disabled={!driver || loadingButton === 'accept'}>
                  <IconSvgView source={closeIcon} width={20} height={20} />
                </TouchableOpacity>
                <Button
                  style={styles.acceptBtn}
                  title={t('accept')}
                  onPress={handleRideAccepted}
                  disabled={!driver || loadingButton === 'decline'}
                  loading={loadingButton === 'accept'}
                />
              </View>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default RideModal;
