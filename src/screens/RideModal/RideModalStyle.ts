import {Platform, StyleSheet, TextStyle, ViewStyle} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    resizeMode: 'cover',
  },

  modalContainer: {
    flex: 1,
    padding: spacing.md,
    marginTop: spacing.xxl * 1.2,
  },

  modalCard: {
    backgroundColor: colors.darkCharcoal,
    padding: spacing.md,
    borderRadius: spacing.sm,
    width: '100%',
  },

  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: spacing.md,
  },

  title: {
    marginHorizontal: spacing.md,
    fontSize: sizes.h4,
    color: colors.white,
    fontFamily: EBGaramondFont.regular,
    textTransform: 'lowercase',
  },

  fareText: {
    marginHorizontal: spacing.md,
    fontSize: sizes.h1,
    color: colors.white,
    fontFamily: EBGaramondFont.EBbold,
  },

  locationContainer: {
    width: '100%',
    padding: spacing.sm,
  },

  location: {
    color: colors.white,
    fontSize: sizes.h4,
    fontWeight: '700',
    fontFamily: GeistFont.regular,
    marginHorizontal: spacing.md,
  },

  acceptBtn: {
    width: '75%',
    marginBottom: spacing.sm,
  },
  declineBtn: {
    flex: 1,
    height: 46,
    marginRight: spacing.md,
    backgroundColor: colors.darkCharcoal,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.lightGrey,
  },

  pickup: {
    color: colors.white,
    fontSize: sizes.h6,
    fontFamily: GeistFont.variable,
  },

  tripDetailsContainer: {
    marginTop: spacing.md,
    width: '100%',
  },

  tripDetailRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.xs,
    flexWrap: 'nowrap',
  },

  tripDetailLabel: {
    color: colors.white,
    fontSize: sizes.h4,
    fontFamily: GeistFont.variable,
    minWidth: 85,
    flexShrink: 0,
  },

  tripDetailValues: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
  },

  tripDetailValue: {
    color: colors.white,
    fontSize: sizes.h4,
    fontFamily: GeistFont.regular,
    flexShrink: 1,
  },

  divider: {
    color: colors.grey,
    marginHorizontal: spacing.xs,
  },

  footerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  countDownContainer: {
    position: 'absolute',
    top: 25,
    right: 20,
    backgroundColor: colors.white,
    borderRadius: spacing.xxl * 2,
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    width: 40,
    height: 40,
  },

  countDownTxt: {
    color: colors.black,
    fontSize: sizes.h6,
    fontFamily: GeistFont.bold,
  },

  fareTxt: {
    color: colors.lightGrey,
    fontSize: sizes.h6,
    fontFamily: GeistFont.variable,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderImage: {
    width: 50,
    height: 50,
  },
});
