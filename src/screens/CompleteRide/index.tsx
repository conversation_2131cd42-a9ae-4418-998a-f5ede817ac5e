import React, {useEffect, useState} from 'react';
import {View, Text, ImageBackground, TouchableOpacity} from 'react-native';
import {images} from '../../constants';
import styles from './CompleteRideStyle';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import Button from '../../components/Button/Button';
import {useTranslation} from 'react-i18next';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import back from '../../icons/back.svg';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useLoader} from '../../hooks/useLoader';
import QrCode from '../../components/QrCode';
import {useDriver} from '../../hooks/useDriver';
import HollowButton from '../../components/Button/HollowButton/HollowButton';
import {STATUS_CODE} from '../../constants/constants';
import {rideDetails, useRideDetails} from '../../hooks/useRideDetailsContext';
import RideService from '../../services/RideService';
import {useFocusEffect} from '@react-navigation/native';
import TripService from '../../services/TripService';
import {useToast} from '../../components/Toast/Toast';
import {useKeepAwake} from '../../hooks/useKeepAwake';
import * as Sentry from '@sentry/react-native';

interface CompleteRideProps {
  navigation: any;
}

const CompleteRide: React.FC<CompleteRideProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {showLoader, hideLoader} = useLoader();
  const {driver, upi} = useDriver();
  const {setTripDetails,tripDetails} = useRideDetails();
  const [user, setUser] = useState<any>();

  useKeepAwake();
  const [btnLoading, setBtnLoading] = useState<boolean>(false);
  const [localTripDetails, setLocalTripDetails] = useState<any>(null);
  const {showToast} = useToast();

  const completeRide = async () => {
    setBtnLoading(true);
    try {
      setTripDetails(null);
      await AsyncStorage.removeItem('tripId');
      await AsyncStorage.removeItem('showRideModal');
      await AsyncStorage.removeItem('rideStatus');
      rideDetails.trip_id = null;
      navigation.reset({
        index: 0,
        routes: [{name: 'BottomTab'}],
      });
    } catch (err: any) {
      const status = err?.response?.status;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
      Sentry.captureException(err, {
        tags: {
          tripId: localTripDetails?.id,
          driverId: driver?.id,
          action: 'complete_ride',
          status_code: status,
        },
      });
    } finally {
      setBtnLoading(false);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      let isActive = true;

      const fetchTripDetails = async () => {
        try {
          showLoader();
          const tripId = await AsyncStorage.getItem('tripId');

          if (!tripId) {
            hideLoader();
            return;
          }

          const response = await TripService.getTrip(JSON.parse(tripId));

          if (response.status === STATUS_CODE.ok && isActive) {
            console.log('Trip details:', response.data.data);

            setLocalTripDetails(response.data.data);
          }
        } catch (err: any) {
          console.log('Error fetching trip details:', err);
          const status = err?.response?.status;
          if (
            [STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)
          ) {
            showToast(t('error_fetching_trip'), 'failure');
            return;
          }
          Sentry.captureException(err, {
            tags: {
              tripId: tripDetails?.id,
              driverId: driver?.id,
              action: 'fetch_trip_details',
              status_code: status,
            },
          });
        } finally {
          hideLoader();
        }
      };

      const fetchUserDetails = async () => {
        try {
          const tripId = localTripDetails?.id || localTripDetails?.id;

          if (!tripId) return;

          const response = await RideService.fetchUserDetails(tripId);
          if (response.status === STATUS_CODE.ok && isActive) {
            setUser(response.data.data.user);
          }
        } catch (err: any) {
          const status = err?.response?.status;
          if (
            [STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)
          ) {
            return;
          }
          Sentry.captureException(err, {
            tags: {
              tripId: localTripDetails?.id,
              driverId: driver?.id,
              action: 'fetch_user_details',
              status_code: status,
            },
          });
        }
      };

      fetchTripDetails().then(() => fetchUserDetails());

      return () => {
        isActive = false;
      };
    }, [localTripDetails?.id]),
  );

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <View style={styles.safeArea}>
        <View style={styles.backContainer}>
          <TouchableOpacity
            hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
            onPress={() => navigation.goBack()}>
            <IconSvgView source={back} />
          </TouchableOpacity>
        </View>
        <View style={styles.card}>
          <Text style={styles.title}>{user?.name}</Text>
          <Text style={styles.title}>{t('owes')}</Text>
          <Text style={styles.rupeeText}>
            {t('rupee')}
            {localTripDetails?.fare_changed
              ? localTripDetails?.original_fare ?? 0
              : localTripDetails?.fare ?? 0}
          </Text>
          {driver?.paymentDetails && (
            <View style={{height: 150}}>
              <QrCode
                upiID={upi ? upi : driver.paymentDetails.upi_id}
                payeeName={driver?.name ?? ''}
                amount={(localTripDetails?.fare_changed
                  ? localTripDetails?.original_fare ?? 0
                  : localTripDetails?.fare ?? 0
                ).toString()}
              />
            </View>
          )}
        </View>
        {!driver?.paymentDetails && (
          <View>
            <Text numberOfLines={2} style={styles.getMoneyTxt}>
              {t('get_money')}
            </Text>
            <View style={{justifyContent: 'center', alignItems: 'center'}}>
              <ImageBackground source={images.qr} style={styles.qrImage}>
                <HollowButton
                  onPress={() => navigation.navigate('AddQr')}
                  title={t('add_qr')}
                />
              </ImageBackground>
            </View>
          </View>
        )}
        <FlexContainer justifyContent="flex-end">
          <Button
            title={t('collect_cash')}
            onPress={() => completeRide()}
            loading={btnLoading}
          />
        </FlexContainer>
      </View>
    </ImageBackground>
  );
};

export default CompleteRide;
