import React, {useEffect, useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  ImageBackground,
  Keyboard,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useLoader} from '../../hooks/useLoader';
import {
  colors,
  EBGaramondFont,
  GeistFont,
  images,
  sizes,
} from '../../constants';
import {spacing} from '../../constants/theme';
import Button from '../../components/Button/Button';
import Input from '../../components/Input/Input';
import {SafeAreaView} from 'react-native-safe-area-context';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import DriverService from '../../services/DriverService';
import {useDriver} from '../../hooks/useDriver';
import QrCode from '../../components/QrCode';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import Back from '../../icons/back.svg';
import {useToast} from '../../components/Toast/Toast';
import {STATUS_CODE} from '../../constants/constants';
import {useFocusEffect} from '@react-navigation/native';

interface FallBackScreenProps {
  navigation?: any;
  route?: any;
}

const AddQr: React.FC<FallBackScreenProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {showLoader, hideLoader} = useLoader();
  const {showToast} = useToast();
  const {fetchDriver, driver, upi, setUpi} = useDriver();
  const MyProfile = route?.params?.MyProfile;
  const [touched, setTouched] = useState(false);

  useFocusEffect(
    React.useCallback(() => {
      (async () => {
        await fetchDriver();
        if (driver?.paymentDetails?.upi_id) {
          setUpi(driver.paymentDetails.upi_id);
        }
      })();
    }, []),
  );

  const upiRegex = /^[a-zA-Z0-9.\-_]{2,256}@[a-zA-Z]{2,64}$/;

  const upiError = useMemo(() => {
    return touched && upi.length > 0 && !upiRegex.test(upi) && upi.includes('@')
      ? t('upi_error')
      : '';
  }, [upi, touched]);

  const handleUpi = async () => {
    try {
      showLoader();
      Keyboard.dismiss();
      const response = await DriverService.updateUpi(upi);
      if (response.status === STATUS_CODE.ok) {
        await fetchDriver();
      }
      if (MyProfile) {
        // showToast(response.data.data.message, 'success');
        navigation.navigate('BottomTab');
      } else {
        navigation.navigate('CompleteRide');
      }
    } catch (err: any) {
      const status = err?.response?.status;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
    } finally {
      hideLoader();
    }
  };

  return (
    <ImageBackground source={images.bg1} style={styles.background}>
      <SafeAreaView style={styles.safearea}>
        <TouchableOpacity
          style={styles.titleContainer}
          onPress={() => navigation.goBack()}>
          <IconSvgView source={Back} />
          <Text style={styles.title}>{t('add_qr')}</Text>
        </TouchableOpacity>
        <FadingHorizontalLine />
        <View>
          <Input
            inputTitle={t('upi_label')}
            placeholder="eg: john.doe@bank"
            onChange={value => {
              setUpi(value);
              if (!touched) {
                setTouched(true);
              }
            }}
            value={upi}
            error={upiError}
          />
        </View>
        {!upiError && <Text style={styles.noteTxt}>{t('upi_note')}</Text>}
        {driver?.paymentDetails?.upi_id && (
          <>
            <FadingHorizontalLine />
            <Text
              style={[
                styles.title,
                {textAlign: 'center', marginVertical: spacing.md},
              ]}>
              {t('your_qr')}
            </Text>
            <View style={{height: 200, alignItems: 'center'}}>
              <QrCode
                upiID={driver.paymentDetails.upi_id}
                route={MyProfile}
                size={150}
              />
            </View>
          </>
        )}

        <FlexContainer justifyContent="flex-end">
          <Button
            title={
              driver?.paymentDetails?.upi_id ? t('update_upi') : t('add_upi')
            }
            onPress={handleUpi}
          />
        </FlexContainer>
      </SafeAreaView>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  background: {
    flex: 1,
    resizeMode: 'cover',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  title: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.h4,
    marginLeft: spacing.xl,
    color: colors.lightGrey,
    width: '90%',
  },
  safearea: {
    flex: 1,
    margin: spacing.xl,
  },
  noteTxt: {
    fontSize: sizes.body,
    color: colors.grey,
    marginVertical: spacing.lg,
    fontFamily: GeistFont.regular,
  },
});

export default AddQr;
