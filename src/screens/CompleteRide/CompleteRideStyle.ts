import {StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  safeArea: {
    flex: 1,
    margin: spacing.xl ,
  },

  backContainer: {
    position: 'absolute',
    top: 40,
    left: 0,
  },

  card: {
    backgroundColor: 'white',
    marginTop: spacing.xl * 4,
    borderRadius: spacing.sm,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xxl,
  },

  title: {
    fontSize: sizes.h4,
    color: colors.black,
    fontFamily: EBGaramondFont.regular,
  },

  rupeeText: {
    fontSize: sizes.h3,
    color: colors.black,
    fontFamily: EBGaramondFont.regular,
  },

  getMoneyTxt: {
    fontSize: sizes.h5,
    color: colors.white,
    fontFamily: EBGaramondFont.regular,
    textAlign: 'center',
    marginTop: spacing.xxl * 2,
  },

  qrImage: {
    resizeMode: 'cover',
    alignItems: 'center',
    justifyContent: 'center',
    width: 180,
    height: 180,
    marginVertical: spacing.lg,
  },
});
