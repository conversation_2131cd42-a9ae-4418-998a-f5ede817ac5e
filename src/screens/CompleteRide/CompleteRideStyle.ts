import {StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  safeArea: {
    flex: 1,
    margin: 0, 
    paddingHorizontal: spacing.xl, 
    position: 'relative',
    paddingTop: 0,
  },

  backContainer: {
    position: 'absolute',
    top: 40,
    left: spacing.xl, 
    zIndex: 10,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 5,
  },

  card: {
    backgroundColor: 'white',
    marginTop: spacing.xl * 4,
    borderRadius: spacing.sm,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xxl,
  },

  title: {
    fontSize: sizes.h4,
    color: colors.black,
    fontFamily: EBGaramondFont.regular,
  },

  rupeeText: {
    fontSize: sizes.h3,
    color: colors.black,
    fontFamily: EBGaramondFont.regular,
  },

  bonusText: {
    fontSize: sizes.h5,
    color: colors.lightGreen,
    fontFamily: EBGaramondFont.regular,
    marginTop: spacing.xs,
  },

  bonusInfoContainer: {
    backgroundColor: '#f8f8f8',
    borderRadius: spacing.sm,
    padding: spacing.md,
    marginTop: spacing.lg,
    width: '100%',
  },

  bonusInfoText: {
    fontSize: sizes.body,
    color: colors.darkGrey,
    fontFamily: EBGaramondFont.regular,
    textAlign: 'center',
  },

  getMoneyTxt: {
    fontSize: sizes.h5,
    color: colors.white,
    fontFamily: EBGaramondFont.regular,
    textAlign: 'center',
    marginTop: spacing.xxl * 2,
  },

  qrImage: {
    resizeMode: 'cover',
    alignItems: 'center',
    justifyContent: 'center',
    width: 180,
    height: 180,
    marginVertical: spacing.lg,
  },

  scrollContainer: {
    flex: 1,
    marginBottom: 70, 
    paddingTop: 20, 
  },

  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingVertical: spacing.md,
    backgroundColor: 'transparent',
    paddingHorizontal: spacing.xl, 
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 5,
  },
});
