import {Platform, StyleSheet, TextStyle, ViewStyle} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  navigateContainer: {
    position: 'absolute',
    top: 40,
    marginVertical: spacing.xl,
    backgroundColor: colors.darkCharcoal,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    width: '100%',
  },

  locationNavigationContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.davyGrey,
    borderRadius: 1,
    height: spacing.xxl * 2,
    width: spacing.xxl * 2,
  },

  pickupContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '85%',
  },

  pickup: {
    color: colors.lightGrey,
    fontSize: sizes.h6,
    fontFamily: GeistFont.variable,
    marginTop: spacing.xl,
  },

  backgroundImage: {
    resizeMode: 'cover',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },

  safeArea: {
    flex: 1,
    padding: spacing.lg,
  },

  locationContainer: {
    paddingHorizontal: spacing.xl,
    marginTop: spacing.lg,
  },

  location: {
    color: colors.lightGrey,
    fontSize: sizes.h6,
    fontFamily: GeistFont.variable,
    marginHorizontal: spacing.md,
  },

  title: {
    fontSize: sizes.h2,
    color: colors.white,
    fontFamily: EBGaramondFont.regular,
    marginVertical: spacing.md,
  },

  rupeeText: {
    marginHorizontal: spacing.md,
    fontSize: sizes.h6,
    color: colors.white,
    fontFamily: EBGaramondFont.regular,
  },

  confirmBtn: {
    marginTop: spacing.xl,
    marginHorizontal: spacing.md,
  },

  locationPositionContainer: {
    position: 'absolute',
    bottom: 225,
    right: 0,
    margin: spacing.xl,
  },

  supportIconContainer: {
    position: 'absolute',
    top: 175,
    right: 20,
    zIndex: 999,
  },
  supportIcon: {
    width: 50,
    height: 50,
    borderRadius: 5,
    backgroundColor: colors.davyGrey,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
