import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  ImageBackground,
  SafeAreaView,
  TouchableOpacity,
  Image,
  AppState,
} from 'react-native';
import {colors, images} from '../../constants';
import styles from './RideRouteStyle';
import Geolocation from 'react-native-geolocation-service';
import Button from '../../components/Button/Button';
import {useTranslation} from 'react-i18next';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import {spacing} from '../../constants/theme';
import diamondInactive from '../../icons/diamondGrey.svg';
import fadingLine from '../../icons/fading_line.svg';
import MapComponent from '../../components/Map/MapComponent';
import MapView, {LatL<PERSON>, <PERSON>er, Polyline} from 'react-native-maps';
import dropIcon from '../../icons/dropIcon.svg';
import navigateIcon from '../../icons/navigation.svg';
import support from '../../icons/support.svg';
import {getDirections, zoomToRoute} from '../../utils/MapUtils';
import OpenMaps from '../PickupSpot/OpenMaps';
import ellipseActive from '../../icons/ellipseActive.svg';
import {useRideDetails} from '../../hooks/useRideDetailsContext';
import {useLoader} from '../../hooks/useLoader';
import myLocation from '../../icons/my_location.svg';
import {userLocationContext} from '../../hooks/userLocationContext';
import {useFocusEffect} from '@react-navigation/native';
import {useDriver} from '../../hooks/useDriver';
import RideService from '../../services/RideService';
import SupportMenu from '../../components/SupportMenu/SupportMenu';
import {useKeepAwake} from '../../hooks/useKeepAwake';
import ConfirmationModal from '../../components/ConfirmationModal';
import * as Sentry from '@sentry/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {navigationRef} from '../../router/navigationService';

interface RideRouteScreenProps {
  navigation: any;
}

const RideRoute: React.FC<RideRouteScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {showLoader, hideLoader} = useLoader();
  const {userLocation, setUserLocation} = userLocationContext();
  const {driver} = useDriver();
  const mapViewRef = useRef<MapView>(null);
  const supportIconRef = useRef<TouchableOpacity>(null);
  const [supportModalVisible, setSupportModalVisible] = useState(false);
  const [confirmationModalVisible, setConfirmationModalVisible] =
    useState(false);

  useKeepAwake();
  const [iconPosition, setIconPosition] = useState({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  });
  const [address, setAddress] = useState<string>('');
  const [region, setRegion] = useState<LatLng | null>(null);
  const {routeCoordinates, setRouteCoordinates, tripDetails} = useRideDetails();
  const [btnLoading, setBtnLoading] = useState<boolean>(false);
  const [markerReady, setMarkerReady] = useState(false);
  const [iconLoaded, setIconLoaded] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [markerRotation, setMarkerRotation] = useState<number>(0);

  useEffect(() => {
    const subscription = AppState.addEventListener(
      'change',
      async nextAppState => {
        const rideStatus = await AsyncStorage.getItem('rideStatus');
        if (rideStatus) {
          if (rideStatus === 'COMPLETED') {
            navigationRef.current?.reset({
              index: 0,
              routes: [{name: 'CompleteRide'}],
            });
          }
        }
      },
    );
    return () => {
      subscription.remove();
    };
  }, []);

  const getVehicleImage = useCallback(() => {
    if (!driver?.vehicles || !driver.vehicles[0]) {
      return images.autoTop;
    }

    const fuelType = driver.vehicles[0].fuel_type;
    switch (fuelType) {
      case 'PETROL':
        return images.autoDiesel;
      case 'DIESEL':
        return images.autoDiesel;
      case 'CNG':
        return images.autoCng;
      case 'ELECTRIC':
        return images.autoElectric;
      default:
        return images.autoTop;
    }
  }, [driver?.vehicles]);

  const toggleSupportMenu = () => {
    if (supportIconRef.current) {
      supportIconRef.current.measure(
        (
          _x: number,
          _y: number,
          width: number,
          height: number,
          pageX: number,
          pageY: number,
        ) => {
          setIconPosition({x: pageX, y: pageY, width, height});
          setSupportModalVisible(!supportModalVisible);
        },
      );
    } else {
      setSupportModalVisible(!supportModalVisible);
    }
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setMarkerReady(true);
    }, 1500);

    return () => clearTimeout(timer);
  }, [driver]);

  useEffect(() => {
    const getCurrentLocation = async () => {
      try {
        Geolocation.getCurrentPosition(
          (position: {coords: {latitude: any; longitude: any}}) => {
            const {latitude, longitude} = position.coords;

            setUserLocation({
              latitude,
              longitude,
            });
          },
          (error: any) => {
            console.log('Error getting location:', error);
          },
          {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 10000,
          },
        );
      } catch (error) {
        console.error('Error requesting location:', error);
      }
    };
    getCurrentLocation();

    const locationInterval = setInterval(() => {
      getCurrentLocation();
    }, 10000);

    return () => clearInterval(locationInterval);
  }, []);

  const calculateRotation = useCallback(
    (coords: {latitude: number; longitude: number}[]) => {
      if (coords.length < 2) return 0;

      const point1 = coords[0];
      const point2 = coords[1];

      const dx = point2.longitude - point1.longitude;
      const dy = point2.latitude - point1.latitude;

      let angle = (Math.atan2(dx, dy) * 180) / Math.PI;

      return angle + 180;
    },
    [],
  );

  const fetchDistance = async () => {
    showLoader();
    try {
      if (tripDetails && userLocation) {
        const currentLocation = `${userLocation.latitude},${userLocation.longitude}`;
        const userDropLocation = `${tripDetails.destination.latitude},${tripDetails.destination.longitude}`;
        const distanceForDrop = await getDirections(
          currentLocation,
          userDropLocation,
        );

        if (distanceForDrop?.points?.length) {
          setRouteCoordinates(distanceForDrop.points || []);

          if (mapViewRef.current) {
            setTimeout(() => {
              zoomToRoute(mapViewRef, distanceForDrop.points);
            }, 500);
          }

          if (distanceForDrop.points && distanceForDrop.points.length >= 2) {
            setMarkerRotation(calculateRotation(distanceForDrop.points));
          }
        }
      }
    } catch (err: any) {
      console.log('Error fetching route:', err);
    } finally {
      hideLoader();
    }
  };

  useFocusEffect(
    useCallback(() => {
      (async () => {
        await fetchDistance();
      })();
    }, []),
  );

  const handleRideComplete = async () => {
    setBtnLoading(true);
    try {
      console.log('RideRoute-Completing ride for trip:', tripDetails?.id);
      const response = await RideService.completeRide(tripDetails?.id);
      if (response) {
        await AsyncStorage.multiSet([['rideStatus', 'COMPLETED']]);
      }
    } catch (error: any) {
      console.log('RideRoute-Error completing ride:', error.response.data);

      Sentry.captureException(error, {
        tags: {
          tripId: tripDetails?.id,
          driverId: driver?.id,
          action: 'ride_completion',
        },
      });
    } finally {
      setBtnLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      if (routeCoordinates.length > 0) {
        const interval = setInterval(() => {
          if (mapViewRef.current) {
            zoomToRoute(mapViewRef, routeCoordinates);
            clearInterval(interval);
          } else {
            console.log('mapViewRef is not set');
          }
        }, 500);

        return () => clearInterval(interval);
      }
    }, [routeCoordinates, mapViewRef.current]),
  );

  const handleStart = () => {
    if (routeCoordinates.length > 0) {
      const destination = routeCoordinates[routeCoordinates.length - 1];
      OpenMaps(destination);
    }
  };

  const handleGetLocation = () => {
    if (routeCoordinates.length > 0) {
      const interval = setInterval(() => {
        if (mapViewRef.current) {
          zoomToRoute(mapViewRef, routeCoordinates);
          clearInterval(interval);
        } else {
          console.log('mapViewRef is not set');
        }
      }, 500);

      setTimeout(() => clearInterval(interval), 5000);

      return () => clearInterval(interval);
    }
  };

  useFocusEffect(
    useCallback(() => {
      if (tripDetails && userLocation) {
        fetchDistance();
      }
    }, [userLocation, tripDetails?.id]),
  );

  useEffect(() => {
    if (routeCoordinates.length > 0) {
      setIconLoaded(false);
      setMarkerReady(false);

      setTimeout(() => {
        setRetryCount(prev => prev + 1);
        setMarkerReady(true);
      }, 200);

      if (routeCoordinates.length >= 2) {
        setMarkerRotation(calculateRotation(routeCoordinates));
      }
    }
  }, [routeCoordinates, calculateRotation]);

  useEffect(() => {
    if (userLocation && routeCoordinates.length > 1) {
      let minDistance = Infinity;
      let closestIndex = 0;

      routeCoordinates.forEach((coord, index) => {
        const distance = Math.sqrt(
          Math.pow(coord.latitude - userLocation.latitude, 2) +
            Math.pow(coord.longitude - userLocation.longitude, 2),
        );

        if (distance < minDistance) {
          minDistance = distance;
          closestIndex = index;
        }
      });

      const nextIndex = Math.min(closestIndex + 1, routeCoordinates.length - 1);

      if (closestIndex !== nextIndex) {
        const nextPoint = routeCoordinates[nextIndex];
        const dx = nextPoint.longitude - userLocation.longitude;
        const dy = nextPoint.latitude - userLocation.latitude;

        let angle = (Math.atan2(dx, dy) * 180) / Math.PI;
        setMarkerRotation(angle + 180);
      }
    }
  }, [userLocation, routeCoordinates]);

  useKeepAwake();

  return (
    <View style={{backgroundColor: colors.darkGrey, flex: 1}}>
      <MapComponent
        ref={mapViewRef}
        marker={false}
        setAddress={setAddress}
        region={region}
        showLocation={false}
        setRegion={setRegion}>
        <View>
          {userLocation && routeCoordinates.length > 0 && (
            <Marker
              key={`auto-${retryCount}`}
              anchor={{x: 0.5, y: 0.5}}
              tracksViewChanges={!markerReady || !iconLoaded}
              coordinate={userLocation}
              title="Current Location">
              <View
                onLayout={() => {
                  setTimeout(() => setIconLoaded(true), 300);
                }}>
                <Image
                  source={getVehicleImage()}
                  style={{
                    width: 60,
                    height: 60,
                    transform: [{rotate: `${markerRotation}deg`}],
                  }}
                  resizeMode="contain"
                />
              </View>
            </Marker>
          )}
          {routeCoordinates.length > 0 && (
            <Marker
              key={`drop-${retryCount}`}
              anchor={{x: 0.5, y: 0.5}}
              tracksViewChanges={false}
              coordinate={routeCoordinates[routeCoordinates.length - 1]}
              title="Drop Location">
              <IconSvgView source={dropIcon} />
            </Marker>
          )}
          {routeCoordinates.length > 1 && (
            <Polyline
              coordinates={routeCoordinates}
              strokeColor="white"
              strokeWidth={4}
              key={`polyline-${routeCoordinates.length}`}
            />
          )}
        </View>
      </MapComponent>

      <View style={styles.supportIconContainer}>
        <TouchableOpacity
          ref={supportIconRef}
          style={styles.supportIcon}
          onPress={toggleSupportMenu}
          accessibilityLabel="Get support">
          <IconSvgView size={30} source={support} />
        </TouchableOpacity>
      </View>

      <View style={styles.locationPositionContainer}>
        <TouchableOpacity
          style={styles.locationNavigationContainer}
          onPress={handleGetLocation}>
          <IconSvgView width={24} source={myLocation} />
        </TouchableOpacity>
      </View>
      <View style={styles.navigateContainer}>
        <View style={styles.pickupContainer}>
          <View>
            <IconSvgView width={12} source={ellipseActive} />
          </View>
          <View>
            <Text numberOfLines={2} style={styles.location}>
              {tripDetails?.destination_address}
            </Text>
          </View>
        </View>
        <TouchableOpacity
          style={{
            width: 50,
            height: 50,
            borderRadius: 50,
            borderWidth: 2,
            borderColor: colors.white,
            justifyContent: 'center',
            alignItems: 'center',
          }}
          onPress={handleStart}>
          <IconSvgView size={30} source={navigateIcon} />
        </TouchableOpacity>
      </View>

      <ImageBackground source={images.bg2} style={styles.backgroundImage}>
        <SafeAreaView style={styles.safeArea}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <View style={styles.locationContainer}>
              <View style={{flexDirection: 'row'}}>
                <View style={{alignItems: 'flex-start'}}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <IconSvgView
                      width={16}
                      source={diamondInactive}
                      svgStyle={{marginLeft: -spacing.xs}}
                    />
                    <Text style={styles.location} numberOfLines={2}>
                      {tripDetails?.source_address}
                    </Text>
                  </View>
                  <View>
                    <IconSvgView
                      svgStyle={{marginLeft: spacing.xxs}}
                      source={fadingLine}
                      size={5}
                      height={30}
                    />
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <IconSvgView width={12} source={ellipseActive} />
                    <Text style={styles.location} numberOfLines={2}>
                      {tripDetails?.destination_address}
                    </Text>
                  </View>
                </View>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}></View>
            </View>
          </View>
          <View style={{justifyContent: 'flex-end'}}>
            <Button
              title={t('complete_ride')}
              style={styles.confirmBtn}
              onPress={() => {
                setConfirmationModalVisible(true);
              }}
              loading={btnLoading}
            />
          </View>
        </SafeAreaView>
      </ImageBackground>

      {supportModalVisible && (
        <SupportMenu
          visible={supportModalVisible}
          onClose={() => setSupportModalVisible(false)}
          position={iconPosition}
        />
      )}

      {confirmationModalVisible && (
        <ConfirmationModal
          visible={confirmationModalVisible}
          onCancel={() => setConfirmationModalVisible(false)}
          onConfirm={handleRideComplete}
          title={t('confirm_complete_ride')}
          message={t('are_you_sure_complete_ride')}
        />
      )}
    </View>
  );
};

export default RideRoute;
