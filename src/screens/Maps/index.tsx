import React, {useEffect, useRef, useState, useCallback} from 'react';
import {
  Platform,
  Text,
  TouchableOpacity,
  View,
  ImageBackground,
  AppState,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useTranslation} from 'react-i18next';
import styles from './MapStyle';
import ToggleSwitch from '../../components/Toggle/Toggle';
import MapComponent from '../../components/Map/MapComponent';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import {userLocationContext} from '../../hooks/userLocationContext';
import myLocation from '../../icons/my_location.svg';
import timer from '../../icons/timer.svg';
import offer from '../../icons/offer.svg';
import MapView, {LatLng} from 'react-native-maps';
import {useKeepAwake} from '../../hooks/useKeepAwake';
import EarningsService from '../../services/EarningsService';
import {useDriver} from '../../hooks/useDriver';
import {useLoader} from '../../hooks/useLoader';
import useBackgroundLocation from '../../hooks/useBackgroundLocation';
import useBackgroundLocationAndroid from '../../hooks/useBackgrounLocationAndroid';
import images from '../../constants/images';
import {useGeofence} from '../../hooks/useGeofence';
import {STATUS_CODE} from '../../constants/constants';
import {useFocusEffect} from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import {
  checkDeviceLocationServices,
  checkForegorundLocationPermission,
  checkLocationPermission,
  requestCallPhonePermission,
  requestNotificationPermission,
} from '../../constants/permissions';
import {getFcmToken} from '../../../firebase.ts';
import {useToast} from '../../components/Toast/Toast.tsx';
import {colors} from '../../constants/fonts.ts';
import DriverService from '../../services/DriverService.ts';
import WalletService from '../../services/WalletService.ts';
import {playOfferSound, stopOfferSound} from '../../utils/Sound.ts';
import * as Sentry from '@sentry/react-native';

interface MapScreenProps {
  navigation: any;
}

const Maps: React.FC<MapScreenProps> = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const {userLocation} = userLocationContext();
  const {driver, fetchDriver, isSystemPaused} = useDriver();

  useKeepAwake();
  const [active, setActive] =
    Platform.OS === 'android'
      ? useBackgroundLocationAndroid({driverId: driver?.id})
      : useBackgroundLocation({driverId: driver?.id});
  const {loading} = useLoader();
  const {showGeofenceModal} = useGeofence();
  const mapViewRef = useRef<MapView | null>(null);
  const [address, setAddress] = useState('');
  const [region, setRegion] = useState<LatLng | null>(userLocation);
  const [earnings, setEarnings] = useState<string>('');
  const [locationPermission, setLocationPermission] = useState<boolean>(false);
  const {stopGeofencing, startGeofencing} = useGeofence();
  const {showToast} = useToast();
  const [minWalletBalance, setMinWalletBalance] = useState<number>(0);
  const [walletBalance, setWalletBalance] = useState<number>(0);
  const [showOfferCard, setShowOfferCard] = useState<boolean>(false);
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [offerDetails, setOfferDetails] = useState<any>(null);
  const [hasTimer, setHasTimer] = useState<boolean>(false);
  const [wasOfferActive, setWasOfferActive] = useState<boolean>(false);

  const OFFER_END_TIME_KEY = 'offer_end_time';

  const handleToggle = async () => {
    try {
      const locationGranted = await checkDeviceLocationServices();

      if (isSystemPaused) {
        showToast(t('system_paused'), 'failure');
        return;
      }
      if (!active && walletBalance < minWalletBalance) {
        showToast(t('wallet_balance_low'), 'failure');
        return;
      }

      if (locationGranted) {
        setActive(!active);
      } else {
        setActive(false);
      }
    } catch (error: any) {
      console.error('Error toggling active state:', error);
      Sentry.captureException(error, {
        tags: {
          driverId: driver?.id,
          action: 'toggle_active_state',
          current_state: active ? 'active' : 'inactive',
        },
      });
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      (async () => {
        startGeofencing();
        getMinimumWalletBalance();
      })();

      return () => {
        stopGeofencing();
      };
    }, []),
  );

  const getMinimumWalletBalance = async () => {
    try {
      const response = await DriverService.getDriverConfig();
      const walletResponse = await WalletService.getWallet();

      const minimumWallet = response.data.data.find(
        (item: any) => item.key === 'driver_minimum_wallet_balance',
      );

      const offerConfig = response.data.data.find(
        (item: any) => item.key === 'offers',
      );

      const minValue = parseFloat(minimumWallet.value);
      const currentBalance = parseFloat(walletResponse.data.data.totalBalance);

      setMinWalletBalance(minValue);
      setWalletBalance(currentBalance);

      if (offerConfig && offerConfig.value) {
        processOfferData(offerConfig.value);
      }
    } catch (error: any) {
      console.error('Failed to fetch wallet or config:', error);
      Sentry.captureException(error, {
        tags: {
          driverId: driver?.id,
          action: 'fetch_wallet_config',
          status_code: error?.response?.status,
        },
      });
    }
  };

  const processOfferData = (offerData: any) => {
    try {
      const data =
        typeof offerData === 'string' ? JSON.parse(offerData) : offerData;

      const isOfferEnabled = data && data.enabled;
      const wasDisabled = offerDetails?.isDisabled;

      const currentLanguage = i18n.language;
      let langKey = currentLanguage;

      if (!data[langKey]) {
        langKey = 'en';
      }

      const offerContent = data[langKey];
      if (!offerContent) {
        setShowOfferCard(false);
        return;
      }

      const timerData = offerContent.timer || data.timer;
      setOfferDetails({
        ...offerContent,
        isDisabled: !isOfferEnabled,
        timer: timerData,
      });

      if (!isOfferEnabled) {
        setShowOfferCard(false);
        AsyncStorage.removeItem(OFFER_END_TIME_KEY).catch(error =>
          console.error('Failed to remove offer end time:', error),
        );
      } else if (
        wasDisabled &&
        timerData &&
        timerData.startTime &&
        timerData.endTime
      ) {
        setHasTimer(true);

        AsyncStorage.removeItem(OFFER_END_TIME_KEY)
          .then(() => {
            checkTimerVisibility(timerData);
          })
          .catch(console.error);
      } else if (timerData && timerData.startTime && timerData.endTime) {
        setHasTimer(true);
        checkTimerVisibility(timerData);
      } else {
        setHasTimer(false);
        setShowOfferCard(true);
      }
    } catch (error: any) {
      console.error('Error processing offer data:', error);
      setShowOfferCard(false);
      Sentry.captureException(error, {
        tags: {
          driverId: driver?.id,
          action: 'process_offer_data',
        },
        extra: {
          offerData: typeof offerData === 'string' ? offerData : JSON.stringify(offerData),
        },
      });
    }
  };

  const checkTimerVisibility = (timerData: {
    startTime: string;
    endTime: string;
  }) => {
    if (offerDetails && offerDetails.isDisabled) {
      setShowOfferCard(false);
      return;
    }

    const now = new Date();
    const currentHour = now.getHours();
    const currentMinutes = now.getMinutes();
    const currentTimeMinutes = currentHour * 60 + currentMinutes;
    const startTimeParts = timerData.startTime.split(':');
    const startHour = parseInt(startTimeParts[0], 10);
    const startMinutes = parseInt(startTimeParts[1], 10);
    const startTimeMinutes = startHour * 60 + startMinutes;

    const endTimeParts = timerData.endTime.split(':');
    const endHour = parseInt(endTimeParts[0], 10);
    const endMinutes = parseInt(endTimeParts[1], 10);
    const endTimeMinutes = endHour * 60 + endMinutes;

    let isTimeToShowOffer = false;

    if (startTimeMinutes < endTimeMinutes) {
      isTimeToShowOffer =
        currentTimeMinutes >= startTimeMinutes &&
        currentTimeMinutes < endTimeMinutes;
    } else {
      isTimeToShowOffer =
        currentTimeMinutes >= startTimeMinutes ||
        currentTimeMinutes < endTimeMinutes;
    }

    if (isTimeToShowOffer) {
      let offerEndTime: number;

      if (startTimeMinutes < endTimeMinutes) {
        // End time is within the same day
        const endTimeToday = new Date();
        endTimeToday.setHours(endHour, endMinutes, 0, 0);
        offerEndTime = endTimeToday.getTime();
      } else {
        // End time is on the next day
        const endTimeTomorrow = new Date();
        if (currentTimeMinutes >= startTimeMinutes) {
          // Current time is after start time, so end time is tomorrow
          endTimeTomorrow.setDate(endTimeTomorrow.getDate() + 1);
        }
        endTimeTomorrow.setHours(endHour, endMinutes, 0, 0);
        offerEndTime = endTimeTomorrow.getTime();
      }

      const remainingMs = offerEndTime - now.getTime();
      const remainingSec = Math.floor(remainingMs / 1000);

      setTimeLeft(remainingSec);
      setShowOfferCard(true);

      AsyncStorage.setItem('offer_is_active', 'true').catch(error =>
        console.error('Failed to store offer active status:', error),
      );

      AsyncStorage.setItem(OFFER_END_TIME_KEY, offerEndTime.toString()).catch(
        error => console.error('Failed to store offer end time:', error),
      );
    } else {
      setTimeLeft(-1);
      setShowOfferCard(true);

      AsyncStorage.setItem('offer_is_active', 'false').catch(error =>
        console.error('Failed to store offer active status:', error),
      );
    }
  };

  useEffect(() => {
    const subscription = AppState.addEventListener(
      'change',
      async nextAppState => {
        if (nextAppState === 'active') {
          const permissionGranted = await checkForegorundLocationPermission();
          const backgroundGranted = await checkLocationPermission();
          if (permissionGranted || backgroundGranted) {
            setLocationPermission(permissionGranted || backgroundGranted);
          }
        }
      },
    );

    return () => {
      subscription.remove();
    };
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      const checkPermissions = async () => {
        const permissionGranted = await checkForegorundLocationPermission();
        const backgroundGranted = await checkLocationPermission();

        setLocationPermission(permissionGranted || backgroundGranted);
      };
      checkPermissions();
    }, []),
  );

  useEffect(() => {
    if (userLocation) {
      handleGetLocation();
    }
  }, [userLocation]);

  // Helper function for checking and initializing the timer
  const checkAndInitializeTimer = async () => {
    try {
      // If no offer details, nothing to do
      if (!offerDetails) {
        return;
      }

      // If the offer is disabled from backend, don't show it
      if (offerDetails.isDisabled) {
        setShowOfferCard(false);
        return;
      }

      // Check if there's a stored end time for the offer
      const storedEndTimeStr = await AsyncStorage.getItem(OFFER_END_TIME_KEY);

      // Check if we know the offer's active status
      const isActiveStr = await AsyncStorage.getItem('offer_is_active');
      const isActive = isActiveStr === 'true';

      // If we have timer data, always recalculate to ensure correct state
      if (offerDetails.timer) {
        checkTimerVisibility(offerDetails.timer);
        return;
      }

      if (!hasTimer) {
        setShowOfferCard(true);
        return;
      }

      if (storedEndTimeStr) {
        const endTime = parseInt(storedEndTimeStr, 10);
        const now = Date.now();
        const remainingMs = Math.max(0, endTime - now);
        const remainingSec = Math.floor(remainingMs / 1000);

        if (remainingSec <= 0) {
          setTimeLeft(-1);
          setShowOfferCard(true);
          await AsyncStorage.setItem('offer_is_active', 'false');
          await AsyncStorage.removeItem(OFFER_END_TIME_KEY);
        } else if (isActive) {
          setTimeLeft(remainingSec);
          setShowOfferCard(true);
        } else {
          setTimeLeft(-1);
          setShowOfferCard(true);
        }
      } else if (offerDetails.timer) {
        checkTimerVisibility(offerDetails.timer);
      } else {
        setShowOfferCard(true);
      }
    } catch (error) {
      console.error('Error reading/writing offer timer:', error);
    }
  };

  useEffect(() => {
    if (offerDetails) {
      checkAndInitializeTimer();
    }
  }, [offerDetails?.isDisabled]);

  useFocusEffect(
    React.useCallback(() => {
      if (!offerDetails) {
        return;
      }

      checkAndInitializeTimer();

      const timerInterval = setInterval(() => {
        setTimeLeft(prevTime => {
          if (prevTime < 0) {
            return prevTime;
          }

          if (offerDetails && offerDetails.isDisabled) {
            return 0;
          }

          if (prevTime <= 1) {
            AsyncStorage.setItem('offer_is_active', 'false').catch(
              console.error,
            );
            AsyncStorage.removeItem(OFFER_END_TIME_KEY).catch(console.error);

            if (offerDetails && offerDetails.timer) {
              checkTimerVisibility(offerDetails.timer);
            } else {
              setTimeLeft(-1);
            }

            return -1; // Signal to show disabled-style card
          }

          return prevTime - 1;
        });
      }, 1000);

      const subscription = AppState.addEventListener('change', nextAppState => {
        if (nextAppState === 'active') {
          checkAndInitializeTimer();
        }
      });

      return () => {
        clearInterval(timerInterval);
        subscription.remove();
      };
    }, [offerDetails]),
  );

  const fetchEarnings = async () => {
    try {
      const response = await EarningsService.getDailyEarnings();
      if (response.status === STATUS_CODE.ok) {
        setEarnings(response.data.data[0].totalFare);
      }
    } catch (err: any) {
      const status = err?.response?.status;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
    }
  };

  const requestPermissionsSequentially = async () => {
    const hasLocationPermission = await checkForegorundLocationPermission();
    if (!hasLocationPermission) {
      await checkLocationPermission();
    }

    await new Promise(resolve => setTimeout(resolve, 1000));

    const hasPhonePermission = await requestCallPhonePermission();
    if (!hasPhonePermission) {
      await requestCallPhonePermission();
    }

    await getFcmToken();
  };

  useEffect(() => {
    const fetchData = async () => {
      await requestPermissionsSequentially();
      await fetchDriver();
      await fetchEarnings();
      await getMinimumWalletBalance();
    };
    fetchData();

    const refreshInterval = setInterval(() => {
      getMinimumWalletBalance(); 
    }, 120000); 

    return () => {
      clearInterval(refreshInterval);
    };
  }, []);

  const handleGetLocation = () => {
    const interval = setInterval(() => {
      if (mapViewRef.current && userLocation) {
        mapViewRef.current.animateToRegion(
          {
            latitude: userLocation?.latitude,
            longitude: userLocation?.longitude,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          },
          100,
        );
        clearInterval(interval);
      }
    }, 200);

    setTimeout(() => clearInterval(interval), 5000);

    return () => clearInterval(interval);
  };
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleViewOfferDetails = () => {
    if (offerDetails) {
      navigation.navigate('Promotions', {driverConfigOffer: offerDetails});
    } else {
      showToast(t('boost_offer_details'), 'success');
    }
  };

  useEffect(() => {
    if (
      !showOfferCard ||
      timeLeft < 0 ||
      !offerDetails ||
      offerDetails.isDisabled
    ) {
      return;
    }

    const seconds = timeLeft % 60;
    const minutes = Math.floor((timeLeft % 3600) / 60);

    if (minutes == 0 && seconds == 0 && timeLeft > 0) {
      playOfferSound();
    }

    return () => {
      stopOfferSound();
    };
  }, [timeLeft, showOfferCard, offerDetails]);

  return (
    <View style={styles.mapContainer}>
      <ImageBackground source={images.map} style={styles.background}>
        {!loading && locationPermission ? (
          !showGeofenceModal ? (
            <MapComponent
              ref={mapViewRef}
              setAddress={setAddress}
              marker={false}
              showLocation={true}
              region={region}
              setRegion={setRegion}
            />
          ) : (
            <View style={styles.modalContainer}>
              <View style={styles.modalContent}>
                <Text style={styles.modalText}>{t('not_serviceable')}</Text>
              </View>
            </View>
          )
        ) : (
          <ImageBackground source={images.map} style={styles.background} />
        )}
      </ImageBackground>
      <View style={styles.rateContainer}>
        <Text style={styles.rateTxt}>
          {t('rupee')} {earnings ? earnings : 0}
        </Text>
      </View>

      {showOfferCard &&
        offerDetails &&
        !loading &&
        locationPermission &&
        !showGeofenceModal && (
          <View style={styles.offerCardContainer}>
            <LinearGradient
              colors={
                timeLeft < 0 // Outside time range, use disabled style
                  ? ['#6A6A6A', '#414141']
                  : ['#E5574D', '#E69400']
              }
              start={{x: 0, y: 0}}
              end={{x: 1, y: 0}}
              style={[
                styles.offerCardGradient,
                timeLeft < 0 && {opacity: 0.8}, // Use disabled style opacity for outside time range
              ]}>
              <View style={styles.offerCardHeader}>
                {hasTimer && (
                  <View style={styles.clockIcon}>
                    <IconSvgView size={35} source={timer} />
                  </View>
                )}
                <Text
                  style={[
                    styles.offerCardTitle,
                    timeLeft < 0 && {color: '#CCCCCC'}, // Use disabled style for outside time range
                  ]}
                  numberOfLines={2}
                  adjustsFontSizeToFit={true}>
                  {offerDetails.title}
                </Text>
              </View>
              <Text
                style={[
                  styles.offerCardDescription,
                  timeLeft < 0 && {color: '#BBBBBB'}, // Use disabled style for outside time range
                ]}
                numberOfLines={3}>
                {offerDetails.subtitle}
              </Text>
              <View
                style={[
                  styles.offerCardFooter,
                  !hasTimer && {justifyContent: 'center'},
                ]}>
                {hasTimer && (
                  <>
                    {/* Show "Starts at XX:XX" when timeLeft is negative (outside time range) */}
                    {timeLeft < 0 ? (
                      <Text style={styles.startTimeText}>
                        {`${t('starts_at')} ${
                          offerDetails.timer?.startTime || ''
                        }`}
                      </Text>
                    ) : (
                      <Text style={styles.offerCardTimer} numberOfLines={1}>
                        {formatTime(timeLeft)}
                      </Text>
                    )}
                  </>
                )}
                <TouchableOpacity
                  style={[
                    styles.offerCardButton,
                    timeLeft < 0 && {backgroundColor: '#505050'}, // Use disabled style for outside time range
                  ]}
                  onPress={handleViewOfferDetails}>
                  <Text
                    style={[
                      styles.offerCardButtonText,
                      timeLeft < 0 && {color: '#CCCCCC'}, // Use disabled style for outside time range
                    ]}
                    numberOfLines={2}>
                    {t('view_details')}
                  </Text>
                </TouchableOpacity>
              </View>
            </LinearGradient>
          </View>
        )}

      {!loading && locationPermission && !showGeofenceModal && (
        <>
          <View style={styles.navbarContainer}>
            <View style={styles.contentBtn}>
              <Text
                numberOfLines={2}
                style={[
                  styles.contentText,
                  {color: active ? colors.fluorescentGreen : colors.white},
                ]}>
                {active ? t('go_online') : t('go_offline')}
              </Text>
              <ToggleSwitch
                isOn={!!active}
                handleToggle={handleToggle}
                activeColor={colors.fluorescentGreen}
              />
            </View>
          </View>
          <View style={styles.locationPositionContainer}>
            <TouchableOpacity
              style={styles.locationContainer}
              onPress={handleGetLocation}>
              <IconSvgView width={24} source={myLocation} />
            </TouchableOpacity>
          </View>
        </>
      )}
    </View>
  );
};

export default Maps;
