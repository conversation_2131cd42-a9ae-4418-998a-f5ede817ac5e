import {Platform, StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  background: {
    flex: 1,
    resizeMode: 'cover',
    justifyContent: 'center',
  },
  mapContainer: {
    flex: 1,
  },

  rateContainer: {
    backgroundColor: colors.white,
    borderRadius: 30,
    width: 100,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    top: 50,
    left: '40%',
  },

  chipsContainer: {
    flexDirection: 'row',
    position: 'absolute',
    top: 50,
    alignSelf: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },

  earningsChip: {
    flexDirection: 'column',
    backgroundColor: colors.white,
    borderRadius: 30,
    paddingVertical: 10,
    paddingHorizontal: 15,
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 110,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
  },

  bonusChip: {
    flexDirection: 'column',
    backgroundColor: colors.white,
    borderRadius: 30,
    paddingVertical: 10,
    paddingHorizontal: 15,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 110,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
  },

  bonusrateContainer: {
    backgroundColor: colors.white,
    borderRadius: 30,
    width: 100,
    height: 60,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    top: 50,
    left: '25%',
    marginBottom: spacing.sm,
  },

  rateTxt: {
    color: colors.black,
    fontSize: sizes.h4,
    fontFamily: EBGaramondFont.regular,
  },

  bonusTxt: {
    color: '#E5574D',
    fontSize: sizes.h4,
    fontFamily: EBGaramondFont.regular,
  },

  navbarContainer: {
    position: 'absolute',
    bottom: spacing.md,
    alignSelf: 'center',
    width: 'auto',
    margin: spacing.xl,
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },

  contentBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: 30,
    borderWidth: 2,
    borderColor: colors.white,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    minWidth: 150,
    maxWidth: 250,
  },

  contentText: {
    fontFamily: GeistFont.regular,
    fontSize: sizes.h6,
    color: colors.lightGrey,
    marginRight: spacing.sm,
    flexShrink: 1, // Allow text to shrink
    maxWidth: '70%', // Limit text width
  },

  locationPositionContainer: {
    position: 'absolute',
    bottom: spacing.md * 8,
    right: 0,
    margin: spacing.xl,
  },

  locationContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.davyGrey,
    borderRadius: 1,
    height: spacing.xxl * 2,
    width: spacing.xxl * 2,
  },

  modalContainer: {
    position: 'absolute',
    bottom: '40%',
    left: 0,
    right: 0,
    padding: 20,
    borderTopLeftRadius: 5,
    borderTopRightRadius: 5,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.5,
  },

  modalContent: {
    alignItems: 'center',
  },

  modalText: {
    fontSize: 16,
    color: 'white',
    textAlign: 'center',
    marginBottom: 10,
  },

  offerCardContainer: {
    position: 'absolute',
    top: 140,
    left: 16,
    right: 16,
    borderRadius: spacing.sm,
    overflow: 'hidden',
  },
  offerCardGradient: {
    padding: 16,
    paddingBottom: 16,
  },
  offerCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  clockIcon: {
    marginRight: 8,
  },
  offerCardTitle: {
    color: colors.white,
    fontSize: sizes.h2,
    fontFamily: EBGaramondFont.regular,
    paddingRight: 8, // Additional padding for the title text specifically
  },
  offerCardDescription: {
    color: colors.white,
    fontSize: sizes.body,
    fontFamily: GeistFont.regular,
    marginBottom: spacing.md,
    paddingRight: 8, // Additional padding for description text
  },
  offerCardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 8, // Added top margin to create space between description and footer
  },
  offerCardTimer: {
    color: colors.white,
    fontSize: sizes.h2,
    fontFamily: EBGaramondFont.regular,
    flex: 1, // Take available space
    textAlign: 'left', // Ensure text is left-aligned
    marginRight: 12, // Add margin to create space
  },
  startTimeText: {
    color: colors.lightGrey,
    fontSize: sizes.h5,
    fontFamily: GeistFont.regular,
    flex: 1, // Take available space
  },
  offerCardButton: {
    backgroundColor: colors.white,
    borderRadius: 24,
    paddingVertical: 8, // Slightly reduced vertical padding
    paddingHorizontal: 16, // Slightly reduced horizontal padding
    marginLeft: 5, // Less margin since we added margin to the timer
    maxWidth: '75%', // Limit width to prevent extending beyond card
  },
  offerCardButtonText: {
    color: colors.black,
    fontSize: sizes.body,
    fontFamily: GeistFont.regular,
    textAlign: 'center', // Center text
    flexWrap: 'wrap', // Allow text to wrap
  },
  loaderImage: {
    width: 20,
    height: 20,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
