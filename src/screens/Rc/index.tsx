import React, {useCallback, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {useToast} from '../../components/Toast/Toast';
import FileUpload from '../../components/FileUpload/FileUpload';
import DocumentPicker, {
  DocumentPickerResponse,
  types,
} from 'react-native-document-picker';
import {Platform, View} from 'react-native';
import {launchCamera} from 'react-native-image-picker';
import {useLoader} from '../../hooks/useLoader';
import DriverService from '../../services/DriverService';
import {useDriver} from '../../hooks/useDriver';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {STATUS_CODE} from '../../constants/constants';
import {useFocusEffect} from '@react-navigation/native';
import Input from '../../components/Input/Input';

interface RcProps {
  navigation: any;
  route: any;
}

const Rc: React.FC<RcProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {showToast} = useToast();
  const {showLoader, hideLoader} = useLoader();
  const {fetchDriver, driver} = useDriver();
  const [rc, setRc] = useState<DocumentPickerResponse[]>([]);
  const driverParam = route?.params?.driver || '';
  const fromWelcome = route?.params?.fromWelcome || false;
  const [isFileRemoved, setIsFileRemoved] = useState(false);
  // const [rcExpiry, setRcExpiry] = useState('');
  const [expiryError, setExpiryError] = useState('');

  const allowedExtensions = ['jpg', 'jpeg', 'png', 'pdf'];
  const allowedMimeTypes = ['image/jpeg', 'image/png', 'application/pdf'];
  const disallowedMimeTypes = ['video/mp4'];
  const disallowedExtensions = ['mp4'];

  useFocusEffect(
    useCallback(() => {
      (async () => {
        await fetchDriver();

        if (driver?.registration_certificate) {
          const fileType = driver.registration_certificate
            .toLowerCase()
            .endsWith('.pdf')
            ? 'application/pdf'
            : 'image/jpeg';

          const updatedUri = `${
            driver.registration_certificate
          }?timestamp=${new Date().getTime()}`;

          setRc([
            {
              uri: updatedUri,
              name:
                fileType === 'application/pdf'
                  ? 'RC.pdf'
                  : 'Registration Certificate.jpg',
              type: fileType,
              fileCopyUri: updatedUri,
              size: 0,
            },
          ]);
        }
      })();
    }, [
      driver?.registration_certificate,
    ]),
  );

  const isValidFile = (file: DocumentPickerResponse) => {
    const fileExtension = file.name
      ? file.name.split('.').pop()?.toLowerCase()
      : '';

    if (
      disallowedExtensions.includes(fileExtension || '') ||
      disallowedMimeTypes.includes(file.type || '')
    ) {
      showToast(t('mp4_not_supported'), 'failure');
      return false;
    }

    return (
      allowedExtensions.includes(fileExtension || '') &&
      allowedMimeTypes.includes(file.type || '')
    );
  };

  const handleDocumentSelection = useCallback(async () => {
    try {
      const response = await DocumentPicker.pick({
        presentationStyle: 'fullScreen',
        type: [types.images, types.pdf],
      });

      const validFiles = response.filter(isValidFile);

      if (validFiles.length === 0) {
        showToast(t('file_selection_error_invalid'), 'failure');
        return;
      }
      setIsFileRemoved(false);
      setRc(validFiles);
    } catch (err) {
      if (!DocumentPicker.isCancel(err)) {
        showToast(t('file_selection_error'), 'failure');
      }
    }
  }, []);

  const handleCameraCapture = useCallback(async () => {
    try {
      const result = await launchCamera({
        mediaType: 'photo',
        quality: 0.8,
        maxWidth: 1280,
        maxHeight: 1280,
        saveToPhotos: false,
      });

      if (result.didCancel) {
        return;
      }

      if (result.errorCode) {
        showToast(result.errorMessage || t('file_selection_error'), 'failure');
        return;
      }

      if (result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        const capturedImage: DocumentPickerResponse = {
          uri: asset.uri || '',
          name: asset.fileName || 'camera_image.jpg',
          type: asset.type || 'image/jpeg',
          size: asset.fileSize || 0,
          fileCopyUri: asset.uri || null,
        };

        setIsFileRemoved(false);
        setRc([capturedImage]);
      }
    } catch (err) {
      showToast(t('file_selection_error'), 'failure');
      console.error('Camera capture error:', err);
    }
  }, []);

  const convertToFileResponse = (docs: DocumentPickerResponse[]) => {
    return docs.map(doc => ({
      uri: doc.uri,
      type: doc.type,
      name: doc.name,
      size: doc.size,
      fileCopyUri: doc.fileCopyUri,
    }));
  };

  const handleRemoveFile = useCallback((index: number) => {
    setIsFileRemoved(true);
    setRc(prevState => {
      const updatedFiles = [...prevState];
      updatedFiles.splice(index, 1);
      return updatedFiles;
    });
  }, []);

  const handleRcExpiry = (newDate: string) => {
    const today = new Date();
    const selectedDate = new Date(newDate);

    if (selectedDate < today) {
      setExpiryError(t('expiry_date_invalid'));
    } else {
      setExpiryError('');
    }
  };

  const handleContinue = async () => {
    try {
      showLoader();
      if (rc.length > 0 && !expiryError) {
        const formData = new FormData();
        const uri =
          Platform.OS === 'android'
            ? rc[0].uri
            : rc[0].uri.replace('file://', '');
        formData.append('rc', {uri, name: rc[0].name, type: rc[0].type});

        const response = await DriverService.update(formData);

        if (response) {
          if (driverParam) {
            await fetchDriver();
            AsyncStorage.removeItem('documents');
            navigation.reset({
              index: 0,
              routes: [{name: 'Welcome'}],
            });
            showToast(t('file_upload_success'), 'success');
          } else {
            showToast(t('file_upload_success'), 'success');
            navigation.navigate('DlNumber');
          }
        }
      } else {
        if (rc.length === 0) {
          showToast(t('no_file_selected'), 'failure');
        } else if (expiryError) {
          showToast(t('please_enter_valid_expiry_date'), 'failure');
        }
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err.response.data.response.code;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
      if (STATUS_CODE.bad_request === status) {
        code === 'update_driver_failed' &&
          showToast(t('file_size_exceeded'), 'failure');
      }
    } finally {
      hideLoader();
    }
  };

  const handleCancel = useCallback(() => {
    navigation.navigate('Document');
  }, [navigation]);

  const getBackButtonConfig = () => {
    if (fromWelcome) {
      return {
        backButtonAction: 'navigate' as const,
        backToScreen: 'Document',
        backParams: {fromWelcome: true},
      };
    }
    return {
      backButtonAction: 'goBack' as const,
    };
  };
  // const renderAdditionalFields = () => {
  //   return (
  //     <View style={{marginTop: 16}}>
  //       <Input
  //         type="date"
  //         onChange={handleRcExpiry}
  //         inputTitle={t('rc_expiry_date')}
  //         error={expiryError}
  //       />
  //     </View>
  //   );
  // };

  return (
    <FileUpload
      navigation={navigation}
      title={t('rc')}
      subtitle={t('rc_subtitle')}
      fileResponse={rc.length > 0 ? convertToFileResponse(rc) : []}
      onContinue={handleContinue}
      onCancel={handleCancel}
      onDocumentSelection={handleDocumentSelection}
      onCameraCapture={handleCameraCapture}
      onRemoveFile={handleRemoveFile}
      driver={driverParam}
      validationMessage={t('file_validation_message', {
        formats: 'PNG, JPG, PDF',
        size: '5MB',
      })}
      continueDisabled={rc.length === 0}
      {...getBackButtonConfig()}
    />
  );
};

export default Rc;
