import React, {useCallback, useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {ImageBackground, StyleSheet, View, Text} from 'react-native';
import {useLoader} from '../../hooks/useLoader';
import {colors, GeistFont, images, sizes} from '../../constants';
import {spacing} from '../../constants/theme';
import Button from '../../components/Button/Button';
import axiosInstance from '../../services/Api';
import {useGeofence} from '../../hooks/useGeofence';
import {useDriver} from '../../hooks/useDriver';
import Config from 'react-native-config';
import {useFocusEffect} from '@react-navigation/native';
import {useRideDetails} from '../../hooks/useRideDetailsContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import notifee from '@notifee/react-native';
import { stopRideSound } from '../../utils/Sound';

interface FallBackScreenProps {
  navigation?: any;
}

const FallBack: React.FC<FallBackScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {showLoader, hideLoader} = useLoader();
  const [retryCount, setRetryCount] = useState(0);
  const [errorMessage, setErrorMessage] = useState('');
  const {stopGeofencing} = useGeofence();
  const {setDriver} = useDriver();
  const {setShowRideModal, setTripDetails} = useRideDetails();

  const clearLocalStorage = async () => {
    setShowRideModal(false);
    setTripDetails(null);
    stopRideSound();
    await AsyncStorage.removeItem('showRideModal');
    await AsyncStorage.removeItem('tripId');
    await AsyncStorage.removeItem('rideStatus');
    await AsyncStorage.removeItem('userCanceled');
    await AsyncStorage.removeItem('timeout');
    await AsyncStorage.removeItem('sentTime');

    const noti = await AsyncStorage.getItem('notificationId');
    noti && (await notifee.cancelDisplayedNotification(noti));
  };

  useFocusEffect(
    useCallback(() => {
      stopGeofencing();
      setDriver(null);
      setShowRideModal(false);
      clearLocalStorage();
    }, []),
  );

  const handleRefresh = async () => {
    showLoader();
    setRetryCount(prev => prev + 1);
    setErrorMessage('');
    try {
      if (Config.API_URL) {
        await axiosInstance.get(Config.API_URL);
      } else {
        throw new Error('API_URL is not defined');
      }
      navigation.reset({
        index: 0,
        routes: [{name: 'SplashScreen'}],
      });
    } catch (error) {
      console.log(error);
      console.log('API call failed:', error);
    } finally {
      hideLoader();
    }
  };

  return (
    <ImageBackground source={images.map} style={styles.background}>
      <View style={styles.container}>
        <Text style={styles.title}>{t('fallback_title')}</Text>
        <Text style={styles.descriptionText}>{t('fallback_description')}</Text>
        {errorMessage ? (
          <Text style={styles.errorText}>{errorMessage}</Text>
        ) : null}
        <Button
          title={t('retry')}
          style={styles.retryBtn}
          onPress={handleRefresh}
        />
      </View>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  background: {
    flex: 1,
    resizeMode: 'cover',
    justifyContent: 'center',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    color: colors.white,
    textAlign: 'center',
    maxWidth: '80%',
    marginTop: spacing.md,
    fontFamily: GeistFont.regular,
    fontSize: sizes.h4,
  },
  descriptionText: {
    color: colors.grey,
    textAlign: 'center',
    maxWidth: '80%',
    marginTop: spacing.md,
    fontFamily: GeistFont.regular,
    fontSize: sizes.h6,
  },
  retryText: {
    color: colors.grey,
    textAlign: 'center',
    marginTop: spacing.md,
    fontFamily: GeistFont.regular,
    fontSize: sizes.h6,
  },
  errorText: {
    color: colors.red,
    textAlign: 'center',
    marginTop: spacing.md,
    fontFamily: GeistFont.regular,
    fontSize: sizes.h6,
  },
  retryBtn: {
    width: '80%',
    marginTop: spacing.xxl,
  },
});

export default FallBack;
