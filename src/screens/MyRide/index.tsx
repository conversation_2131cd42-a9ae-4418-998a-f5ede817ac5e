import React, {useEffect, useState} from 'react';
import {View, Text, Image, ImageBackground, ScrollView} from 'react-native';
import images from '../../constants/images';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import styles from './MyRideStyle';
import {useTranslation} from 'react-i18next';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import {colors, GeistFont} from '../../constants';
import {useLoader} from '../../hooks/useLoader';
import TripService from '../../services/TripService';
import {STATUS_CODE} from '../../constants/constants';

interface RideDetailsScreenProps {
  navigation: any;
}
interface Ride {
  id: number;
  created_at: string;
  updated_at: string;
  driverId: number;
  userId: number;
  status: string;
  source: {latitude: number; longitude: number};
  destination: {latitude: number; longitude: number};
  source_address: string;
  destination_address: string;
  distance: number;
  duration: number;
  fare: number;
  bonus?: string;
  tip: number | null;
  vehicleType: string;
  offerApplied?: boolean;
  offerDetails?: any;
}

const MyRide: React.FC<RideDetailsScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {showLoader, hideLoader, loading} = useLoader();
  const [activeTab, setActiveTab] = useState<'all' | 'upcoming'>('all');
  const [allRides, setAllRides] = useState<Ride[]>([]);
  const [upcomingRides, setUpcomingRides] = useState<Ride[]>([]);

  useEffect(() => {
    const fetchRides = async () => {
      try {
        showLoader();
        if (activeTab === 'all') {
          const completedResponse = await TripService.getDriverTrips(
            'completed',
          );
          if (completedResponse.data.data) {
            setAllRides(completedResponse.data.data.trips);
          }
        } else if (activeTab === 'upcoming') {
          const upcomingResponse = await TripService.getDriverTrips(
            'processing',
          );
          if (upcomingResponse.data.data) {
            setUpcomingRides(upcomingResponse.data.data.trips);
          }
        }
      } catch (err: any) {
        const status = err?.response?.status;
        if (
          [STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)
        ) {
          return;
        }
      } finally {
        hideLoader();
      }
    };

    fetchRides();
  }, [activeTab]);

  const renderRides = (rides: Ride[]) => {
    // console.log('Rendering rides:', rides);

    if (rides.length === 0 && !loading) {
      return (
        <FlexContainer justifyContent="center" alignItems="center">
          <Text
            style={{color: colors.lightGrey, fontFamily: GeistFont.regular}}>
            {t('no_rides_found')}
          </Text>
        </FlexContainer>
      );
    }

    return rides.map((ride, index) => (
      <View
        key={index}
        style={{
          marginBottom: index === rides.length - 1 ? 30 : 0,
        }}>
        <View style={styles.rideContainer}>
          <Image
            source={images.auto} // Default to auto image
            style={styles.vehicleIcon}
          />
          <View style={{flex: 1}}>
            <Text style={styles.dateText}>
              {ride.updated_at.substring(0, 10)}
            </Text>
            <Text numberOfLines={2} style={styles.locationLabel}>
              <Text style={styles.whiteText}>{t('pickup_location')}: </Text>
              <Text>{ride.source_address}</Text>
            </Text>
            <Text numberOfLines={2} style={styles.locationLabel}>
              <Text style={styles.whiteText}>{t('where_to')}: </Text>
              <Text>{ride.destination_address}</Text>
            </Text>
            <View style={styles.detailsContainer}>
              <Text style={[styles.detailsText, styles.whiteText]}>
                {t('km')}: {ride.distance}
              </Text>
              <View style={styles.fareContainer}>
                <View style={styles.fareRow}>
                  <Text style={[styles.detailsText, styles.whiteText]}>
                    {t('ride_fare')}: {t('rupee')}
                    {ride.fare}
                  </Text>
                </View>
                {ride.bonus && parseFloat(ride.bonus) > 0 && (
                  <View style={styles.bonusRow}>
                    <Text style={[styles.detailsText, styles.bonusText]}>
                      + {t('rupee')}
                      {ride.bonus} {t('bonus')}
                    </Text>
                  </View>
                )}
              </View>
            </View>
          </View>
        </View>
        {index !== rides.length - 1 && <FadingHorizontalLine />}
      </View>
    ));
  };

  return (
    <>
      <FlexContainer flex={1}>
        <ImageBackground source={images.bg2} style={styles.backgroundImage}>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{t('my_rides')}</Text>
          </View>

          <FadingHorizontalLine />
          {/* <View style={styles.subtitle}>
            <TouchableOpacity
              style={styles.tab}
              onPress={() => setActiveTab('all')}>
              <View style={activeTab === 'all' ? styles.activeTab : null}>
                <Text
                  style={[
                    styles.subtitleText,
                    { color: activeTab === 'all' ? colors.white : colors.grey },
                  ]}>
                  {t('all_rides')}
                </Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.tab}
              onPress={() => setActiveTab('upcoming')}>
              <View style={activeTab === 'upcoming' ? styles.activeTab : null}>
                <Text
                  style={[
                    styles.subtitleText,
                    {
                      color: activeTab === 'upcoming' ? colors.white : colors.grey,
                    },
                  ]}>
                  {t('upcoming_rides')}
                </Text>
              </View>
            </TouchableOpacity>
          </View> */}
          <ScrollView style={styles.safeArea}>
            {activeTab === 'all'
              ? renderRides(allRides)
              : renderRides(upcomingRides)}
          </ScrollView>
        </ImageBackground>
      </FlexContainer>
    </>
  );
};

export default MyRide;
