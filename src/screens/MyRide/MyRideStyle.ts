import {Platform, StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, theme, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: spacing.xxl * 4,
  },

  safeArea: {
    padding: spacing.md,
  },

  title: {
    position: 'absolute',
    top: 20,
    left: 20,
    marginVertical: spacing.xl,
    fontSize: sizes.h3,
    color: colors.white,
    fontFamily: EBGaramondFont.regular,
  },

  subtitle: {
    backgroundColor: colors.darkGrey,
    height: 60,
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    marginVertical: spacing.xl,
  },

  subtitleText: {
    color: colors.white,
    textAlign: 'center',
    paddingHorizontal: spacing.xl,
    fontFamily: GeistFont.regular,
  },

  rideContainer: {
    padding: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
  },

  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },

  kmTxt: {
    color: colors.grey,
    fontSize: sizes.h3 / 2,
    fontFamily: GeistFont.regular,
  },

  activeTab: {
    textAlign: 'center',
    borderBottomWidth: 2,
    borderBottomColor: colors.white,
  },

  locationLabel: {
    color: colors.grey,
    fontSize: sizes.body,
    fontFamily: GeistFont.variable,
    marginVertical: spacing.xs,
  },

  rideText: {
    color: colors.lightGrey,
    fontSize: sizes.h6,
    fontFamily: GeistFont.variable,
  },

  vehicleIcon: {
    width: 40,
    height: 40,
    marginRight: spacing.md,
  },

  dateText: {
    fontSize: sizes.h5,
    color: colors.white,
    marginBottom: spacing.xs,
    fontFamily: GeistFont.bold,
  },

  detailsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: spacing.sm,
  },

  detailsText: {
    fontSize: sizes.body,
    color: colors.grey,
    fontFamily: GeistFont.bold,
  },

  whiteText: {
    color: colors.white,
    marginRight: spacing.sm,
    fontFamily: GeistFont.bold,
  },

  bonusText: {
    color: colors.white,
    fontFamily: GeistFont.bold,
    fontSize: sizes.body,
  },

  fareContainer: {
    flexDirection: 'column',
    alignItems: 'flex-end',
  },

  fareRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  bonusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: -2,
  },
});
