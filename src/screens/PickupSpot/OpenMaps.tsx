import { Alert, Linking, Platform } from 'react-native';

const OpenMaps = (destination: { latitude: number; longitude: number; }) => {
  const appleMapsUrl = `maps:0,0?q=${destination.latitude},${destination.longitude}`;
  const googleMapsUrl = `google.navigation:q=${destination.latitude},${destination.longitude}&mode=d`;
  const webUrl = `https://www.google.com/maps/dir/?api=1&destination=${destination.latitude},${destination.longitude}`;

  const openUrl = async (url: string) => {
    try {
      await Linking.openURL(url);
    } catch (error) {
      console.error('Error opening maps', error);
      await Linking.openURL(webUrl);
    }
  };

  if (Platform.OS === 'ios') {
    Alert.alert(
      'Choose Maps',
      'Which map application would you like to use?',
      [
        { text: 'Apple Maps', onPress: () => openUrl(appleMapsUrl) },
        { text: 'Google Maps', onPress: () => openUrl(googleMapsUrl) },
        { text: 'Cancel', style: 'cancel' },
      ],
      { cancelable: true }
    );
  } else if (Platform.OS === 'android') {
    openUrl(googleMapsUrl);
  } else {
    openUrl(webUrl);
  }
};


export default OpenMaps;