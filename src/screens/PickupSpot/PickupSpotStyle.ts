import {Platform, StyleSheet, Dimensions} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

const {width: SCREEN_WIDTH} = Dimensions.get('window');

export default StyleSheet.create({
  navigateContainer: {
    position: 'absolute',
    top: 40,
    marginVertical: spacing.xl,
    backgroundColor: colors.davyGrey,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: spacing.xl,
    width: '100%',
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
    flexShrink: 0,
    minWidth: 120, 
    justifyContent: 'flex-end',
    marginLeft: spacing.md, 
  },
  pickupContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '85%',
  },
  location: {
    color: colors.white,
    fontSize: sizes.h6,
    fontFamily: GeistFont.variable,
    marginTop: spacing.sm,
  },
  pickupIcon: {
    marginTop: spacing.sm,
  },
  backgroundImage: {
    resizeMode: 'cover',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  safeArea: {
    flex: 1,
    margin: spacing.xl,
  },
  titleContainer: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  titleWrapper: {
    flexShrink: 1, 
    maxWidth: SCREEN_WIDTH < 350 ? '55%' : '65%', 
  },
  title: {
    fontSize: SCREEN_WIDTH < 350 ? sizes.h3 : sizes.h2, 
    color: colors.white,
    fontFamily: EBGaramondFont.regular,
    marginBottom: spacing.xl,
    flexShrink: 1,
  },
  btnContainer: {
    justifyContent: 'space-between',
    marginTop: spacing.xl,
    flexDirection: 'row',
  },
  confirmBtn: {
    width: '75%',
  },
  declineBtn: {
    flex: 1,
    height: 50,
    marginRight: spacing.md,
    backgroundColor: colors.darkGrey,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.lightGrey,
  },
  locationPositionContainer: {
    position: 'absolute',
    right: spacing.xxs,
    bottom: '130%',
  },
  locationNavigationContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.davyGrey,
    borderRadius: 5,
    height: spacing.xxl * 2,
    width: spacing.xxl * 2,
  },
  supportIconContainer: {
    position: 'absolute',
    top: 175, 
    right: 20,
    zIndex: 999,
  },
  supportIcon: {
    width: 50,
    height: 50,
    borderRadius: 5,
    backgroundColor: colors.davyGrey,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
