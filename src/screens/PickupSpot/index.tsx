import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  Animated as RNAnimated,
  Easing,
  View,
  Text,
  ImageBackground,
  SafeAreaView,
  TouchableOpacity,
  Linking,
  Alert,
  Image,
} from 'react-native';
import {colors, images} from '../../constants';
import styles from './PickupSpotStyle';
import myLocation from '../../icons/my_location.svg';
import Button from '../../components/Button/Button';
import {useTranslation} from 'react-i18next';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import {spacing} from '../../constants/theme';
import diamondActive from '../../icons/diamondActive.svg';
import MapComponent from '../../components/Map/MapComponent';
import MapView, {<PERSON>t<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>} from 'react-native-maps';
import dropIcon from '../../icons/dropIcon.svg';
import closeIcon from '../../icons/close.svg';
import phoneIcon from '../../icons/call.svg';
import navigateIcon from '../../icons/navigation.svg';
import chatIcon from '../../icons/chat.svg';
import {getDirections, zoomToRoute} from '../../utils/MapUtils';
import {useToast} from '../../components/Toast/Toast';
import OpenMaps from './OpenMaps';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useRideDetails} from '../../hooks/useRideDetailsContext';
import {useLoader} from '../../hooks/useLoader';
import ChatModal from '../../components/ChatModal';
import {useDriver} from '../../hooks/useDriver';
import {formatTime} from '../../utils/TimeUtils';
import {userLocationContext} from '../../hooks/userLocationContext';
import {STATUS_CODE} from '../../constants/constants';
import {useFocusEffect} from '@react-navigation/native';
import Geolocation from 'react-native-geolocation-service';
import RideService from '../../services/RideService';
import MessageService from '../../services/MessageService';
import notifee from '@notifee/react-native';
import {requestCallPhonePermission} from '../../constants/permissions';
import support from '../../icons/support.svg';
import SupportMenu from '../../components/SupportMenu/SupportMenu';
import {useKeepAwake} from '../../hooks/useKeepAwake';
import ConfirmationModal from '../../components/ConfirmationModal';
import * as Sentry from '@sentry/react-native';

interface PickupScreenProps {
  navigation: any;
}

const PickupSpot: React.FC<PickupScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const mapViewRef = useRef<MapView>(null);
  const supportIconRef = useRef<TouchableOpacity>(null);
  const [supportModalVisible, setSupportModalVisible] = useState(false);
  const [iconPosition, setIconPosition] = useState({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  });
  const {driver} = useDriver();

  useKeepAwake();
  const {
    routeCoordinates,
    tripDetails,
    setRouteCoordinates,
    messages,
    setMessages,
  } = useRideDetails();
  const [address, setAddress] = useState<string>('');
  const [region, setRegion] = useState<LatLng | null>(null);
  const {userLocation, setUserLocation} = userLocationContext();
  const {showToast} = useToast();
  const {showLoader, hideLoader} = useLoader();
  const [isChatModalVisible, setChatModalVisible] = useState(false);
  const [user, setUser] = useState<any>();
  const currentTime = new Date().toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
  });
  const [loadingButton, setLoadingButton] = useState<
    'pickup' | 'cancel' | null
  >(null);
  const [markerReady, setMarkerReady] = useState(false);
  const [hasUnreadMessages, setHasUnreadMessages] = useState(false);
  const badgeOpacity = useRef(new RNAnimated.Value(1)).current;
  const [iconLoaded, setIconLoaded] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [markerRotation, setMarkerRotation] = useState<number>(0);
  const [cancelModalVisible, setCancelModalVisible] = useState<boolean>(false);

  const getVehicleImage = useCallback(() => {
    if (!driver?.vehicles || !driver.vehicles[0]) {
      return images.autoTop;
    }

    const fuelType = driver.vehicles[0].fuel_type;
    switch (fuelType) {
      case 'PETROL':
        return images.autoDiesel;
      case 'DIESEL':
        return images.autoDiesel;
      case 'CNG':
        return images.autoCng;
      case 'ELECTRIC':
        return images.autoElectric;
      default:
        return images.autoTop;
    }
  }, [driver?.vehicles]);

  const toggleSupportMenu = () => {
    if (supportIconRef.current) {
      supportIconRef.current.measure(
        (
          _x: number,
          _y: number,
          width: number,
          height: number,
          pageX: number,
          pageY: number,
        ) => {
          setIconPosition({x: pageX, y: pageY, width, height});
          setSupportModalVisible(!supportModalVisible);
        },
      );
    } else {
      setSupportModalVisible(!supportModalVisible);
    }
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setMarkerReady(true);
    }, 1500);

    return () => clearTimeout(timer);
  }, [driver]);

  useEffect(() => {
    const getCurrentLocation = async () => {
      try {
        Geolocation.getCurrentPosition(
          (position: {coords: {latitude: any; longitude: any}}) => {
            const {latitude, longitude} = position.coords;
            setUserLocation({
              latitude,
              longitude,
            });
          },
          (error: any) => {
            console.log('Error getting location:', error);
          },
          {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 10000,
          },
        );
      } catch (error) {
        console.error('Error requesting location:', error);
      }
    };

    getCurrentLocation();

    const locationInterval = setInterval(() => {
      getCurrentLocation();
    }, 20000);

    return () => clearInterval(locationInterval);
  }, []);

  const handleGetLocation = () => {
    if (routeCoordinates.length > 0) {
      const interval = setInterval(() => {
        if (mapViewRef.current) {
          zoomToRoute(mapViewRef, routeCoordinates);
          clearInterval(interval);
        }
      }, 500);

      setTimeout(() => clearInterval(interval), 5000);

      return () => clearInterval(interval);
    }
  };

  const fetchDistance = async () => {
    showLoader();
    try {
      if (tripDetails && userLocation) {
        const userPickupLocation = `${tripDetails.source.latitude},${tripDetails.source.longitude}`;
        const driverPickupLocation = `${userLocation.latitude},${userLocation.longitude}`;

        const distanceForPickup = await getDirections(
          userPickupLocation,
          driverPickupLocation,
        );
        setRouteCoordinates(distanceForPickup.points || []);
      }
    } catch (err: any) {
      console.log(err);
    } finally {
      hideLoader();
    }
  };

  const fetchUser = async () => {
    try {
      const response = await RideService.fetchUserDetails(tripDetails?.id);
      if (response.status === STATUS_CODE.ok) {
        setUser(response.data.data.user);
      }
    } catch (err: any) {
      console.log(err.response.data);

      const status = err?.response?.status;
      const message = err?.response?.data?.message;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
    }
  };

  useFocusEffect(
    useCallback(() => {
      (async () => {
        try {
          await fetchDistance();
          await fetchUser();
        } catch (error) {
          console.error('Error fetching trip details or distance:', error);
        }
      })();
    }, [userLocation]),
  );

  // useEffect(() => {
  //   let flickerAnimation: RNAnimated.CompositeAnimation;

  //   if (hasUnreadMessages && !isChatModalVisible) {
  //     flickerAnimation = RNAnimated.loop(
  //       RNAnimated.sequence([
  //         RNAnimated.timing(badgeOpacity, {
  //           toValue: 0.3,
  //           duration: 500,
  //           easing: Easing.ease,
  //           useNativeDriver: true,
  //         }),
  //         RNAnimated.timing(badgeOpacity, {
  //           toValue: 1,
  //           duration: 500,
  //           easing: Easing.ease,
  //           useNativeDriver: true,
  //         }),
  //       ]),
  //     );

  //     flickerAnimation.start();
  //   }

  //   return () => {
  //     if (flickerAnimation) {
  //       flickerAnimation.stop();
  //     }
  //   };
  // }, [hasUnreadMessages, isChatModalVisible, badgeOpacity]);

  // useEffect(() => {
  //   if (tripDetails?.id) {
  //     const fetchMessages = async () => {
  //       try {
  //         const response = await MessageService.getMessages(
  //           Number(tripDetails?.id),
  //         );

  //         if (response.status === 200) {
  //           const newMessages = response.data.data.map((msg: any) => {
  //             const time = msg.timestamp
  //               ? formatTime(new Date(msg.timestamp))
  //               : 'Unknown Time';

  //             return {
  //               id: msg.id,
  //               text: msg.content,
  //               sender: msg.driverId !== null,
  //               time: time,
  //             };
  //           });

  //           if (
  //             !isChatModalVisible &&
  //             messages.length > 0 &&
  //             newMessages.length > messages.length
  //           ) {
  //             const hasNewUserMessage = newMessages.some(
  //               (newMsg: {sender: boolean; id: number}) =>
  //                 !newMsg.sender &&
  //                 !messages.some(oldMsg => oldMsg.id === newMsg.id),
  //             );
  //             if (hasNewUserMessage) {
  //               setHasUnreadMessages(true);
  //             }
  //           }

  //           setMessages(newMessages);
  //         }
  //       } catch (error) {
  //         console.log('Error fetching messages:', error);
  //       }
  //     };

  //     fetchMessages();

  //     const interval = setInterval(fetchMessages, 5000);

  //     return () => clearInterval(interval);
  //   }
  // }, [tripDetails, isChatModalVisible]);

  // const toggleChatModal = async () => {
  //   await notifee.cancelAllNotifications();

  //   if (!isChatModalVisible) {
  //     setHasUnreadMessages(false);
  //     try {
  //       const response = await MessageService.getMessages(
  //         Number(tripDetails?.id),
  //       );

  //       if (response.status === 200) {
  //         setMessages(
  //           response.data.data.map((msg: any) => {
  //             const time = msg.timestamp
  //               ? formatTime(new Date(msg.timestamp))
  //               : 'Unknown Time';

  //             return {
  //               id: msg.id,
  //               text: msg.content,
  //               sender: msg.driverId !== null,
  //               time: time,
  //             };
  //           }),
  //         );
  //       }
  //     } catch (error) {
  //       console.error('Error fetching messages:', error);
  //     }
  //   }

  //   setChatModalVisible(!isChatModalVisible);
  // };

  const sendMessage = async (message: string) => {
    if (!message.trim() || !tripDetails?.id) return;

    try {
      const response = await MessageService.sendMessages(
        message,
        Number(tripDetails.id),
      );
      if (response.status === STATUS_CODE.created) {
        setMessages(prevMessages => [
          ...prevMessages,
          {
            id: Date.now(),
            text: message,
            sender: true,
            time: currentTime,
            content: message,
          },
        ]);
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  useFocusEffect(
    useCallback(() => {
      if (routeCoordinates.length > 0) {
        const interval = setInterval(() => {
          if (mapViewRef.current) {
            zoomToRoute(mapViewRef, routeCoordinates);
            clearInterval(interval);
          }
        }, 500);

        return () => clearInterval(interval);
      }
    }, [routeCoordinates, mapViewRef.current]),
  );

  const handleStart = () => {
    if (routeCoordinates.length > 0) {
      const destination = routeCoordinates[0];
      OpenMaps(destination);
    }
  };

  const handleCancel = () => {
    setCancelModalVisible(true);
  };

  const confirmCancel = async () => {
    setCancelModalVisible(false);
    setLoadingButton('cancel');
    try {
      if (tripDetails) {
        console.log('RideCancel-PickupSpot-tripid:', tripDetails.id);
        const response = await RideService.cancelRide(tripDetails?.id);
        if (response) {
          console.log('RideCanceled-PickupSpot:', response.data.data);
        }
      }
    } catch (err: any) {
      console.error('Error canceling ride:Pickuspot', err.response.data);
      const status = err?.response?.status;
      const code = err.response?.data?.response?.code;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      } else if (STATUS_CODE.bad_request === status) {
        if (code === 'trip_already_cancelled') {
          showToast(t('trip_already_cancelled'), 'failure');
        } else if (code === 'no_active_trip') {
          await clearLocalStorage();
          navigation.reset({
            index: 0,
            routes: [{name: 'BottomTab'}],
          });
          showToast(t('no_active_trip'), 'failure');
          return;
        }
      }
      Sentry.captureException(err, {
        tags: {
          tripId: tripDetails?.id,
          driverId: driver?.id,
          action: 'ride_cancellation',
          status_code: status,
          error_code: code,
        },
      });
    } finally {
      setLoadingButton(null);
    }
  };

  const makePhoneCall = async (number: string) => {
    if (!number) {
      Alert.alert('No phone number available');
      return;
    }

    const hasPermission = await requestCallPhonePermission();

    if (hasPermission) {
      Linking.openURL(`tel:${number}`).catch(() =>
        Alert.alert('Error', 'Unable to make a call'),
      );
    } else {
      Alert.alert(
        'Permission Required',
        'Call permission is required to make a phone call. Please enable it in settings.',
        [
          {text: 'Cancel', style: 'cancel'},
          {text: 'Open Settings', onPress: () => Linking.openSettings()},
        ],
      );
    }
  };

  const handlePickup = async () => {
    setLoadingButton('pickup');
    try {
      await AsyncStorage.setItem('rideStatus', 'ONRIDEPIN');
      navigation.reset({
        index: 0,
        routes: [{name: 'RidePin'}],
      });
    } catch (error) {
      console.error('Error handling pickup:', error);
    } finally {
      setLoadingButton(null);
    }
  };

  const clearLocalStorage = async () => {
    await AsyncStorage.removeItem('showRideModal');
    await AsyncStorage.removeItem('tripId');
    await AsyncStorage.removeItem('rideStatus');
    await AsyncStorage.removeItem('userCanceled');
    await AsyncStorage.removeItem('timeout');
    await AsyncStorage.removeItem('sentTime');
    await AsyncStorage.removeItem('showRideStatusCheck');
    await AsyncStorage.removeItem('showRideAborted');
    await AsyncStorage.removeItem('sentTime');
    await notifee.cancelAllNotifications();

    const noti = await AsyncStorage.getItem('notificationId');
    noti && (await notifee.cancelDisplayedNotification(noti));
  };

  useEffect(() => {
    if (userLocation && routeCoordinates.length > 1) {
      let minDistance = Infinity;
      let closestIndex = 0;

      routeCoordinates.forEach((coord, index) => {
        const distance = Math.sqrt(
          Math.pow(coord.latitude - userLocation.latitude, 2) +
            Math.pow(coord.longitude - userLocation.longitude, 2),
        );

        if (distance < minDistance) {
          minDistance = distance;
          closestIndex = index;
        }
      });

      const nextPointIndex = Math.min(
        closestIndex + 1,
        routeCoordinates.length - 1,
      );
      if (nextPointIndex !== closestIndex) {
        const nextPoint = routeCoordinates[nextPointIndex];
        const currentPoint = userLocation;

        const y =
          Math.sin(
            nextPoint.longitude * (Math.PI / 180) -
              currentPoint.longitude * (Math.PI / 180),
          ) * Math.cos(nextPoint.latitude * (Math.PI / 180));

        const x =
          Math.cos(currentPoint.latitude * (Math.PI / 180)) *
            Math.sin(nextPoint.latitude * (Math.PI / 180)) -
          Math.sin(currentPoint.latitude * (Math.PI / 180)) *
            Math.cos(nextPoint.latitude * (Math.PI / 180)) *
            Math.cos(
              nextPoint.longitude * (Math.PI / 180) -
                currentPoint.longitude * (Math.PI / 180),
            );

        let bearing = Math.atan2(y, x) * (180 / Math.PI);

        bearing = (bearing + 360) % 360;

        setMarkerRotation(bearing);
        setRetryCount(prev => prev + 1);
      }
    }
  }, [userLocation, routeCoordinates]);

  useEffect(() => {
    if (routeCoordinates.length > 1) {
      setRetryCount(prev => prev + 1);
    }
  }, [routeCoordinates]);

  useKeepAwake();

  return (
    <View style={{backgroundColor: colors.darkGrey, flex: 1}}>
      <MapComponent
        ref={mapViewRef}
        marker={false}
        setAddress={setAddress}
        region={region}
        showLocation={false}
        setRegion={() => {}}>
        <View>
          {routeCoordinates.length > 0 && (
            <Marker
              key={`auto-${retryCount}-${routeCoordinates.length}`}
              anchor={{x: 0.5, y: 0.5}}
              tracksViewChanges={!markerReady || !iconLoaded}
              coordinate={routeCoordinates[routeCoordinates.length - 1]}
              title="Current Location">
              <View
                onLayout={() => {
                  setTimeout(() => setIconLoaded(true), 300);
                }}>
                <Image
                  source={getVehicleImage()}
                  style={{
                    width: 60,
                    height: 60,
                    transform: [{rotate: `${markerRotation - 90}deg`}],
                  }}
                  resizeMode="contain"
                />
              </View>
            </Marker>
          )}
          {routeCoordinates.length > 0 && (
            <Marker
              anchor={{x: 0.5, y: 0.5}}
              tracksViewChanges={false}
              coordinate={routeCoordinates[0]}
              title="Drop Location">
              <IconSvgView source={dropIcon} />
            </Marker>
          )}
          {routeCoordinates.length > 1 && (
            <Polyline
              coordinates={routeCoordinates}
              strokeColor="white"
              strokeWidth={4}
              key={`polyline-${routeCoordinates.length}`}
            />
          )}
        </View>
      </MapComponent>

      <View style={styles.supportIconContainer}>
        <TouchableOpacity
          ref={supportIconRef}
          style={styles.supportIcon}
          onPress={toggleSupportMenu}
          accessibilityLabel="Get support">
          <IconSvgView size={30} source={support} />
        </TouchableOpacity>
      </View>

      <View style={styles.navigateContainer}>
        <View style={styles.pickupContainer}>
          <View>
            <IconSvgView
              width={16}
              svgStyle={styles.pickupIcon}
              source={diamondActive}
            />
          </View>
          <View style={{marginHorizontal: spacing.md}}>
            <Text numberOfLines={2} style={styles.location}>
              {tripDetails?.source_address}
            </Text>
          </View>
        </View>
        <TouchableOpacity
          style={{
            width: 50,
            height: 50,
            borderRadius: 50,
            borderWidth: 2,
            borderColor: colors.white,
            justifyContent: 'center',
            alignItems: 'center',
          }}
          onPress={handleStart}>
          <IconSvgView size={30} source={navigateIcon} />
        </TouchableOpacity>
      </View>
      <ImageBackground source={images.bg2} style={styles.backgroundImage}>
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.titleContainer}>
            <View style={styles.titleWrapper}>
              <Text
                style={styles.title}
                numberOfLines={2}
                adjustsFontSizeToFit={true}
                minimumFontScale={0.8}>
                {t('go_pickup')}
              </Text>
            </View>
            <View style={styles.iconContainer}>
              {/* <TouchableOpacity onPress={toggleChatModal}>
                <IconSvgView size={40} source={chatIcon} />
                {hasUnreadMessages && !isChatModalVisible && (
                  <RNAnimated.View
                    style={{
                      position: 'absolute',
                      top: -1,
                      right: -1,
                      backgroundColor: colors.lightGreen,
                      width: spacing.md,
                      height: spacing.md,
                      borderRadius: spacing.md,
                      borderWidth: 1,
                      borderColor: colors.white,
                      opacity: badgeOpacity,
                    }}
                  />
                )}
              </TouchableOpacity> */}
              <TouchableOpacity
                onPress={() => makePhoneCall(user?.phone ?? '')}>
                <IconSvgView size={40} source={phoneIcon} />
              </TouchableOpacity>
            </View>
          </View>
          <FadingHorizontalLine />

          <View style={styles.btnContainer}>
            <TouchableOpacity
              style={styles.declineBtn}
              onPress={handleCancel}
              disabled={loadingButton === 'pickup'}>
              <IconSvgView source={closeIcon} width={20} height={20} />
            </TouchableOpacity>
            <Button
              style={styles.confirmBtn}
              title={t('pickup')}
              onPress={handlePickup}
              disabled={loadingButton === 'cancel'}
              loading={loadingButton === 'pickup'}
            />
          </View>
          <View style={styles.locationPositionContainer}>
            <TouchableOpacity
              style={styles.locationNavigationContainer}
              onPress={() => handleGetLocation()}>
              <IconSvgView width={24} source={myLocation} />
            </TouchableOpacity>
          </View>
        </SafeAreaView>

        {isChatModalVisible && (
          <ChatModal
            visible={isChatModalVisible}
            onClose={() => setChatModalVisible(false)}
            driverId={driver?.id.toString()!}
            messages={messages}
            tripId={tripDetails?.id}
            sendMessage={sendMessage}
          />
        )}

        <ConfirmationModal
          visible={cancelModalVisible}
          title={t('cancel_ride')}
          message={t('cancel_ride_confirmation_message')}
          onConfirm={confirmCancel}
          onCancel={() => setCancelModalVisible(false)}
        />
      </ImageBackground>
      {supportModalVisible && (
        <SupportMenu
          visible={supportModalVisible}
          onClose={() => setSupportModalVisible(false)}
          position={iconPosition}
        />
      )}
    </View>
  );
};

export default PickupSpot;
