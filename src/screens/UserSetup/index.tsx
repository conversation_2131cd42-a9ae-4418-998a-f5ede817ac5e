import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {
  ImageBackground,
  Text,
  ScrollView,
  View,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Keyboard,
  Dimensions,
  TextInput,
} from 'react-native';
import {images} from '../../constants';
import styles from './UserSetupStyle';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import Input from '../../components/Input/Input';
import Button from '../../components/Button/Button';
import Dropdown from '../../components/DropDown/DropDown';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import diamond from '../../icons/diamond.svg';
import diamondInactive from '../../icons/diamondBlack.svg';
import {useTranslation} from 'react-i18next';
import {SafeAreaView} from 'react-native-safe-area-context';
import MultiSelect from '../../components/MultiSelect/MultiSelect';
import DriverService from '../../services/DriverService';
import {useLoader} from '../../hooks/useLoader';
import {useToast} from '../../components/Toast/Toast';
import {useDriver} from '../../hooks/useDriver';
import {STATUS_CODE} from '../../constants/constants';
import {useFocusEffect} from '@react-navigation/native';
import support from '../../icons/support.svg';
import SupportMenu from '../../components/SupportMenu/SupportMenu';
import AsyncStorage from '@react-native-async-storage/async-storage';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';

interface UserSetupProps {
  navigation: any;
}

const UserSetup: React.FC<UserSetupProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {showLoader, hideLoader} = useLoader();
  const {driver, fetchDriver} = useDriver();
  const {showToast} = useToast();
  const [name, setName] = useState('');
  const [touched, setTouched] = useState(false);
  const [location, setLocation] = useState('');
  const [language, setLanguage] = useState('');
  const [gender, setGender] = useState<{label: string; value: string}>();
  const supportIconRef = useRef(null);
  const scrollViewRef = useRef(null);
  const referralInputRef = useRef(null);
  const [supportModalVisible, setSupportModalVisible] = useState(false);
  const [iconPosition, setIconPosition] = useState(null);
  const [dob, setDob] = useState('');
  const [error, setError] = useState<string>('');
  const [receiveWhatsAppUpdates, setReceiveWhatsAppUpdates] = useState(false);

  const [referralNumber, setReferralNumber] = useState('+91 ');
  const [referralError, setReferralError] = useState<string>('');
  const [referralDriverName, setReferralDriverName] = useState<string>('');
  const [isValidatingReferral, setIsValidatingReferral] =
    useState<boolean>(false);
  const [referralVerified, setReferralVerified] = useState(false);

  useEffect(() => {
    const loadWhatsAppPreference = async () => {
      try {
        const savedPreference = await AsyncStorage.getItem(
          'receiveWhatsAppUpdates',
        );
        if (savedPreference !== null) {
          setReceiveWhatsAppUpdates(JSON.parse(savedPreference));
        }
      } catch (error) {
        console.error('Error loading WhatsApp preference:', error);
      }
    };

    loadWhatsAppPreference();
  }, []);

  const toggleSupportMenu = () => {
    if (supportIconRef.current) {
      supportIconRef.current.measure(
        (
          _x: number,
          _y: number,
          width: number,
          height: number,
          pageX: number,
          pageY: number,
        ) => {
          setIconPosition({x: pageX, y: pageY, width, height});
          setSupportModalVisible(!supportModalVisible);
        },
      );
    } else {
      setSupportModalVisible(!supportModalVisible);
    }
  };

  useFocusEffect(
    useCallback(() => {
      const fetchData = async () => {
        const driver = await fetchDriver();

        if (driver) {
          setName(driver?.name ?? '');
          setGender(
            driver?.gender
              ? {label: driver.gender, value: driver.gender}
              : undefined,
          );
          setLocation(driver?.location ?? '');
          setLanguage(driver?.preferred_language ?? '');
          if (driver?.dob) {
            setDob(driver.dob);
          }
        }
      };

      fetchData();
    }, []),
  );

  useEffect(() => {
    if (referralNumber === '+91 ') {
      setReferralError('');
      setReferralDriverName('');
    }
  }, [referralNumber, t]);

  const verifyReferral = async () => {
    if (!/^\+91\s[0-9]{10}$/.test(referralNumber)) {
      setReferralError(t('invalid_phone_format'));
      return;
    }

    try {
      setIsValidatingReferral(true);
      setReferralError(t('validating_referral'));

      const phoneToValidate = referralNumber.replace(/\s/g, '');
      const response = await DriverService.referral(phoneToValidate);

      if (
        response.data &&
        response.data.data &&
        response.data.data.valid &&
        response.data.data.driverName &&
        response.data.data.driverName.trim() !== '' &&
        response.data.data.driverName !== 'Driver'
      ) {
        setReferralError('');
        setReferralVerified(true);
        setReferralDriverName(response.data.data.driverName);
        Keyboard.dismiss();
      } else {
        setReferralVerified(false);
        setReferralDriverName('');
        setReferralError(t('no_such_driver_exists'));
      }
    } catch (err) {
      console.log('Referral verification error:', err);
      setReferralVerified(false);
      setReferralDriverName('');
      setReferralError(t('no_such_driver_exists'));
    } finally {
      setIsValidatingReferral(false);
    }
  };

  const handleReferralChange = (input: string) => {
    if (!input.startsWith('+91 ')) {
      setReferralNumber('+91 ');
      return;
    }
    const numberPart = input.substring(4);
    const digitsOnly = numberPart.replace(/[^0-9]/g, '');
    if (digitsOnly.length <= 10) {
      setReferralNumber('+91 ' + digitsOnly);
      setReferralError('');
    }
  };

  const handleReferralInputFocus = () => {
    if (scrollViewRef.current && referralInputRef.current) {
      setTimeout(() => {
        referralInputRef.current.measureLayout(
          scrollViewRef.current,
          (x, y) => {
            scrollViewRef.current.scrollTo({
              y: y - 50,
              animated: true,
            });
          },
          () => console.log('Failed to measure layout'),
        );
      }, 100);

      // Scroll again after a delay to ensure keyboard is fully shown
      setTimeout(() => {
        referralInputRef.current.measureLayout(
          scrollViewRef.current,
          (x, y) => {
            scrollViewRef.current.scrollTo({
              y: y - 50,
              animated: true,
            });
          },
          () => console.log('Failed to measure layout'),
        );
      }, 500);
    }
  };

  const genderData = [
    {label: t('men'), value: '1'},
    {label: t('women'), value: '2'},
    {label: t('not_specified'), value: '3'},
  ];

  const placesData = [
    {
      label: t('thiruvananthapuram'),
      value: 'thiruvananthapuram',
    },
  ];

  const languageData = [
    {label: t('malayalam'), value: 'malayalam'},
    {label: t('english'), value: 'english'},
    {label: t('hindi'), value: 'hindi'},
    {label: t('tamil'), value: 'tamil'},
  ];
  const handleDateChange = (newDate: string) => {
    setDob(newDate);

    if (!newDate || newDate.trim() === '') {
      setError('');
      return;
    }

    const isValidAge = (dateString: string) => {
      try {
        let birthDate = new Date(dateString);

        if (isNaN(birthDate.getTime())) {
          const parts = dateString.split('/');
          if (parts.length === 3) {
            birthDate = new Date(
              parseInt(parts[2]), // Year
              parseInt(parts[0]) - 1, // Month (0-based in JS)
              parseInt(parts[1]), // Day
            );
          }
        }

        if (isNaN(birthDate.getTime())) {
          return false;
        }

        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();

        if (
          today.getMonth() < birthDate.getMonth() ||
          (today.getMonth() === birthDate.getMonth() &&
            today.getDate() < birthDate.getDate())
        ) {
          age--;
        }

        return age >= 18;
      } catch (e) {
        console.error('Date parsing error:', e);
        return false;
      }
    };

    if (!isValidAge(newDate)) {
      setError(t('validate_adult'));
    } else {
      setError('');
    }
  };
  const register = async () => {
    try {
      if (name && gender) {
        if (referralValidationNeeded) {
          setReferralError(t('please_verify_referral'));
          return;
        }
        console.log('register', name, gender, location, dob, language);

        showLoader();
        const referralToSend =
          referralVerified &&
          referralDriverName &&
          referralDriverName !== 'Driver' &&
          referralDriverName.trim() !== ''
            ? referralNumber.replace(/\s/g, '')
            : '';

        const response = await DriverService.update({
          name,
          gender: gender.label,
          preferredLanguage: language,
          location,
          dob,
          referrerPhone: referralToSend,
        });
        console.log('response', response);

        if (response) {
          hideLoader();
          navigation.navigate('Vehicle');
          showToast(t('driver_update_sucessfull'), 'success');
        }
      }
    } catch (err: any) {
      console.log('Error in registration:', err);
      
      const status = err?.response?.status;
      const errorData = err?.response?.data?.response;
      const errorMessage = errorData?.message;
      const errorCode = errorData?.code;
      
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
      
      if (status === STATUS_CODE.bad_request) {
        if (errorCode === 'update_driver_failed') {
          showToast(errorMessage || t('update_failed'), 'failure');
        } else if (errorMessage) {
          showToast(errorMessage, 'failure');
        } else {
          showToast(t('registration_error'), 'failure');
        }
      }
    } finally {
      hideLoader();
    }
  };

  const nameError = useMemo(() => {
    return touched && name.length < 2 ? t('name_length_error') : '';
  }, [name, touched]);

  const referralValidationNeeded = useMemo(() => {
    return referralNumber !== '+91 ' && !referralVerified;
  }, [referralNumber, referralVerified]);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      event => {
        const currentlyFocusedInput =
          TextInput.State?.currentlyFocusedInput?.();
        const isReferralInputFocused =
          currentlyFocusedInput === referralInputRef.current;

        if (
          isReferralInputFocused &&
          referralInputRef.current &&
          scrollViewRef.current
        ) {
          setTimeout(() => {
            referralInputRef.current?.measureLayout(
              scrollViewRef.current,
              (x, y) => {
                const screenHeight = Dimensions.get('window').height;
                const keyboardHeight = event.endCoordinates.height;
                const inputPosition = y;
                const visibleArea = screenHeight - keyboardHeight - 150;

                if (inputPosition > visibleArea) {
                  scrollViewRef.current.scrollTo({
                    y: inputPosition - visibleArea + 150,
                    animated: true,
                  });
                }
              },
              () => console.log('Failed to measure layout'),
            );
          }, 300);
        }
      },
    );

    return () => {
      keyboardDidShowListener.remove();
    };
  }, []);

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
          style={{flex: 1}}>
          <View style={styles.fixedHeader}>
            <View style={styles.iconContainer}>
              <FlexContainer direction="row" justifyContent="center">
                <IconSvgView svgStyle={styles.diamondIcon} source={diamond} />
                <IconSvgView svgStyle={styles.diamondIcon} source={diamond} />
                <IconSvgView svgStyle={styles.diamondIcon} source={diamond} />
                <IconSvgView
                  svgStyle={styles.diamondIcon}
                  source={diamondInactive}
                />
              </FlexContainer>
              <TouchableOpacity
                ref={supportIconRef}
                style={styles.icon}
                onPress={toggleSupportMenu}
                accessibilityLabel="Get support">
                <IconSvgView size={30} source={support} />
              </TouchableOpacity>
            </View>
          </View>

          <ScrollView
            ref={scrollViewRef}
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled">
            <View>
              <Text numberOfLines={2} style={styles.title}>
                {t('userSetup_title')}
              </Text>
            </View>
            <View>
              <Input
                inputTitle={t('your_name')}
                placeholder={t('name_example')}
                onChange={value => {
                  setName(value);
                  if (!touched) {
                    setTouched(true);
                  }
                }}
                value={name}
                error={nameError}
              />
            </View>
            <View>
              <Dropdown
                label={t('select')}
                data={genderData}
                onSelect={setGender}
                title={t('your_gender')}
                defaultValue={gender?.label}
              />
            </View>
            <View>
              <Input
                type="date"
                value={dob}
                onChange={handleDateChange}
                inputTitle={t('dob')}
                error={error}
              />
            </View>
            <View>
              <Dropdown
                label={t('select')}
                data={placesData}
                onSelect={item => setLocation(item.label)}
                title={t('where')}
                defaultValue={location}
              />
            </View>
            <View>
              <MultiSelect
                label={t('select')}
                onClear={() => setLanguage('')}
                data={languageData}
                onSelect={selectedItems => {
                  console.log(selectedItems);
                  setLanguage(selectedItems.map(item => item.label).join(', '));
                }}
                title={t('language')}
                defaultValue={driver?.preferred_language}
              />
            </View>

            <View ref={referralInputRef}>
              <Input
                inputTitle={t('referral_number')}
                onChange={handleReferralChange}
                value={referralNumber}
                keyboardType="numeric"
                maxLength={14}
                error={referralError}
                onFocus={handleReferralInputFocus}
                rightButton={{
                  title: t('verify'),
                  onPress: verifyReferral,
                  disabled:
                    isValidatingReferral ||
                    !/^\+91\s[0-9]{10}$/.test(referralNumber),
                }}
              />
              {referralDriverName &&
                referralDriverName !== 'Driver' &&
                referralVerified &&
                referralDriverName.trim() !== '' && (
                  <Text style={styles.successMessage}>
                    {t('referral_valid', {name: referralDriverName})}
                  </Text>
                )}
            </View>

            <View style={{height: 80}} />
          </ScrollView>

          <View style={styles.fixedFooter}>
            <Button
              title={t('continue')}
              style={styles.continueBtn}
              disabled={
                !name ||
                !gender ||
                !!nameError ||
                !language ||
                !location ||
                !dob ||
                !!error ||
                referralValidationNeeded
              }
              onPress={register}
            />
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
      <SupportMenu
        visible={supportModalVisible}
        onClose={() => setSupportModalVisible(false)}
        position={iconPosition}
      />
    </ImageBackground>
  );
};

export default UserSetup;
