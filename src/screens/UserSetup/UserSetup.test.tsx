import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import UserSetup from '.';

describe('UserSetup Component', () => {
    test('navigates to Welcome screen on Continue button press', () => {
        const mockNavigation = { navigate: jest.fn() };
        const { getByPlaceholderText, getByText } = render(
            <UserSetup navigation={mockNavigation} />
        );

        const input = getByPlaceholderText('eg: <PERSON>');
        fireEvent.changeText(input, '<PERSON>');

        const dropdown = getByText('Select');
        fireEvent.press(dropdown);

        fireEvent.press(getByText('Men'));

        const continueButton = getByText('Continue');
        fireEvent.press(continueButton);

        expect(mockNavigation.navigate).toHaveBeenCalledWith('Welcome', { name: '<PERSON>' });
    });
});
