import {StyleSheet, Platform} from 'react-native';
import {sizes, EBGaramondFont, colors, theme, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  safeArea: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: spacing.xl,
  },

  fixedHeader: {
    width: '100%',
    paddingTop: Platform.OS === 'ios' ? spacing.md : spacing.sm,
    backgroundColor: 'transparent',
    zIndex: 10,
  },

  fixedFooter: {
    width: '100%',
    paddingVertical: spacing.md,
    backgroundColor: colors.black,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },

  scrollContent: {
    paddingTop: spacing.md,
    paddingBottom: spacing.xxl * 2,
  },

  iconContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: spacing.md,
    alignItems: 'center',
  },

  icon: {
    marginTop: spacing.xl,
  },

  diamondIcon: {
    marginTop: spacing.xl,
    marginRight: spacing.xl,
  },

  title: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.largeTitle,
    fontWeight: '400',
    color: colors.white,
    flexWrap: 'wrap',
    width: '90%',
    marginBottom: spacing.sm,
  },

  continueBtn: {
    marginVertical: spacing.sm,
  },

  // Add these styles for the referral section
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.sm,
  },
  checkbox: {
    marginRight: spacing.md,
  },
  checkboxInner: {
    width: 24,
    height: 24,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },

  successMessage: {
    color: colors.green,
    marginTop: spacing.xs,
    fontFamily: GeistFont.regular,
    fontSize: sizes.body,
  },
  whatsappCheckboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.md,
  },
});
