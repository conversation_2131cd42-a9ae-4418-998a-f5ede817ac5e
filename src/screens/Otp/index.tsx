import React, {useState, useEffect, useRef, useCallback} from 'react';
import {
  ImageBackground,
  Text,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  View,
} from 'react-native';
import BackgroundTimer from 'react-native-background-timer';
import {images} from '../../constants';
import styles from './OtpStyle';
import {useToast} from '../../components/Toast/Toast';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import OTPInput from '../../components/OtpInput/OtpInput';
import Button from '../../components/Button/Button';
import Back from '../../icons/back.svg';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import {OTP_TIMEOUT, STATUS_CODE} from '../../constants/constants';
import diamond from '../../icons/diamond.svg';
import diamondInactive from '../../icons/diamondBlack.svg';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useTranslation} from 'react-i18next';
import {SafeAreaView} from 'react-native-safe-area-context';
import DriverService from '../../services/DriverService';
import {useLoader} from '../../hooks/useLoader';
import {useDriver} from '../../hooks/useDriver';
import {useAuth} from '../../hooks/useAuth';
import {debounce} from 'lodash';

interface OtpScreenProps {
  navigation: any;
  route: any;
}

const Otp: React.FC<OtpScreenProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {showLoader, hideLoader} = useLoader();
  const {showToast} = useToast();
  const {fetchDriver} = useDriver();
  const [isOtpComplete, setIsOtpComplete] = useState<boolean>(false);
  const [otp, setOtp] = useState<string>('');
  const [confirmation, setConfirmation] = useState<any>();
  const mobile = route.params?.mobile || '';
  const login = route.params?.login || '';
  const [error, setError] = useState<boolean>(false);
  const {checkAuthentication} = useAuth();
  const [timer, setTimer] = useState<number>(0);
  const otpInputRef = useRef<any>(null);
  const [isResendDisabled, setIsResendDisabled] = useState<boolean>(false);
  const [otpKey, setOtpKey] = useState<number>(0);
  const timerIdRef = useRef<number | null>(null);
  const receiveWhatsAppUpdates = route.params.whatsappPermission;

  useEffect(() => {
    return () => {
      if (timerIdRef.current !== null) {
        BackgroundTimer.clearInterval(timerIdRef.current);
        timerIdRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    const checkExistingTimer = async () => {
      try {
        const storedExpiryTime = await AsyncStorage.getItem('otpTimerExpiry');
        if (storedExpiryTime) {
          const expiryTime = parseInt(storedExpiryTime);
          const now = Date.now();
          if (expiryTime > now) {
            const remainingTime = Math.floor((expiryTime - now) / 1000);
            setTimer(remainingTime);
            setIsResendDisabled(true);
            startBackgroundTimer(remainingTime);
          } else {
            AsyncStorage.removeItem('otpTimerExpiry');
            setTimer(0);
            setIsResendDisabled(false);
          }
        }
      } catch (error) {
        console.error('Error restoring timer state:', error);
      }
    };

    checkExistingTimer();

    if (mobile) {
      sendOTP(mobile);
    } else {
      showToast(t('mobile_not_found_error'), 'failure');
    }
  }, [mobile]);

  const startBackgroundTimer = (duration: number) => {
    if (timerIdRef.current !== null) {
      BackgroundTimer.clearInterval(timerIdRef.current);
    }

    setTimer(duration);
    setIsResendDisabled(true);

    const expiryTime = Date.now() + duration * 1000;
    AsyncStorage.setItem('otpTimerExpiry', expiryTime.toString());

    timerIdRef.current = BackgroundTimer.setInterval(() => {
      setTimer(prevTimer => {
        const newValue = prevTimer - 1;

        if (newValue <= 0) {
          if (timerIdRef.current !== null) {
            BackgroundTimer.clearInterval(timerIdRef.current);
            timerIdRef.current = null;
          }
          AsyncStorage.removeItem('otpTimerExpiry');
          setIsResendDisabled(false);
          return 0;
        }

        return newValue;
      });
    }, 1000);
  };

  const handleChange = (otpValue: string) => {
    setOtp(otpValue);
  };

  const handleOtpComplete = useCallback(
    debounce((complete: boolean) => {
      if (complete) {
        console.log('this is called');
        setIsOtpComplete(true);
      } else {
        setIsOtpComplete(false);
      }
    }, 300),
    [],
  );

  const resetOtpInput = () => {
    setOtp('');
    setIsOtpComplete(false);
    setOtpKey(prevKey => prevKey + 1);

    // Remove the setTimeout which can cause timing issues
    if (otpInputRef.current && otpInputRef.current.resetFocus) {
      otpInputRef.current.resetFocus();
    }
  };

  const sendOTP = async (phone: string) => {
    try {
      showLoader();
      setError(false);
      resetOtpInput();

      const response = await DriverService.login({
        phone: phone,
        whatsappPermission: receiveWhatsAppUpdates,
      });
      if (response.status === STATUS_CODE.created) {
        setConfirmation(response.data.data.message);
        showToast(t('otp_send_sucess'), 'success');
        startBackgroundTimer(OTP_TIMEOUT);
      } else {
        throw new Error(t('unexpected_error'));
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err?.response?.data?.response?.code;
      const expiryTime = err?.response?.data?.response?.remainingTime;

      if (expiryTime) {
        startBackgroundTimer(Math.floor(expiryTime));
      }

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      } else if (STATUS_CODE.bad_request === status) {
        code === 'otp_cooldown' &&
          showToast(t('wait_before_resend'), 'failure');
      }
    } finally {
      hideLoader();
    }
  };

  const verifyOTP = async () => {
    try {
      showLoader();
      const response = await DriverService.verifyOtp(mobile, otp);

      if (response?.data?.data.verified) {
        if (timerIdRef.current !== null) {
          BackgroundTimer.clearInterval(timerIdRef.current);
          timerIdRef.current = null;
        }
        AsyncStorage.removeItem('otpTimerExpiry');

        await AsyncStorage.setItem(
          'accessToken',
          response.data?.data.accessToken,
        );
        await AsyncStorage.setItem(
          'refreshToken',
          response.data?.data.refreshToken,
        );
        await checkAuthentication();
        await fetchDriver();
        hideLoader();
        navigation.replace('NavigationGateway');
        showToast(t('otp_verified_success'), 'success');
      } else {
        throw new Error(t('otp_verification_failed'));
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err?.response?.data?.response?.code;
      console.log(err?.response?.data?.response?.code);

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      } else if (STATUS_CODE.bad_request === status) {
        code === 'invalid_otp' && showToast(t('incorrect_otp'), 'failure');
        code === 'otp_expired' && showToast(t('otp_expired'), 'failure');
        code === 'verification_code_not_found' &&
          showToast(t('verification_code_not_found'), 'failure');
      }
    } finally {
      hideLoader();
    }
  };

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 0}
        style={{flex: 1}}
        enabled={Platform.OS === 'ios'}>
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.fixedHeader}>
            <View style={styles.iconContainer}>
              <TouchableOpacity onPress={() => navigation.replace('Start')}>
                <IconSvgView svgStyle={styles.icon} source={Back} />
              </TouchableOpacity>
              <FlexContainer direction="row" justifyContent="center">
                <IconSvgView svgStyle={styles.diamondIcon} source={diamond} />
                <IconSvgView svgStyle={styles.diamondIcon} source={diamond} />
                {!login && (
                  <>
                    <IconSvgView
                      svgStyle={styles.diamondIcon}
                      source={diamondInactive}
                    />
                    <IconSvgView
                      svgStyle={styles.diamondIcon}
                      source={diamondInactive}
                    />
                  </>
                )}
              </FlexContainer>
            </View>
            <View>
              <Text style={styles.title}>
                {t('enter_otp')}
                {'\n'}
                {mobile}
              </Text>
            </View>
          </View>
          <View>
            <OTPInput
              key={otpKey}
              ref={otpInputRef}
              otp={otp}
              onOtpChange={handleChange}
              defaultValue={6}
              onOtpComplete={handleOtpComplete}
            />
          </View>
          <FlexContainer flex={1} direction="row">
            {timer > 0 && (
              <Text style={styles.resendTxt}>
                {`${t('resend_otp_in')} ${timer}s`}
              </Text>
            )}
            {timer === 0 && !isResendDisabled && (
              <FlexContainer direction="row" styles={{flexWrap: 'wrap'}}>
                <Text style={styles.resendTxt}>{t("didn't_get")}</Text>
                <TouchableOpacity
                  testID="resend-otp"
                  disabled={timer > 0}
                  onPress={() => {
                    resetOtpInput();
                    sendOTP(mobile);
                  }}>
                  <Text style={styles.resendOtp}>{t('resend_otp')}</Text>
                </TouchableOpacity>
              </FlexContainer>
            )}
          </FlexContainer>
        </SafeAreaView>

        <View style={styles.fixedFooter}>
          <Button
            title={t('continue')}
            style={styles.continueBtn}
            disabled={!isOtpComplete || error}
            onPress={verifyOTP}
          />
        </View>
      </KeyboardAvoidingView>
    </ImageBackground>
  );
};

export default Otp;
