import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import Otp from '.';
import { ToastProvider } from '../../components/Toast/Toast';

jest.mock('@react-native-firebase/auth', () => {

    return {
        __esModule: true,
        default: {
            auth: jest.fn(() => ({
                signInWithPhoneNumber: jest.fn(),
            })),
        },
    };
});


describe('<Otp />', () => {
    it('renders correctly', () => {
        const { getByText } = render(
            <ToastProvider>
                <Otp navigation={{ goBack: jest.fn() }} route={{ params: { mobile: '**********' } }} />
            </ToastProvider>
        );

        expect(getByText(/enter the otp sent to/i)).toBeTruthy();
    });

    it('sends OTP and verifies successfully', async () => {
        const { getByTestId, getByText } = render(
            <ToastProvider>
                <Otp navigation={{ goBack: jest.fn(), navigate: jest.fn() }} route={{ params: { mobile: '**********' } }} />
            </ToastProvider>
        );

        const signInWithPhoneNumberMock = jest.fn();
        require('@react-native-firebase/auth').default.auth.mockReturnValueOnce({
            signInWithPhoneNumber: signInWithPhoneNumberMock,
        });

        act(() => {
            fireEvent.press(getByTestId('resend-otp'));
        });

        act(() => {
            fireEvent.changeText(getByTestId('otp-input'), '**********');
        });

        act(() => {
            fireEvent.press(getByText(/continue/i));
        });

    });
});
