import {StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, theme, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  safeArea: {
    flex: 1,
    paddingHorizontal: spacing.xl,
  },

  iconContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: spacing.xxl * 1.5,
  },

  icon: {
    marginTop: spacing.xl,
  },

  diamondIcon: {
    marginTop: spacing.xl,
    marginRight: spacing.xl,
  },

  title: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.largeTitle,
    color: colors.white,
    marginBottom: spacing.xl,
  },

  resendTxt: {
    fontFamily: GeistFont.regular,
    fontSize: sizes.h6,
    color: colors.grey,
    paddingRight: spacing.sm,
    flexWrap: 'wrap',
  },

  resendOtp: {
    fontSize: sizes.h6,
    color: colors.white,
    textDecorationLine: 'underline',
    textDecorationColor: colors.grey,
    textDecorationStyle: 'solid',
    fontFamily: GeistFont.bold,
  },

  continueBtn: {
    width: '100%', 
    marginVertical: spacing.md, 
    marginHorizontal: 0, 
  },

  fixedHeader: {
    paddingTop: spacing.md,
    backgroundColor: colors.black,
    zIndex: 10,
  },

  scrollContent: {
    flexGrow: 1,
    paddingBottom: spacing.xxl,
  },

  fixedFooter: {
    width: '100%',
    backgroundColor: colors.black,
    paddingBottom: spacing.md,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    paddingHorizontal: spacing.xl,
  },
});
