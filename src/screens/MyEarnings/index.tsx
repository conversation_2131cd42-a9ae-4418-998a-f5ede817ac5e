import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  ImageBackground,
  ScrollView,
  NativeSyntheticEvent,
  NativeScrollEvent,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import {PanGestureHandler} from 'react-native-gesture-handler';
import images from '../../constants/images';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import TripService from '../../services/EarningsService';
import styles from './MyEarningsStyle';
import {useLoader} from '../../hooks/useLoader';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import chevronLeft from '../../icons/chevron-left.svg';
import chevronRight from '../../icons/chevron-right.svg';
import {monthAbbreviations, STATUS_CODE} from '../../constants/constants';
import {Earnings} from '../../types';
import {useToast} from '../../components/Toast/Toast';
import {useFocusEffect} from '@react-navigation/native';
import {spacing} from '../../constants/theme';
import EarningsService from '../../services/EarningsService';

interface EarningsScreenProps {
  navigation: any;
}

const MyEarnings: React.FC<EarningsScreenProps> = () => {
  const {t} = useTranslation();
  const [activeView, setActiveView] = useState<'daily' | 'weekly' | 'monthly'>(
    'daily',
  );
  const [earningsData, setEarningsData] = useState<Earnings[]>([]);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [totalEarnings, setTotalEarnings] = useState<number>(0);
  const [dailyEarnings, setDailyEarnings] = useState<number>(0);
  const [currentEarnings, setCurrentEarnings] = useState<number>(0);
  const {showLoader, hideLoader, loading} = useLoader();
  const scrollViewRef = useRef<ScrollView>(null);
  const [scrollX, setScrollX] = useState(0);

  const fetchTodayEarnings = async () => {
    try {
      const response = await EarningsService.getDailyEarnings();
      if (response.status === STATUS_CODE.ok) {
        setDailyEarnings(response.data.data[0].totalFare);
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const message = err?.response?.data?.message;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
      if (message) {
      }
    }
  };

  const fetchEarnings = async () => {
    try {
      showLoader();
      const totalResponse = await TripService.getAllEarnings();

      if (totalResponse.status === STATUS_CODE.ok) {
        setTotalEarnings(totalResponse.data.data.totalFare);
      }

      let response;
      switch (activeView) {
        case 'daily':
          response = await TripService.getDailyEarnings();
          break;
        case 'weekly':
          response = await TripService.getWeeklyEarnings();
          break;
        case 'monthly':
          response = await TripService.getMonthlyEarnings();
          break;
      }

      if (response?.status === STATUS_CODE.ok) {
        const fetchedData = response.data.data.earnings || response.data.data;
        setEarningsData(fetchedData);

        if (fetchedData.length > 0) {
          setSelectedItem(fetchedData[0].date);
          setCurrentEarnings(fetchedData[0].totalFare);
        }
      }
    } catch (err: any) {
      const message = err?.response?.data?.message || t('unexpected_error');
    } finally {
      hideLoader();
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      fetchEarnings();
      fetchTodayEarnings();
    }, [activeView]),
  );

  const handleViewChange = (view: 'daily' | 'weekly' | 'monthly') => {
    setActiveView(view);
    setSelectedItem(null);
    setCurrentEarnings(0);
  };

  const handleEarningsSelection = (item: Earnings) => {
    setSelectedItem(item.date);
    setCurrentEarnings(item.totalFare);
  };

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    setScrollX(event.nativeEvent.contentOffset.x);
  };

  const scrollScrollView = (direction: 'left' | 'right') => {
    const itemWidth = 100;
    const scrollAmount = direction === 'left' ? -itemWidth * 2 : itemWidth * 2;

    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: scrollX + scrollAmount,
        animated: true,
      });
    }
  };

  const formatDate = (date: string) => {
    if (!date) return '';

    const [month, day] = date.split(' ');
    return day
      ? `${monthAbbreviations[month] || month} ${day}`
      : `${monthAbbreviations[month] || month}`;
  };

  const renderSubtitleItem = (item: Earnings) => {
    const formattedDate =
      activeView === 'weekly'
        ? item.date.replace(' - ', ' -')
        : formatDate(item.date);
    const isActive = item.date === selectedItem;

    return (
      <TouchableOpacity
        key={item.date}
        onPress={() => handleEarningsSelection(item)}>
        <View style={styles.subtitleItem}>
          <Text
            style={
              isActive ? styles.activeSubtitleText : styles.inactiveSubtitleText
            }>
            {!loading && formattedDate}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <FlexContainer flex={1}>
      <ImageBackground source={images.bg2} style={styles.backgroundImage}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{t('my_earnings')}</Text>
        </View>

        <FadingHorizontalLine />

        <View style={styles.headingContainer}>
          <Text style={styles.headingTxt}>{t('today_earning')}</Text>
          <Text style={styles.headingTxt}>
            {t('rupee')} {dailyEarnings}
          </Text>
        </View>

        <FadingHorizontalLine />

        <View style={styles.headingContainer}>
          {['daily', 'weekly', 'monthly'].map(view => (
            <TouchableOpacity
              key={view}
              onPress={() => handleViewChange(view as any)}>
              <Text
                style={
                  activeView === view ? styles.activeTab : styles.inactiveTab
                }>
                {t(view)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <TouchableOpacity onPress={() => scrollScrollView('left')}>
            <IconSvgView
              svgStyle={{marginHorizontal: spacing.sm}}
              source={chevronLeft}
              size={20}
              color="black"
            />
          </TouchableOpacity>

          <PanGestureHandler>
            <ScrollView
              ref={scrollViewRef}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.subtitleContainer}
              onScroll={handleScroll}
              scrollEventThrottle={16}>
              {earningsData.map(renderSubtitleItem)}
            </ScrollView>
          </PanGestureHandler>

          <TouchableOpacity onPress={() => scrollScrollView('right')}>
            <IconSvgView
              svgStyle={{marginHorizontal: spacing.sm}}
              source={chevronRight}
              size={20}
              color="black"
            />
          </TouchableOpacity>
        </View>

        <View style={styles.rateContainer}>
          <View style={styles.rateCard}>
            <Text style={styles.rateTxt}>
              {t('rupee')} {currentEarnings}
            </Text>
          </View>
        </View>

        <FadingHorizontalLine />

        <View style={styles.headingContainer}>
          <Text style={styles.headingTxt}>{t('total_earning')}</Text>
          <Text style={styles.headingTxt}>
            {t('rupee')} {totalEarnings}
          </Text>
        </View>

        <FadingHorizontalLine />
      </ImageBackground>
    </FlexContainer>
  );
};

export default MyEarnings;
