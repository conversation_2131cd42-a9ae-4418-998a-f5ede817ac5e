import {Platform, StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, theme, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: spacing.xxl * 4,
  },
  title: {
    position: 'absolute',
    top: 20,
    left: 20,
    marginVertical: spacing.xxl,
    fontSize: sizes.h3,
    color: colors.white,
    fontFamily: EBGaramondFont.regular,
  },
  subtitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activeTab: {
    textAlign: 'center',
    color: colors.white,
    fontSize: sizes.h5,
    fontFamily: GeistFont.regular,
  },
  inactiveTab: {
    color: 'grey',
    fontSize: sizes.h6,
    fontFamily: GeistFont.regular,
  },
  headingContainer: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    margin: spacing.lg,
    paddingHorizontal: spacing.lg,
  },
  headingTxt: {
    fontSize: sizes.h4,
    color: colors.white,
    fontFamily: EBGaramondFont.regular,
    marginVertical: spacing.lg,
  },
  rateContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: spacing.lg,
  },
  rateCard: {
    backgroundColor: colors.white,
    borderRadius: spacing.md * 3,
    width: spacing.xl * 5,
    height: spacing.xxl * 2,
    alignItems: 'center',
    marginBottom: spacing.xl,
    justifyContent: 'center',
    marginLeft: 20, // Added margin to separate rate cards
  },
  
  rateCardRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  rateTxt: {
    color: colors.black,
    fontSize: sizes.h4,
    fontFamily: EBGaramondFont.regular,
    alignItems: 'center',
  },
  subtitleItem: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.sm,
  },
  activeSubtitleText: {
    color: colors.white,
    fontFamily: GeistFont.regular,
    fontSize: sizes.body,
    textAlign: 'center',
    backgroundColor: colors.darkCharcoal,
    padding: spacing.md,
    maxWidth: 100,
    minWidth: 80,
    borderRadius: spacing.sm,
  },
  inactiveSubtitleText: {
    color: colors.grey,
    fontFamily: GeistFont.regular,
    fontSize: sizes.body,
    textAlign: 'center',
    backgroundColor: colors.darkGrey,
    padding: spacing.md,
  },
  bonusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg * 2,
    marginVertical: spacing.md,
  },
  bonusLabel: {
    color: colors.white,
    fontSize: sizes.h5,
    fontFamily: GeistFont.regular,
  },
  bonusValue: {
    color: colors.white,
    fontSize: sizes.h5,
    fontFamily: GeistFont.regular,
  },
  scrollView: {
    flexGrow: 0,
  },
  chevronContainer: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.lg,
  },
  bonusText: {
    color: colors.primary,
    fontSize: sizes.h6,
    fontFamily: GeistFont.regular,
    marginTop: spacing.sm,
  },
  bonusTxt: {
    color: 'green', // Ensure visibility
    fontSize: sizes.h6,
    fontFamily: GeistFont.regular,
    marginTop: spacing.sm,
  },
});
