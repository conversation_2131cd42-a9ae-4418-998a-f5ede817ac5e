import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  ImageBackground,
  Text,
  ScrollView,
  TouchableOpacity,
  View,
  Keyboard,
} from 'react-native';
import {colors, images} from '../../constants';
import styles from './DlNumberStyle';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import Input from '../../components/Input/Input';
import Button from '../../components/Button/Button';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import Back from '../../icons/back.svg';
import {useToast} from '../../components/Toast/Toast';
import diamond from '../../icons/diamond.svg';
import {useTranslation} from 'react-i18next';
import {SafeAreaView} from 'react-native-safe-area-context';
import DriverService from '../../services/DriverService';
import {useLoader} from '../../hooks/useLoader';
import {useDriver} from '../../hooks/useDriver';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {STATUS_CODE} from '../../constants/constants';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import {useFocusEffect} from '@react-navigation/native';
import support from '../../icons/support.svg';
import SupportMenu from '../../components/SupportMenu/SupportMenu';

interface UserSetupProps {
  navigation: any;
  route: any;
}

const DlNumber: React.FC<UserSetupProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {showToast} = useToast();
  const {showLoader, hideLoader} = useLoader();
  const [dlNo, setDlNo] = useState('');
  const [dlExpiry, setDlExpiry] = useState('');
  // const [rcExpiry, setRcExpiry] = useState('');
  // const [insuranceExpiry, setInsuranceExpiry] = useState('');
  const [expiryError, setExpiryError] = useState('');
  const driverParam = route?.params?.driver || '';
  const fromProfile = route?.params?.fromProfile || false;
  const fromWelcome = route?.params?.fromWelcome || false;
  const {fetchDriver} = useDriver();
  const [hasChanges, setHasChanges] = useState(false);
  const [isEditing, setIsEditing] = useState(!fromProfile);
  const [initialValues, setInitialValues] = useState({
    dlNo: '',
    dob: '',
    dlExpiry: '',
    // rcExpiry: '',
    // insuranceExpiry: '',
    // insuranceNumber: '',
    // rcNumber: '',
  });
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const supportIconRef = useRef(null);
  const [supportModalVisible, setSupportModalVisible] = useState(false);
  const [iconPosition, setIconPosition] = useState(null);

  const toggleSupportMenu = () => {
    if (supportIconRef.current) {
      supportIconRef.current.measure(
        (
          _x: number,
          _y: number,
          width: number,
          height: number,
          pageX: number,
          pageY: number,
        ) => {
          setIconPosition({x: pageX, y: pageY, width, height});
          setSupportModalVisible(!supportModalVisible);
        },
      );
    } else {
      setSupportModalVisible(!supportModalVisible);
    }
  };

  useFocusEffect(
    useCallback(() => {
      const fetchData = async () => {
        const driver = await fetchDriver();

        if (driver) {
          const initialData = {
            dlNo: driver.driving_license_no || '',
            dlExpiry: driver.license_expiry || '',
            // rcExpiry: driver.rc_expiry || '',
            // insuranceExpiry: driver.insurance_expiry || '',
            // insuranceNumber: driver.insurance_number || '',
            // rcNumber: driver.rc_number || '',
          };
          setDlNo(initialData.dlNo);
          setDlExpiry(initialData.dlExpiry);
          // setRcExpiry(initialData.rcExpiry);
          // setInsuranceExpiry(initialData.insuranceExpiry);
          setInitialValues(initialData);
          setIsInitialLoad(false);
        }
      };

      fetchData();
    }, []),
  );

  useEffect(() => {
    if (!isInitialLoad) {
      const formIsValid = dlNo.trim() !== '' && dlExpiry && !expiryError;
      const hasValidContent = !!(dlNo.trim() !== '' && dlExpiry);
      setHasChanges(hasValidContent && formIsValid);
    }
  }, [dlNo, dlExpiry, expiryError, isInitialLoad]);

  const handleDlExpiry = (newDate: string) => {
    if (!isEditing) return;
    setDlExpiry(newDate);

    const today = new Date();
    const selectedDate = new Date(newDate);

    if (selectedDate < today) {
      setExpiryError(t('expiry_date_invalid'));
    } else {
      setExpiryError('');
    }
  };

  const handleBackNavigation = () => {
    if (fromWelcome || !driverParam) {
      navigation.navigate('Rc', {fromWelcome: true});
    } else {
      navigation.goBack();
    }
  };

  const handleContinue = async () => {
    try {
      showLoader();
      Keyboard.dismiss();

      if (fromProfile && !isEditing) {
        setIsEditing(true);
        hideLoader();
        return;
      }

      const response = await DriverService.update({
        drivingLicenseNo: dlNo || '',
        licenseExpiry: dlExpiry,
        // rcExpiry: rcExpiry,
        // insuranceExpiry: insuranceExpiry,
      });

      if (
        response.status === STATUS_CODE.ok ||
        response.status === STATUS_CODE.created
      ) {
        showToast(t('driver_update_sucessfull'), 'success');
        if (fromProfile) {
          await fetchDriver();
          if (getButtonTitle() === t('save')) {
            navigation.navigate('Welcome');
          } else {
            navigation.goBack();
          }
        } else if (driverParam) {
          await fetchDriver();
          AsyncStorage.removeItem('documents');
          navigation.reset({
            index: 0,
            routes: [{name: 'Welcome'}],
          });
        } else {
          navigation.navigate('Dl');
        }
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const message = err?.response?.data?.message;

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
      if (message) {
        // showToast(message, 'failure');
      }
    } finally {
      hideLoader();
    }
  };

  const handleCancel = useCallback(() => {
    if (fromProfile) {
      navigation.goBack();
    } else {
      navigation.navigate('Document');
    }
  }, [navigation, fromProfile]);

  const getButtonTitle = () => {
    if (fromProfile) {
      return isEditing ? t('save') : t('edit');
    }
    return driverParam ? t('update') : t('continue');
  };

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        {/* Fixed header */}
        <View style={!driverParam ? styles.iconContainer : styles.backIcon}>
          <TouchableOpacity style={styles.icon} onPress={handleBackNavigation}>
            <IconSvgView source={Back} />
          </TouchableOpacity>
          {!driverParam && (
            <>
              <FlexContainer direction="row" justifyContent="center">
                {[...Array(4)].map((_, index) => (
                  <IconSvgView
                    key={index}
                    svgStyle={styles.diamondIcon}
                    source={diamond}
                  />
                ))}
              </FlexContainer>
              <TouchableOpacity
                ref={supportIconRef}
                style={styles.icon}
                onPress={toggleSupportMenu}
                accessibilityLabel="Get support">
                <IconSvgView size={30} source={support} />
              </TouchableOpacity>
            </>
          )}
        </View>

        <ScrollView
          contentContainerStyle={{flexGrow: 1}}
          keyboardShouldPersistTaps="handled">
          <View>
            <Text style={styles.title}>{t('dl_no')}</Text>
          </View>
          <View>
            <Input
              value={dlNo}
              onChange={text => isEditing && setDlNo(text)}
              inputTitle={t('dl_no')}
              editable={isEditing}
              placeholder="AB-11-20210004567"
            />
          </View>
          <View>
            <Input
              type="date"
              value={dlExpiry}
              onChange={handleDlExpiry}
              inputTitle={t('dl_expiry_date')}
              error={expiryError}
              editable={isEditing}
            />
          </View>

          <FlexContainer justifyContent="flex-end">
            <Button
              title={getButtonTitle()}
              style={styles.continueBtn}
              onPress={handleContinue}
              disabled={
                !isEditing ? false : !dlNo.trim() || !dlExpiry || !!expiryError
              }
            />
            {(!driverParam || !isEditing) && (
              <Button
                onPress={handleCancel}
                title={t('cancel')}
                style={styles.cancelBtn}
                textColor={colors.red}
              />
            )}
          </FlexContainer>
        </ScrollView>
      </SafeAreaView>
      <SupportMenu
        visible={supportModalVisible}
        onClose={() => setSupportModalVisible(false)}
        position={iconPosition}
      />
    </ImageBackground>
  );
};

export default DlNumber;
