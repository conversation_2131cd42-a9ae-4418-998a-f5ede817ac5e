import {Platform, StyleSheet, ViewStyle} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  safeArea: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: spacing.xl,
  },

  iconContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: spacing.xxl,
  },

  backIcon: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },

  icon: {
    marginTop: spacing.xl,
  },

  diamondIcon: {
    marginTop: spacing.xl,
    marginRight: spacing.xl,
  },

  title: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.largeTitle,
    color: colors.white,
  },

  continueBtn: {
    marginVertical: spacing.xl,
  },

  cancelBtn: {
    backgroundColor: 'rgba(192, 104, 104, 0.04)',
    marginBottom: spacing.xl,
  },
});
