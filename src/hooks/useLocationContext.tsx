import React, {ReactNode, createContext, useContext, useState} from 'react';
import {LatLng} from 'react-native-maps';

interface LocationContextType {
  pickupAddress: string;
  whereto: string;
  pickupLocation: LatLng | null;
  dropLocation: LatLng | null;
  setPickupAddress: (pickup: string) => void;
  setPickupLocation: (pickup: LatLng | null) => void;
  setWhereto: (drop: string) => void;
  setDropLocation: (pickup: LatLng | null) => void;
}

const LocationContext = createContext<LocationContextType | undefined>(
  undefined,
);

export const LocationProvider: React.FC<{children: ReactNode}> = ({
  children,
}) => {
  const [pickupAddress, setPickupAddress] = useState('');
  const [pickupLocation, setPickupLocation] = useState<LatLng | null>(null);
  const [dropLocation, setDropLocation] = useState<LatLng | null>(null);
  const [whereto, setWhereto] = useState('');

  return (
    <LocationContext.Provider
      value={{
        pickupLocation,
        whereto,
        setPickupLocation,
        pickupAddress,
        dropLocation,
        setDropLocation,
        setPickupAddress,
        setWhereto,
      }}>
      {children}
    </LocationContext.Provider>
  );
};

export const useLocationContext = () => {
  const context = useContext(LocationContext);
  if (!context) {
    throw new Error(
      'useLocationContext must be used within a LocationProvider',
    );
  }
  return context;
};
