import BackgroundJob from 'react-native-background-actions';
import {
    Platform,
    Linking,
    Alert,
} from 'react-native';
import {useEffect, useState } from 'react';
import Geolocation from 'react-native-geolocation-service';
import { socket } from '../socket/socket';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getDirections, calculateRemainingRoute } from '../utils/MapUtils';
import { request, PERMISSIONS, RESULTS, check } from 'react-native-permissions';

const sleep = (time: any) => new Promise<void>((resolve) => setTimeout(() => resolve(), time));

BackgroundJob.on('expiration', () => {
    console.log('iOS: I am being closed!');
});

const options = {
    taskName: 'Engine',
    taskTitle: 'Engine',
    taskDesc: 'Engine is tracking your location',
    taskIcon: {
        name: 'ic_launcher',
        type: 'mipmap',
    },
    color: '#ff00ff',
    linkingURI: 'exampleScheme://chat/jane',
    parameters: {
        delay: 10000,
    },
};

function handleOpenURL(evt: any) {
    console.log(evt.url);
}

Linking.addEventListener('url', handleOpenURL);

export default function useBackgroundLocation({ driverId }: { driverId?: number }): [boolean, (state: boolean) => void] {
    let playing = BackgroundJob.isRunning();

    const [active, setActive] = useState(false)

    const fetchLocation = async (taskData: any) => {
        await new Promise(async (resolve) => {
            const { delay } = taskData;
            console.log(BackgroundJob.isRunning(), delay);
            try {
                for (let i = 0; BackgroundJob.isRunning(); i++) {
                    console.log('Runned -> ', i);
                    const active = JSON.parse((await AsyncStorage.getItem('active')) ?? 'false');
                    const trip = JSON.parse((await AsyncStorage.getItem('trip')) as string);

                    Geolocation.getCurrentPosition(
                        (position) => {
                            try {
                                const { latitude, longitude } = position.coords;
                                console.log(latitude, longitude, driverId);
                                if (trip) {
                                    const driverLocation = `${latitude},${longitude}`;
                                    const destinationLocation = `${trip.source.latitude},${trip.source.longitude}`;
                                    getDirections(driverLocation, destinationLocation).then(updatedRoute => {
                                        const reducedRoute = calculateRemainingRoute(updatedRoute.points, { latitude, longitude });
                                        socket.emit("driver.route", { tripId: trip.id, route: reducedRoute });
                                    });
                                }
                                socket.emit("driver.status", {
                                    driverId: driverId,
                                    isActive: active ? 1 : 0,
                                    currentLocation: { latitude, longitude }
                                });
                            } catch (e) {
                                console.log(e);
                            }
                        },
                        (error) => {
                            console.log("Error fetching location", error);
                        },
                        { enableHighAccuracy: true, distanceFilter: 0 }
                    );
                    await BackgroundJob.updateNotification({ taskDesc: 'Runned -> ' + i });
                    await sleep(delay);
                }
            } catch (e) {
                console.log("Error in fetchLocation", e);
            }
        });
    };

    const navigateToSettings = () => {
        Alert.alert(
            "Background Location Permission Required",
            "The app requires background location permission. Please go to settings and enable 'Allow Always' for location access.",
            [
                { text: "Cancel", style: "cancel" },
                { text: "Go to Settings", onPress: () => Linking.openSettings() }
            ]
        );
    };

    const requestLocationPermission = async () => {
        if (Platform.OS === 'ios') {
            const result = await check(PERMISSIONS.IOS.LOCATION_ALWAYS);
            console.log("iOS permission status:", result);
            if (result === RESULTS.GRANTED) {
                return true
            } else {
                return false
            }
        } else {
            const fineLocationGranted = await request(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);
            if (fineLocationGranted === RESULTS.GRANTED) {
                const backgroundLocationGranted = await request(PERMISSIONS.ANDROID.ACCESS_BACKGROUND_LOCATION);
                if (backgroundLocationGranted === RESULTS.GRANTED) {
                    return true
                } else {
                    return false
                }
            } else {
                return false
            }
        }
    };

    const toggleBackground = async () => {
        playing = !playing;
        if (playing) {
            try {
                console.log('Trying to start background service');
                await BackgroundJob.start(fetchLocation, options);
                console.log('Background service started successfully!');
            } catch (e) {
                console.log('Error starting background service', e);
            }
        } else {
            console.log('Stopping background service');
            await BackgroundJob.stop();
        }
    };

    useEffect(() => {
        AsyncStorage.setItem('active', JSON.stringify(active))
        if (active) toggleBackground()
        else {
            console.log('Stop background service');
            BackgroundJob.stop();
        }
    }, [active])

    const handleActive = async (state: boolean) => {
        if (state)
            if (await requestLocationPermission())
                setActive(true)
            else navigateToSettings()
        else setActive(false)
    }

    return [active, handleActive]
}
