import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  Dispatch,
  SetStateAction,
} from 'react';
import DriverService from '../services/DriverService';
import {Driver} from '../types';
import {STATUS_CODE} from '../constants/constants';
import {replace} from '../router/navigationService';
import {Platform} from 'react-native';
import {getBuildNumber} from 'react-native-device-info';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useAuth} from './useAuth';

interface DriverContextType {
  driver: Driver | null;
  setDriver: React.Dispatch<React.SetStateAction<Driver | null>>;
  fetchDriver: () => Promise<Driver>;
  upi: string;
  setUpi: Dispatch<SetStateAction<string>>;
  isVerified: boolean;
  setIsVerified: Dispatch<SetStateAction<boolean>>;
  forceUpdate: boolean;
  setForceUpdate: Dispatch<SetStateAction<boolean>>;
  isSystemPaused: boolean;
  setIsSystemPaused: Dispatch<SetStateAction<boolean>>;
  systemPauseReason: string;
  setSystemPauseReason: Dispatch<SetStateAction<string>>;
}

const DriverContext = createContext<DriverContextType | undefined>(undefined);

export const DriverProvider: React.FC<{children: ReactNode}> = ({children}) => {
  const [driver, setDriver] = useState<Driver | null>(null);
  const [upi, setUpi] = useState('');
  const [isVerified, setIsVerified] = useState<boolean>(false);
  const [forceUpdate, setForceUpdate] = useState<boolean>(false);
  const [isSystemPaused, setIsSystemPaused] = useState<boolean>(false);
  const [systemPauseReason, setSystemPauseReason] = useState<string>('');

  const checkSuspensionStatus = async () => {
    const accessToken = await AsyncStorage.getItem('accessToken');
    if (!accessToken) {
      return false;
    }
    
    try {
      const os = Platform.OS.toString();
      const currentVersion = await getBuildNumber().toString();

      const response = await DriverService.getDriver(os, currentVersion);
      if (response.status === STATUS_CODE.ok) {
        const driverData = response.data?.data;
        setDriver(driverData);
        setForceUpdate(driverData.forceUpdate);
        if (driverData.status && driverData.status.status === 'SystemPause') {
          setIsSystemPaused(true);
          setSystemPauseReason(driverData.status.message);
        } else {
          setIsSystemPaused(false);
          setSystemPauseReason('');
        }

        await AsyncStorage.setItem('driver', JSON.stringify(driverData));
        
        if (driverData.id) {
          await AsyncStorage.setItem('userId', driverData.id.toString());
        }
        
        return driverData;
      }
    } catch (err: any) {
      const status = err?.response?.status;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        replace('Fallback');
        return false;
      }
    }
    return false;
  };

  return (
    <DriverContext.Provider
      value={{
        driver,
        setDriver,
        fetchDriver: checkSuspensionStatus,
        upi,
        setUpi,
        isVerified,
        setIsVerified,
        forceUpdate,
        setForceUpdate,
        isSystemPaused,
        setIsSystemPaused,
        systemPauseReason,
        setSystemPauseReason,
      }}>
      {children}
    </DriverContext.Provider>
  );
};

export const useDriver = (): DriverContextType => {
  const context = useContext(DriverContext);
  if (!context) {
    throw new Error('useDriver must be used within a DriverProvider');
  }
  return context;
};
