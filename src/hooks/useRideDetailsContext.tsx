import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  Dispatch,
  SetStateAction,
  useEffect,
} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useFocusEffect} from '@react-navigation/native';
import {Message} from '../types';
import {useDriver} from './useDriver';
import {
  checkForegorundLocationPermission,
  checkLocationPermission,
} from '../constants/permissions';

interface RideDetailsContextProps {
  pickup: string;
  setPickup: (pickup: string) => void;
  destination: string;
  setDestination: (destination: string) => void;
  routeCoordinates: {latitude: number; longitude: number}[];
  setRouteCoordinates: Dispatch<
    SetStateAction<{latitude: number; longitude: number}[]>
  >;
  showRideModal: boolean;
  setShowRideModal: (value: boolean) => void;
  showRideStatusModal: boolean;
  setShowRideStatusModal: (value: boolean) => void;
  tripDetails: any;
  setTripDetails: Dispatch<SetStateAction<any>>;
  tripId: any;
  setTripId: Dispatch<SetStateAction<any>>;
  userDetails: any;
  setUserDetails: Dispatch<SetStateAction<any>>;
  rideExpiryTime: number;
  setRideExpiryTime: Dispatch<SetStateAction<number>>;
  messages: Message[];
  setMessages: Dispatch<SetStateAction<Message[]>>;
}

const RideDetailsContext = createContext<RideDetailsContextProps | undefined>(
  undefined,
);

interface RideDetailsProviderProps {
  children: ReactNode;
}

export let rideDetails = {
  trip_id: null,
};

export const RideDetailsProvider: React.FC<RideDetailsProviderProps> = ({
  children,
}) => {
  const [pickup, setPickup] = useState<string>('');
  const {driver} = useDriver();
  const [destination, setDestination] = useState<string>('');
  const [routeCoordinates, setRouteCoordinates] = useState<
    {latitude: number; longitude: number}[]
  >([]);
  const [showRideModal, setShowRideModal] = useState<boolean>(false);
  const [showRideStatusModal, setShowRideStatusModal] =
    useState<boolean>(false);
  const [tripDetails, setTripDetails] = useState(null);
  const [tripId, setTripId] = useState<string | null>();
  const [userDetails, setUserDetails] = useState(null);
  const [rideExpiryTime, setRideExpiryTime] = useState(0);
  const [messages, setMessages] = useState<Message[]>([]);
  const [locationPermission, setLocationPermission] = useState<boolean>(false);

  const checkPermissions = async () => {
    const permissionGranted = await checkForegorundLocationPermission();
    const backgroundGranted = await checkLocationPermission();
    if (permissionGranted || backgroundGranted) {
      setLocationPermission(permissionGranted && backgroundGranted);
    }
  };

  useEffect(() => {
    (async () => {
      await checkPermissions();
      const showModal = await AsyncStorage.getItem('showRideModal');
      const showRideStatusModal = await AsyncStorage.getItem(
        'showRideStatusCheck',
      );
      const rideStatusModal =
        showRideStatusModal && JSON.parse(showRideStatusModal) && driver;
      rideStatusModal &&
        setShowRideStatusModal(JSON.parse(showRideStatusModal));
      rideDetails.trip_id && setTripId(rideDetails.trip_id);
      const rideModal = showModal && JSON.parse(showModal) && driver;
      rideModal &&
        locationPermission &&
        setShowRideModal(JSON.parse(showModal));
      await AsyncStorage.setItem('pickup', pickup);
      await AsyncStorage.setItem('destination', destination);
    })();
  }, [driver]);

  return (
    <RideDetailsContext.Provider
      value={{
        pickup,
        setPickup,
        destination,
        setDestination,
        routeCoordinates,
        setRouteCoordinates,
        showRideModal,
        setShowRideModal,
        tripDetails,
        setTripDetails,
        tripId,
        setTripId,
        userDetails,
        setUserDetails,
        rideExpiryTime,
        setRideExpiryTime,
        messages,
        setMessages,
        showRideStatusModal,
        setShowRideStatusModal,
      }}>
      {children}
    </RideDetailsContext.Provider>
  );
};

export const useRideDetails = () => {
  const context = useContext(RideDetailsContext);
  if (!context) {
    throw new Error('useRideDetails must be used within a RideDetailsProvider');
  }
  return context;
};
