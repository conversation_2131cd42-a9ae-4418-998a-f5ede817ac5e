import React, { createContext, useContext, useState, ReactNode, useCallback, useEffect } from 'react';
import { Image, View, StyleSheet, Modal } from 'react-native';

interface LoaderContextProps {
  showLoader: () => void;
  hideLoader: () => void;
  loading: Boolean
}

const LoaderContext = createContext<LoaderContextProps | undefined>(undefined);

export const LoaderProvider = ({ children }: { children: ReactNode }) => {
  const [loading, setLoading] = useState(false);

  const showLoader = useCallback(() => setLoading(true), []);
  const hideLoader = useCallback(() => setLoading(false), []);

  return (
    <LoaderContext.Provider value={{ showLoader, hideLoader, loading }}>
      {children}
      {loading && (
        <Modal transparent>
          <View style={styles.loaderContainer}>
            <Image
              source={require('../icons/LOADER.gif')}
              style={styles.loaderImage}
            />
          </View>
        </Modal>
      )}
    </LoaderContext.Provider>
  );
};

export const useLoader: () => LoaderContextProps = () => {
  const [loading, setLoading] = useState(false);

  const showLoader = useCallback(() => setLoading(true), []);
  const hideLoader = useCallback(() => setLoading(false), []);
  const context = useContext(LoaderContext);

  useEffect(() => {
    loading && context?.showLoader()
    !loading && context?.hideLoader()
  }, [loading])

  if (!context) {
    throw new Error('useLoader must be used within a LoaderProvider');
  }
  return { showLoader, hideLoader, loading };
};

const styles = StyleSheet.create({
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderImage: {
    width: 50,
    height: 50,
  },
});
