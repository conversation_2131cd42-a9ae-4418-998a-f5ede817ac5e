import React, {
  createContext,
  useContext,
  useState,
  useRef,
  useEffect,
} from 'react';
import {AppState, Linking, Text} from 'react-native';
import {useToast} from '../components/Toast/Toast';
import {useAuth} from './useAuth';
import Geolocation from 'react-native-geolocation-service';
import GeofenceService from '../services/GeofenceService';
import {STATUS_CODE} from '../constants/constants';
import {
  checkForegorundLocationPermission,
  checkLocationPermission,
  checkPreciseLocationPermission,
  requestFineLocationPermission,
} from '../constants/permissions';
import Snackbar from '../components/SnackBar';
import {useTranslation} from 'react-i18next';

interface GeofenceContextType {
  showGeofenceModal: boolean;
  setShowGeofenceModal: (value: boolean) => void;
  startGeofencing: () => void;
  stopGeofencing: () => void;
}

const GeofenceContext = createContext<GeofenceContextType | undefined>(
  undefined,
);

export const GeofenceProvider: React.FC<React.PropsWithChildren<{}>> = ({
  children,
}) => {
  const {t} = useTranslation();
  const [showGeofenceModal, setShowGeofenceModal] = useState(false);
  const [showSnackbar, setShowSnackbar] = useState(false);
  const {showToast} = useToast();
  const {isAuthenticated} = useAuth();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [preciseLocation, setPreciseLocation] = useState(false);

  const fetchCurrentLocation = () => {
    Geolocation.getCurrentPosition(
      position => {
        const {latitude, longitude} = position.coords;
        if (latitude && longitude) {
          if (isAuthenticated) {
            checkGeofence({latitude, longitude});
          }
        }
      },
      async (error: {message: string}) => {
        if (error.message == 'No location provider available.') {
          await requestFineLocationPermission();
        } else showToast('Geolocation error: ' + error.message);
      },
      {
        timeout: 10000,
        maximumAge: 0,
      },
    );
  };

  const checkGeofence = async (currentLocation: {
    latitude: number;
    longitude: number;
  }) => {
    if (!isAuthenticated) return;

    try {
      const {data} = await GeofenceService.checkGeofence(currentLocation);
      const isInside = data.data.isInside;
      setShowGeofenceModal(!isInside);
    } catch (err: any) {
      const status = err?.response?.status;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
    }
  };

  const startGeofencing = async () => {
    const locationPermissionGranted = await checkForegorundLocationPermission();

    if (!locationPermissionGranted) {
      const permissionRequested = await requestFineLocationPermission();

      if (!permissionRequested) {
        setShowSnackbar(true);
      } else {
        if (isAuthenticated) {
          fetchCurrentLocation();
          intervalRef.current = setInterval(() => {
            fetchCurrentLocation();
          }, 20000);
        }
      }
    } else {
      if (isAuthenticated) {
        fetchCurrentLocation();
        intervalRef.current = setInterval(() => {
          fetchCurrentLocation();
        }, 20000);
      }
    }
  };

  const stopGeofencing = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
      console.log('Geofencing stopped.', intervalRef.current);
    }
    setShowSnackbar(false);
    setShowGeofenceModal(false);
  };

  const handleSnackbarOkPress = async () => {
    setShowSnackbar(false);
    Linking.openSettings();
    const permissionGranted = await checkForegorundLocationPermission();
    const preciseLocation = await checkPreciseLocationPermission();

    if (permissionGranted && preciseLocation) {
      if (isAuthenticated) {
        fetchCurrentLocation();
        intervalRef.current = setInterval(() => {
          fetchCurrentLocation();
        }, 20000);
      }
    } else {
      setShowSnackbar(true);
    }
  };

  useEffect(() => {
    const subscription = AppState.addEventListener(
      'change',
      async nextAppState => {
        if (nextAppState === 'active') {
          const permissionGranted = await checkForegorundLocationPermission();
          const backgroundGranted = await checkLocationPermission();
          const preciseLocation = await checkPreciseLocationPermission();
          if (preciseLocation) {
            setPreciseLocation(true);
          }
          if (permissionGranted || backgroundGranted) {
            setShowSnackbar(false);
          }
        }
      },
    );

    return () => {
      subscription.remove();
    };
  }, [AppState]);

  return (
    <>
      <GeofenceContext.Provider
        value={{
          showGeofenceModal,
          setShowGeofenceModal,
          startGeofencing,
          stopGeofencing,
        }}>
        {children}
      </GeofenceContext.Provider>
      <Snackbar
        visible={showSnackbar}
        message={[
          t('please_provide') + ' ',
          <Text key="bold" style={{fontWeight: 'bold'}}>
            {t('allow_while_app')}
          </Text>,
          ' ',
          t('foreground_location_message'),
        ]}
        onOkPress={handleSnackbarOkPress}
      />
    </>
  );
};

export const useGeofence = () => {
  const context = useContext(GeofenceContext);
  if (!context)
    throw new Error('useGeofence must be used within a GeofenceProvider');
  return context;
};
