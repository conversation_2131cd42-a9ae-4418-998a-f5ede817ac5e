import {NativeModules, NativeEventEmitter} from 'react-native';
import {act, useCallback, useEffect, useState} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  checkLocationPermission,
  checkNotificationPermission,
  checkOverlayPermission,
} from '../constants/permissions';
import {userLocationContext} from './userLocationContext';
import {isUndefined} from 'lodash';
import {navigate} from '../router/navigationService';
import RideService from '../services/RideService';
import {useFocusEffect} from '@react-navigation/native';
import {STATUS_CODE} from '../constants/constants';

const {
  LocationModule,
  FloatingWindow,
  LocationPermissionModule,
  PermissionModule,
} = NativeModules;
const locationEventEmitter = new NativeEventEmitter(LocationModule);
const locationPermissionEventEmitter = new NativeEventEmitter(
  LocationPermissionModule,
);

export default function useBackgrounLocationAndroid({
  driverId,
}: {
  driverId?: number;
}): [boolean | undefined, (state: boolean) => void] {
  const [active, setActive] = useState<boolean | undefined>();
  const {userLocation} = userLocationContext();

  const checkPermissions = async () => {
    try {
      const locationGranted = await checkLocationPermission();
      const overlayGranted = await checkOverlayPermission();
      const notificationGranted = await checkNotificationPermission();
      const batteryGranted = await PermissionModule.checkBatteryOptimization();

      return (
        locationGranted &&
        overlayGranted &&
        notificationGranted &&
        batteryGranted
      );
    } catch (error) {
      console.error('Error checking permissions:', error);
      return false;
    }
  };

  const loadActiveState = async () => {
    try {
      const activeState = await AsyncStorage.getItem('active');
      if (activeState) {
        setActive(JSON.parse(activeState));
      } else {
        setActive(false);
        try {
          const response = await RideService.sendDriverStatus({
            isActive: 0,
          });
        } catch (err: any) {
          const status = err?.response?.status;
          const code = err.response.data.response.code;
          if (
            [STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)
          ) {
            return;
          }
          if (STATUS_CODE.bad_request === status) {
            code === 'status_update_failed' &&
              console.log('status_update_failed');
          }
        }
        stopLocationService();
      }
    } catch (error) {
      console.error('Error loading active state:', error);
    }
  };

  useEffect(() => {
    const checkAndLoad = async () => {
      const permissionGiven = await checkPermissions();
      if (permissionGiven) {
        await loadActiveState();
      }
    };

    checkAndLoad();
  }, []);

  const stopLocationService = () => {
    const LocationFetchTrigger = NativeModules.LocationModule;
    if (LocationFetchTrigger) {
      LocationFetchTrigger.stopService();
      LocationModule.stopEnhancedService();
      LocationModule.stopLocationService();
    } else {
      console.log(
        'skt-- The location fetch trigger module is null. Cannot stop service',
      );
    }
  };

  const fetchLocation = async () => {
    const trip = JSON.parse((await AsyncStorage.getItem('trip')) as string);
    const LocationFetchTrigger = NativeModules.LocationModule;
    if (LocationFetchTrigger && active) {
      LocationModule.startService(10000);
      LocationModule.startLocationService(10);
      LocationModule.startEnhancedService(10);
      locationEventEmitter.removeAllListeners('locationUpdate');
      locationEventEmitter.addListener('locationUpdate', async location => {
        try {
          try {
            const {latitude, longitude} = location;
            try {
              const response = await RideService.sendDriverStatus({
                isActive: active ? 1 : 0,
                currentLocation: {latitude, longitude},
              });
            } catch (err: any) {
              const status = err?.response?.status;
              const code = err.response.data.response.code;
              if (
                [STATUS_CODE.not_found, STATUS_CODE.server_error].includes(
                  status,
                )
              ) {
                return;
              }
              if (STATUS_CODE.bad_request === status) {
                code === 'status_update_failed' &&
                  console.log('status_update_failed');
              }
            }
          } catch (e) {
            console.log(e);
          }
        } catch (error) {
          console.warn('Location callback error:', error);
        }
      });
    } else {
      console.log('skt-- The location fetch trigger module is null');
    }

    if (LocationPermissionModule) {
      LocationPermissionModule.startListening();
      locationPermissionEventEmitter.removeAllListeners(
        'onLocationPermissionChanged',
      );
      locationPermissionEventEmitter.addListener(
        'onLocationPermissionChanged',
        async hasPermission => {
          console.log('skt-- Location permission changed:', hasPermission);
          // Handle the permission change here
          stopLocationService();
          await FloatingWindow.hide();
        },
      );
    }
  };

  useFocusEffect(
    useCallback(() => {
      const updateLocationService = async () => {
        if (active) {
          await fetchLocation();
        } else {
          stopLocationService();
          await FloatingWindow.hide();
        }
      };

      if (!isUndefined(active)) {
        updateLocationService();
      }
      return () => {};
    }, [active]),
  );

  const handleActive = async (state: boolean) => {
    if (state) {
      const allPermissionsGranted = await checkPermissions();

      if (allPermissionsGranted) {
        setActive(true);
        await AsyncStorage.setItem('active', JSON.stringify(true));
      } else {
        setActive(false);
        const response = await RideService.sendDriverStatus({
          isActive: 0,
        });
        stopLocationService();
        navigate('PermissionList');
      }
    } else {
      setActive(false);
      await AsyncStorage.setItem('active', JSON.stringify(false));
      stopLocationService();
      try {
        const response = await RideService.sendDriverStatus({
          isActive: 0,
        });
      } catch (err: any) {
        const status = err?.response?.status;
        const code = err.response.data.response.code;
        if (
          [STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)
        ) {
          return;
        }
        if (STATUS_CODE.bad_request === status) {
          code === 'status_update_failed' &&
            console.log('status_update_failed');
        }
      }
    }
  };

  return [active, handleActive];
}
