import {useTranslation} from 'react-i18next';

const needsSmallerFonts = (locale: string) => {
  return locale === 'ta' || locale === 'ma';
};

export const useFontSizes = () => {
  const {i18n} = useTranslation();
  const locale = i18n.language;

  const isSmaller = needsSmallerFonts(locale);

  return {
    largeTitle: isSmaller ? 36 : 36,
    h1: isSmaller ? 32 : 32,
    h2: isSmaller ? 28 : 28,
    h3: isSmaller ? 24 : 24,
    h4: isSmaller ? 20 : 20,
    h5: isSmaller ? 18 : 18,
    h6: isSmaller ? 16 : 16,
    body: isSmaller ? 14 : 14,
  };
};
