import { useEffect } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import KeepAwake from '../utils/KeepAwake';

/**
 * Custom hook to manage screen wake lock
 * 
 * This hook will activate the keep awake functionality when the component mounts
 * and will manage the screen wake lock based on the app state (foreground/background)
 * 
 * @param enabled - Optional boolean to enable/disable keep awake functionality
 * @returns void
 * 
 * @example
 * ```tsx
 * // In any component where you want to keep the screen awake
 * import { useKeepAwake } from '../hooks/useKeepAwake';
 * 
 * const MyComponent = () => {
 *   // This will keep the screen awake while this component is mounted
 *   useKeepAwake();
 *   
 *   // Or conditionally enable it
 *   // useKeepAwake(isNavigating);
 *   
 *   return (
 *     // your component
 *   );
 * };
 * ```
 */
export const useKeepAwake = (enabled = true) => {
  useEffect(() => {
    if (!enabled) return;
    
    try {
      // Activate keep awake when the component mounts
      // No need to call activate here, as it's already managed by App.tsx
      // This prevents duplicate calls that might be causing issues
      console.log('useKeepAwake: Component mounted with keep awake enabled');
      
      // We don't need to handle app state changes here since it's already
      // being handled at the App level
      
      // Simply return a cleanup function that doesn't do anything
      return () => {
        console.log('useKeepAwake: Component unmounted');
        // Don't deactivate here, as it might affect other components that need keep awake
      };
    } catch (error) {
      console.error('Error in useKeepAwake hook:', error);
      return () => {};
    }
  }, [enabled]);
};
