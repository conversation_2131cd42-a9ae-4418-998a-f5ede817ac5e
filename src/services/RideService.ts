import Api from './Api';

interface DriverStatusPayload {
  isActive: number;
  currentLocation?: {
    latitude: number;
    longitude: number;
  };
}

const defaultHeaders = {
  'Keep-Alive': 'timeout=120',
};

export default {
  sendDriverStatus({isActive, currentLocation}: DriverStatusPayload) {
    return Api.post(
      'driver/status',
      {
        isActive,
        currentLocation,
      },
      {headers: defaultHeaders},
    );
  },

  acceptRide(id: number, accepted: boolean) {
    return Api.post(
      `ride/${id}/response`,
      {
        accepted: accepted,
      },
      {headers: defaultHeaders},
    );
  },

  cancelRide(id: number) {
    return Api.post(
      `/ride/cancel`,
      {
        tripId: id,
        status: 'driver_cancelled',
      },
      {headers: defaultHeaders},
    );
  },

  verifyOtp(id: number, otp: string) {
    return Api.post(
      `ride/verify-otp`,
      {
        tripId: id,
        otp: otp,
      },
      {headers: defaultHeaders},
    );
  },

  statusCheck(id: number, response: boolean) {
    return Api.post(
      `/ride/status/action`,
      {
        tripId: id,
        response: response ? "CONTINUING" : "STOPPING", 
      },
      { headers: defaultHeaders },
    );
  },
  
  completeRide(id: number) {
    return Api.post(`ride/${id}/complete`, {headers: defaultHeaders});
  },

  fetchUserDetails(id: number) {
    return Api.post(`ride/user`, {tripId: id}, {headers: defaultHeaders});
  },
};
