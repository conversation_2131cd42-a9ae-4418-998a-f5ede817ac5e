import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import {replace} from '../router/navigationService';
import axiosRetry from 'axios-retry';
import Config from 'react-native-config';
import TokenService from './TokenService';

// Create axios instance
const axiosInstance = axios.create({
  baseURL: Config.API_URL,
});

// Add retry logic
axiosRetry(axiosInstance, {
  retries: 3,
  retryDelay: () => 1000,
  retryCondition: error => {
    if (
      error?.response?.status === 429 &&
      error.config?.url?.includes('refresh')
    ) {
      // Skip retrying refresh API on 429
      console.warn('429 on refresh token endpoint – skipping axios-retry');
      return false;
    }
    return (
      axiosRetry.isNetworkOrIdempotentRequestError(error) ||
      (error?.response && error.response.status >= 500) ||
      !error.response
    );
  },
  onRetry: (retryCount, error) => {
    console.log(
      `Retry attempt #${retryCount} for request: ${
        error?.config?.url ?? 'unknown URL'
      }`,
    );
  },
});

// Request interceptor to handle authorization
axiosInstance.interceptors.request.use(
  async config => {
    if (config.url && !config.url.includes('auth/refresh/driver')) {
      try {
        // Check network connectivity first
        const NetInfo = require('@react-native-community/netinfo');
        const networkState = await NetInfo.fetch();

        if (!networkState.isConnected) {
          // If we're offline, check if this is a critical API call
          // For non-critical calls, we might want to delay or queue them
          console.log('No network connectivity for request:', config.url);

          // For important requests, throw error so they can be retried later
          if (!config.headers?.['allow-offline']) {
            throw new Error('No network connectivity');
          }
        }

        const refreshNeeded = await TokenService.checkTokenRefreshNeeded();
        if (refreshNeeded) {
          console.log('Token refresh needed before request');
          const refreshSuccessful = await TokenService.refreshTokens();

          if (!refreshSuccessful) {
            console.log(
              'Token refresh failed, proceeding with existing token if available',
            );
          }
        }

        const accessToken = await AsyncStorage.getItem('accessToken');
        if (accessToken) {
          config.headers.Authorization = `Bearer ${accessToken}`;
        }
      } catch (error) {
        console.error('Error in token refresh check:', error);
        // Still attempt to set the existing token
        const accessToken = await AsyncStorage.getItem('accessToken');
        if (accessToken) {
          config.headers.Authorization = `Bearer ${accessToken}`;
        }
      }
    } else {
      const accessToken = await AsyncStorage.getItem('accessToken');
      if (accessToken) {
        config.headers.Authorization = `Bearer ${accessToken}`;
      }
    }

    // Automatically set Content-Type to multipart/form-data if data is FormData
    if (config.data instanceof FormData) {
      config.headers['Content-Type'] = 'multipart/form-data';
    }

    return config;
  },
  error => Promise.reject(error),
);

// Updated Logout helper with error logging
async function handleLogoutAndRedirect(
  sourceId = 'unknown',
  errorMsg = 'Session expired',
) {
  try {
    const accessToken = await AsyncStorage.getItem('accessToken');
    const refreshToken = await AsyncStorage.getItem('refreshToken');
    const userIdString = await AsyncStorage.getItem('userId');
    const userId = userIdString ? parseInt(userIdString, 10) : null;

    const userType = 'driver';

    const apiUrl = Config.API_URL || '';
    const baseUrl = apiUrl.endsWith('/') ? apiUrl : `${apiUrl}/`;

    const formattedErrorMsg =
      typeof errorMsg === 'object' && errorMsg !== null
        ? JSON.stringify(errorMsg)
        : String(errorMsg);

    try {
      await axios.post(`${baseUrl}logout-logs`, {
        accessToken,
        refreshToken,
        userId,
        userType,
        errorMessage: `[Source:${sourceId}] ${formattedErrorMsg}`,
      });
    } catch (logError: any) {
      console.log(
        'Failed to log refresh token failure:',
        logError?.message || logError,
      );
    }

    await AsyncStorage.clear();
    replace('Start');

    return Promise.reject(
      new Error(
        `[Source:${sourceId}] ${formattedErrorMsg}. Redirected to Start.`,
      ),
    );
  } catch (error: any) {
    // If the logging itself fails, still perform the logout
    console.error('Error during logout process:', error);
    await AsyncStorage.clear();
    replace('Start');
    return Promise.reject(
      new Error(`Failed logout process: ${error?.message || 'Unknown error'}`),
    );
  }
}

// Response interceptor
axiosInstance.interceptors.response.use(
  response => response,
  async (error: any) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = await AsyncStorage.getItem('refreshToken');

        if (!refreshToken) throw new Error('Refresh token not found');

        const apiUrl = Config.API_URL || '';
        const baseUrl = apiUrl.endsWith('/') ? apiUrl : `${apiUrl}/`;
        const refreshEndpoint = `${baseUrl}auth/refresh/driver`;

        let refreshResponse;

        try {
          refreshResponse = await axios.get(refreshEndpoint, {
            headers: {
              Authorization: `Bearer ${refreshToken}`,
              'Content-Type': 'application/json',
            },
            timeout: 1000,
          });
        } catch (refreshError: any) {
          if (refreshError.response?.status === 429) {
            console.warn('429 on refresh token. Retrying after delay...');
            const delay =
              parseInt(refreshError.response.headers['retry-after']) || 3;
            await new Promise(resolve => setTimeout(resolve, delay * 1000));

            try {
              console.log(
                'Retrying token refresh after rate limit cooldown...',
              );
              refreshResponse = await axios.get(refreshEndpoint, {
                headers: {
                  Authorization: `Bearer ${refreshToken}`,
                  'Content-Type': 'application/json',
                },
                timeout: 10000, // 10 seconds timeout
              });
              console.log('Retry succeeded');
            } catch (finalRefreshError: any) {
              console.error(
                'Final refresh attempt failed:',
                finalRefreshError.response?.status || finalRefreshError.message,
              );
              return handleLogoutAndRedirect(
                '1',
                `Rate limit retry failed: ${
                  finalRefreshError.message || 'Unknown error'
                }`,
              );
            }
          } else if (refreshError.response?.status === 401) {
            console.error(
              'Refresh token rejected (401 Unauthorized). Token may be expired or invalid.',
            );
            return handleLogoutAndRedirect(
              '2',
              'Refresh token rejected (401 Unauthorized)',
            );
          } else {
            console.error(
              'Refresh failed:',
              refreshError.message || refreshError,
            );
            return handleLogoutAndRedirect(
              '3',
              `Refresh failed: ${refreshError.message || 'Unknown error'}`,
            );
          }
        }

        const {accessToken, refreshToken: newRefreshToken} =
          refreshResponse.data.data;

        await AsyncStorage.setItem('accessToken', accessToken);
        await AsyncStorage.setItem('refreshToken', newRefreshToken);

        originalRequest.headers.Authorization = `Bearer ${accessToken}`;
        return axiosInstance(originalRequest);
      } catch (err: any) {
        return handleLogoutAndRedirect(
          '4',
          `Token refresh process error: ${err?.message || 'Unknown error'}`,
        );
      }
    }

    // Handle repeated failures after retry attempts
    if (
      error.config &&
      error.config['axios-retry'] &&
      error.config['axios-retry'].retryCount >= 3
    ) {
      console.error('All retries failed:', error);

      if (!error.response || error.message?.includes('Network Error')) {
        console.warn('Network error occurred');
      } else {
        console.warn('Unexpected error. Redirecting to fallback screen');
        replace('Fallback');
      }
    }

    // Log and return 429s as-is (except refresh handled above)
    if (error.response?.status === 429) {
      console.warn('429 Too Many Requests. Not retrying.');
    }

    // Special handling for network errors
    if (error.message === 'Network Error') {
      console.warn('Network connectivity issue detected');

      // Return a more user-friendly error
      return Promise.reject({
        ...error,
        userFriendly: true,
        message:
          'Cannot connect to server. Please check your internet connection.',
      });
    }

    return Promise.reject(error);
  },
);

export default axiosInstance;
