import Api from './Api';

export interface Policy {
  id: string;
  name: string;
  link: string;
  description?: string;
  sortOrder?: number;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface PolicyResponse {
  data: Policy[];
  error: null | string;
  errorDetails: string;
}

export default {
  getPolicies() {
    return Api.get<PolicyResponse>('policies');
  },
};
