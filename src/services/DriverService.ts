import Api from './Api';

export default {
  login(credentials: {phone: string; whatsappPermission?: boolean}) {
    return Api.post(`auth/login/driver`, credentials);
  },
  verifyOtp(phone: string, otp: string) {
    return Api.post(`auth/verify/driver`, {phone, otp});
  },
  update(driver: any) {
    return Api.patch(`driver/me`, driver);
  },
  getDriver(os: string, currentVersion: string) {
    return Api.get(`driver/me`, {
      params: {
        os,
        currentVersion,
      },
    });
  },
  findDriver(phone: string) {
    return Api.post(`driver/check`, {phone});
  },
  deleteDriver(id: number) {
    return Api.delete(`driver/${id}`);
  },
  updateUpi(upiId: string) {
    return Api.put(`driver/payment`, {upi_id: upiId});
  },
  updateFcmToken(token: string) {
    return Api.post(`fcm-tokens/upsert`, {
      entityType: 'Driver',
      fcmToken: token,
    });
  },
  getVerificationStatus() {
    return Api.get(`driver-verification/status`);
  },
  startVerification() {
    return Api.post(`driver-verification/start_verification`);
  },
  getDriverConfig() {
    return Api.get(`driver-config`);
  },
  referral(phone: string) {
    return Api.post(`driver/validate-referral`, {phone});
  },
};
