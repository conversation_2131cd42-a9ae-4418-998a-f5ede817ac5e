package com.mapto.driver.modules

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.util.Log
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.modules.core.DeviceEventManagerModule
import com.mapto.driver.utils.MapToConstants.Companion.ACTION_LOCATION_STATE_CHANGED
import com.mapto.driver.utils.MapToConstants.Companion.EXTRA_GPS_STATE
import com.mapto.driver.utils.MapToConstants.Companion.TAG

class LocationStateModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    private var localReceiver: BroadcastReceiver? = null

    init {
        setupLocalReceiver()
    }

    override fun getName() = "LocationStateModule"

    @ReactMethod
    fun getCurrentState(promise: Promise) {
        val prefs = reactApplicationContext.getSharedPreferences("LocationState", Context.MODE_PRIVATE)
        val isGpsEnabled = prefs.getBoolean("isGpsEnabled", false)
        promise.resolve(isGpsEnabled)
    }

    private fun setupLocalReceiver() {
        val filter = IntentFilter(ACTION_LOCATION_STATE_CHANGED)

        localReceiver = object : BroadcastReceiver() {
            var lastExecutionTime: Long = 0
            override fun onReceive(context: Context?, intent: Intent?) {
                Log.d(TAG, "Local broadcast received")
                val isGpsEnabled = intent?.getBooleanExtra(EXTRA_GPS_STATE, false) ?: false
                val currentTime = System.currentTimeMillis()
                Log.d(TAG, "Should we emit ?  ${((currentTime - lastExecutionTime) > 2000)}, " +
                        "last time = $lastExecutionTime")
                if ((currentTime - lastExecutionTime) > 2000) {
                    lastExecutionTime = currentTime
                    Log.d(TAG, "Emitting event to RN, GPS state: $isGpsEnabled")
                    sendEvent(isGpsEnabled)
                }
            }
        }

        // Register with LocalBroadcastManager
        LocalBroadcastManager.getInstance(reactApplicationContext).registerReceiver(localReceiver!!, filter)
        Log.d(TAG, "Local receiver registered")
    }

    private fun sendEvent(value: Boolean) {
        reactApplicationContext
            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
            .emit("locationStateChanged", value)
    }

    override fun getConstants(): Map<String, Any> {
        return hashMapOf("LOCATION_STATE_EVENT" to "locationStateChanged")
    }

    override fun invalidate() {
        super.invalidate()
        localReceiver?.let {
            LocalBroadcastManager.getInstance(reactApplicationContext).unregisterReceiver(it)
            localReceiver = null
            localReceiver = null
        }
    }
}