package com.mapto.driver.service

import android.Manifest
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.content.pm.PackageManager
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.annotation.RequiresPermission
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.content.edit
import com.mapto.driver.R
import com.mapto.driver.utils.MapToConstants.Companion.APP_NAME
import com.mapto.driver.utils.MapToConstants.Companion.LOCATION_NOTIFICATION_ID
import com.mapto.driver.utils.MapToConstants.Companion.TAG

class LocationService : Service(), LocationListener {
    private var locationManager: LocationManager? = null
    private var locationCallback: MyLocationCallback? = null
    private var locationUpdatesEnabled = false
    private var binder: IBinder = LocalBinder()

    inner class LocalBinder : Binder() {
        fun getService(): LocationService? {
            return this@LocationService
        }
    }

    override fun onBind(intent: Intent?): IBinder {
        return binder
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        getSharedPreferences("service_prefs", MODE_PRIVATE).edit {
            putBoolean("isServiceRunning", true)
        }
        Log.d(TAG, "In onCreate of LocationService class")
        if (isFineLocationPermissionGiven() || isCourseLocationPermissionGiven()) {
            startForeground(LOCATION_NOTIFICATION_ID, createNotification())

            locationManager = getSystemService(LOCATION_SERVICE) as? LocationManager
            if (ActivityCompat.checkSelfPermission(
                    this,
                    Manifest.permission.ACCESS_FINE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(
                    this,
                    Manifest.permission.ACCESS_COARSE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                return
            }
            requestLocationUpdates()
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return START_STICKY
    }

    override fun stopService(name: Intent?): Boolean {
        Log.d(TAG, "In stopService of service class")
        stopLocationUpdates()
        return super.stopService(name)
    }

    override fun onDestroy() {
        Log.d(TAG, "In onDestroy")
        super.onDestroy()
        stopLocationUpdates()
    }

    fun setLocationCallback(callback: MyLocationCallback) {
        Log.d(TAG, "Setting location callback")
        this.locationCallback = callback
        if (ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_COARSE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return
        }
        requestLocationUpdates()
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channelId = getString(R.string.notification_channel_id)
            val channelName = getString(R.string.notification_channel_name)
            val channelDescription = getString(R.string.notification_channel_description)
            val importance = NotificationManager.IMPORTANCE_HIGH

            val channel = NotificationChannel(channelId, channelName, importance)
            channel.description = channelDescription
            val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        val notificationBuilder = NotificationCompat.Builder(
            this,
            getString(R.string.notification_channel_id)
        )
            .setContentTitle("$APP_NAME location service is active")
            .setContentText("$APP_NAME is fetching location...")
            .setSmallIcon(R.drawable.ic_location)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setOngoing(true)
        return notificationBuilder.build()
    }

    private fun cancelNotification() {
        Log.d(TAG, "cancelNotification: Cancelling notification")
        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.cancelAll()
    }


    @RequiresPermission(
        anyOf = [Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION]
    )
    private fun requestLocationUpdates() {
        Log.d(TAG, "In requestLocationUpdates of LocationService class")
        Log.d(TAG, "LocationManager = ${locationManager?.getProviders(true)}")
        if (locationManager == null) return

        val interval = getSharedPreferences("service_prefs", MODE_PRIVATE)
            .getLong("locationFetchInterval", 10000)

        val fineLocationGranted = isFineLocationPermissionGiven()
        val courseLocationGranted = isCourseLocationPermissionGiven()

        Log.d(
            TAG, "LocationService class interval = $interval, " +
                    "fine = $fineLocationGranted, course = $courseLocationGranted"
        )

        if (!fineLocationGranted && !courseLocationGranted) {
            // Request permission if not granted
            return
        }

        Log.d(TAG, "In requestLocationUpdates location update enabled =  $locationUpdatesEnabled")
        //if (!locationUpdatesEnabled) {
        if (fineLocationGranted) {
            Log.d(TAG, "In requestLocationUpdates fine location request")
            locationManager?.requestLocationUpdates(
                LocationManager.GPS_PROVIDER,
                interval,
                0f,
                this // Use the LocationListener (this class)
            )
            locationUpdatesEnabled = true
        } else if (courseLocationGranted) {
            Log.d(TAG, "In requestLocationUpdates course location request")
            locationManager?.requestLocationUpdates(
                LocationManager.NETWORK_PROVIDER,
                interval,
                5f,
                this // Use the LocationListener (this class)
            )
            locationUpdatesEnabled = true
        }
        //}
    }

    private fun stopLocationUpdates() {
        Log.d(TAG, "In stopLocationUpdates locationManager =  $locationManager")
        if (locationManager == null) return

        getSharedPreferences("service_prefs", MODE_PRIVATE).edit {
            putBoolean("isServiceRunning", false)
        }

        if (locationUpdatesEnabled) {
            locationManager?.removeUpdates(this) // Remove the LocationListener
            locationUpdatesEnabled = false
            Log.d(TAG, "In stopLocationUpdates Stopped location service")
        }
        cancelNotification()
    }

    private fun isFineLocationPermissionGiven(): Boolean {
        return ActivityCompat.checkSelfPermission(
            this,
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED

    }

    private fun isCourseLocationPermissionGiven(): Boolean {
        return ActivityCompat.checkSelfPermission(
            this,
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED

    }

    override fun onLocationChanged(location: Location) {
        // Handle the location update (e.g., send it to a server, update UI, etc.)
        // location.latitude, location.longitude, etc.
        if (locationUpdatesEnabled)
            Log.d(
                TAG,
                "On location change triggered --> lat = ${location.latitude} lon = ${location.longitude}"
            )

        Log.d(TAG, "Location call back is ${locationCallback != null}")

        try {
            locationCallback?.onLocationUpdate(location.latitude, location.longitude)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onProviderEnabled(provider: String) {}

    override fun onProviderDisabled(provider: String) {}
}