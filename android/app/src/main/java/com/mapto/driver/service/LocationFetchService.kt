package com.mapto.driver.service

import android.Manifest
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.location.Location
import android.os.Binder
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import androidx.annotation.RequiresPermission
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationAvailability
import com.google.android.gms.location.LocationCallback
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationResult
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.Priority
import com.mapto.driver.R
import com.mapto.driver.utils.MapToConstants.Companion.APP_NAME
import com.mapto.driver.utils.MapToConstants.Companion.FASTEST_UPDATE_INTERVAL_IN_MILLISECONDS
import com.mapto.driver.utils.MapToConstants.Companion.LOCATION_NOTIFICATION_CHANNEL_ID
import com.mapto.driver.utils.MapToConstants.Companion.LOCATION_NOTIFICATION_ID
import com.mapto.driver.utils.MapToConstants.Companion.TAG
import com.mapto.driver.utils.MapToConstants.Companion.UPDATE_INTERVAL_IN_MILLISECONDS
import com.mapto.driver.utils.MapToConstants.Companion.isEnded
import com.mapto.driver.utils.MapToConstants.Companion.mRequestingLocationUpdates
import com.mapto.driver.utils.Utility

class LocationFetchService : Service() {

    private lateinit var mFusedLocationClient: FusedLocationProviderClient
    private lateinit var mLocationRequest: LocationRequest
    private lateinit var mLocationCallback: LocationCallback
    private lateinit var mHandler: Handler
    private var myLocationCallback: MyLocationCallback? = null

    private var latitude: Double = 0.0
    private var longitude: Double = 0.0
    private val results1 = FloatArray(1)
    // private var distanceInMeters: Float = 0f // This was calculated but not obviously used elsewhere, uncomment if needed

    private var binder: IBinder = LocalBinder()

    inner class LocalBinder : Binder() {
        fun getService(): LocationFetchService? {
            return this@LocationFetchService
        }
    }

    override fun onBind(intent: Intent?): IBinder {
        Log.d(TAG, "Service onBind")
        startLocationUpdatesIfNotAlreadyRunning() // Ensure updates start when bound if not already
        return binder // Return the IBinder
    }

    override fun onUnbind(intent: Intent?): Boolean {
        Log.d(TAG, "Service onUnbind")
        return super.onUnbind(intent) // Allow rebinding
    }

    @RequiresPermission(Manifest.permission.SCHEDULE_EXACT_ALARM)
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "ON CREATE WAS HIT")

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                LOCATION_NOTIFICATION_CHANNEL_ID,
                "Location Service Channel",
                NotificationManager.IMPORTANCE_DEFAULT
            )
            val manager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            manager.createNotificationChannel(channel)
        }

        val notification: Notification = NotificationCompat.Builder(this, LOCATION_NOTIFICATION_CHANNEL_ID)
            .setOngoing(true)
            .setContentTitle("$APP_NAME location Service Active")
            .setSmallIcon(R.drawable.ic_location) // Ensure this drawable exists
            .setContentText("$APP_NAME is fetching your location for finding nearby ride requests")
            .build()
        startForeground(LOCATION_NOTIFICATION_ID, notification)

        mHandler = Handler(Looper.getMainLooper())
        mFusedLocationClient = LocationServices.getFusedLocationProviderClient(this)
        createLocationRequest()
        createLocationCallback()

        // Call your periodic task scheduling if needed and permissions allow
        if (ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.SCHEDULE_EXACT_ALARM
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            sendLocationDataToServerPeriodically()
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "ON START COMMAND WAS HIT")
        isEnded = false
        startLocationUpdatesIfNotAlreadyRunning()
        return START_STICKY
    }

    override fun onDestroy() {
        Log.d(TAG, "ON DESTROY WAS HIT")
        mHandler.removeCallbacksAndMessages(null)
        stopLocationUpdates()
        myLocationCallback = null
        super.onDestroy()
    }

    fun setLocationCallback(callback: MyLocationCallback?) {
        Log.d(TAG, "Setting location callback")
        this.myLocationCallback = callback
        startLocationUpdatesIfNotAlreadyRunning()
    }

    @RequiresPermission(Manifest.permission.SCHEDULE_EXACT_ALARM)
    private fun sendLocationDataToServerPeriodically() {
        Log.d(TAG, "Setting up periodic data send (not fully implemented in example)")

    }

    private fun createLocationRequest() {
        mLocationRequest = LocationRequest.Builder(
            Priority.PRIORITY_HIGH_ACCURACY,
            UPDATE_INTERVAL_IN_MILLISECONDS
        ).apply {
            setMinUpdateIntervalMillis(FASTEST_UPDATE_INTERVAL_IN_MILLISECONDS)
            setMinUpdateDistanceMeters(5.0f)
        }.build()
    }

    private fun createLocationCallback() {
        mLocationCallback = object : LocationCallback() {
            override fun onLocationResult(locationResult: LocationResult) {
                super.onLocationResult(locationResult)
                locationResult.locations.forEach { location ->
                    location?.let {
                        Log.i(TAG, "Location Changed: $it")

                        updateUI(it)
                    }
                }
            }

            override fun onLocationAvailability(locationAvailability: LocationAvailability) {
                super.onLocationAvailability(locationAvailability)
                Log.d(TAG, "Location Availability: ${locationAvailability.isLocationAvailable}")
                if (!locationAvailability.isLocationAvailable && mRequestingLocationUpdates) {
                    // Consider logging or attempting to restart updates if appropriate
                    Log.d(TAG, "Location service is not available")
                }
            }
        }
    }

    private fun updateUI(currentLocation: Location) {
        mHandler.post {
            val batteryPercent =
                Utility.getInstance().getBatteryPercentage(this@LocationFetchService)
            if (latitude != 0.0 && longitude != 0.0) {
                Location.distanceBetween(
                    latitude,
                    longitude,
                    currentLocation.latitude,
                    currentLocation.longitude,
                    results1
                )
                // distanceInMeters = results1[0] // Uncomment if you need to use this value
            }
            latitude = currentLocation.latitude
            longitude = currentLocation.longitude


            Log.d(
                TAG,
                "Location updated: Lat=$latitude, Lon=$longitude, Battery=$batteryPercent%"
            )
            myLocationCallback?.onLocationUpdate(latitude, longitude)
        }
    }

    private fun startLocationUpdatesIfNotAlreadyRunning() {
        if (!mRequestingLocationUpdates) {
            startLocationUpdates()
        }
    }

    private fun startLocationUpdates() {
        Log.d(TAG, "Attempting to start location updates.")
        if (ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED && ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_COARSE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            Log.w(TAG, "Location permission not granted. Cannot start updates.")
            stopSelf()
            return
        }

        try {
            mFusedLocationClient.requestLocationUpdates(
                mLocationRequest,
                mLocationCallback,
                Looper.getMainLooper()
            )
            mRequestingLocationUpdates = true
            isEnded = false
            Log.i(TAG, "Location updates started successfully.")
        } catch (e: SecurityException) {
            Log.e(TAG, "Lost location permission. Could not start updates.", e)
            mRequestingLocationUpdates = false
            stopSelf()
        }
    }

    private fun stopLocationUpdates() {
        if (mRequestingLocationUpdates) {
            Log.i(TAG, "Stopping location updates.")
            mFusedLocationClient.removeLocationUpdates(mLocationCallback)
            mRequestingLocationUpdates = false
            isEnded = true
        }
    }
}