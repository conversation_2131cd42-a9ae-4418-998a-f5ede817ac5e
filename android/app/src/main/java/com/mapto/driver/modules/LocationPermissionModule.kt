package com.mapto.driver.modules

import android.content.Intent
import android.content.IntentFilter
import android.util.Log
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.mapto.driver.service.PermissionChangeReceiver

class LocationPermissionModule(private val reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    private var permissionChangeReceiver: PermissionChangeReceiver? = null

    override fun getName(): String {
        return "LocationPermissionModule"
    }

    @ReactMethod
    fun startListening() {
        // Register the receiver
        Log.d("skt", "Registering broadcast receiver for location")
        permissionChangeReceiver = PermissionChangeReceiver(reactContext)
        val filter = IntentFilter(Intent.ACTION_PACKAGE_CHANGED).apply {
            addDataScheme("package")
        }
        reactContext.registerReceiver(permissionChangeReceiver, filter)
    }

    @ReactMethod
    fun stopListening() {
        // Unregister the receiver
        Log.d("skt", "Unregistering broadcast receiver for location")
        permissionChangeReceiver?.let {
            reactContext.unregisterReceiver(it)
            permissionChangeReceiver = null
        }
    }
}