package com.mapto.driver.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.util.Log
import androidx.core.content.ContextCompat
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.modules.core.DeviceEventManagerModule

class PermissionChangeReceiver(private val reactContext: ReactApplicationContext) : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent) {
        Log.d("skt", "Received inside broad cast receiver --> ${intent.action}")

        if (intent.action == Intent.ACTION_PACKAGE_CHANGED) {
            // Check if the location permission has changed
            val locationPermission = ContextCompat.checkSelfPermission(
                context,
                android.Manifest.permission.ACCESS_FINE_LOCATION
            )

            // Emit an event to React Native
            reactContext
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                .emit("onLocationPermissionChanged", locationPermission == PackageManager.PERMISSION_GRANTED)
        }
    }
}