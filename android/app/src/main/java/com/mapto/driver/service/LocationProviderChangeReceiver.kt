package com.mapto.driver.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.location.LocationManager
import android.util.Log
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.mapto.driver.utils.MapToConstants.Companion.ACTION_LOCATION_STATE_CHANGED
import com.mapto.driver.utils.MapToConstants.Companion.EXTRA_GPS_STATE
import com.mapto.driver.utils.MapToConstants.Companion.TAG
import androidx.core.content.edit

class LocationProviderChangeReceiver() : BroadcastReceiver() {

    override fun onReceive(context: Context?, intent: Intent?) {
        Log.d(TAG, "Received inside broad cast receiver --> ${intent?.action}")
        if (intent?.action == LocationManager.PROVIDERS_CHANGED_ACTION) {
            context?.let {
                Log.d(TAG, "Trying to broadcast the event to be received by another registered in module")
                val locationManager = it.getSystemService(Context.LOCATION_SERVICE) as LocationManager
                val isGpsEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)

                Log.d(TAG, "Broadcasting local intent, GPS state: $isGpsEnabled")

                // Store the state in SharedPreferences so the React Native module can access it
                val prefs = it.getSharedPreferences("service_prefs", Context.MODE_PRIVATE)
                prefs.edit { putBoolean("isGpsEnabled", isGpsEnabled) }

                // Optionally broadcast a local intent that the module can listen to
                val localIntent = Intent(ACTION_LOCATION_STATE_CHANGED).apply {
                    putExtra(EXTRA_GPS_STATE, isGpsEnabled)
                }
                localIntent.putExtra("isGpsEnabled", isGpsEnabled)
                LocalBroadcastManager.getInstance(it).sendBroadcast(localIntent)
                // context.sendBroadcast(localIntent)

            }
        }
    }
}