package com.mapto.driver.utils

import com.facebook.react.bridge.ReactContext

data class MapToConstants(val reactContext: ReactContext) {
    companion object {
        const val TAG = "skt"
        const val APP_NAME = "MapTo"

        const val LOCATION_PERMISSION_REQUEST_CODE = 1000
        const val RESTART_PENDING_INTENT_REQUEST_CODE = 1001
        const val BATTERY_OPTIMIZATION_REQUEST_CODE = 1002

        const val UPDATE_INTERVAL_IN_MILLISECONDS: Long = 1000 * 30 // 30 secs
        const val FASTEST_UPDATE_INTERVAL_IN_MILLISECONDS: Long =
            UPDATE_INTERVAL_IN_MILLISECONDS / 2
        const val HORIZONTAL_ACCURACY_IN_METERS: Int = 100

        const val LOCATION_NOTIFICATION_ID = 12300000
        const val FLOATING_NOTIFICATION_ID = 12300001

        //""FloatingWindowChannel" --> Keeping both channel names same to group notification
        const val LOCATION_NOTIFICATION_CHANNEL_ID = "com.locationfetch.FOREGROUND_CHANNEL"
        const val FLOATING_NOTIFICATION_CHANNEL_ID = "com.locationfetch.FOREGROUND_CHANNEL"
        const val ALARM_TRIGGER_SOURCE = "trigger_source"

        const val ACTION_RESTART_AND_SHOW_FLOATING_MODULE =
            "com.locationfetch.ACTION_RESTART_AND_SHOW_FLOATING_MODULE"
        const val ACTION_SHOW = "com.locationfetch.service.ACTION_SHOW"
        const val ACTION_HIDE = "com.locationfetch.service.ACTION_HIDE"
        const val ACTION_OPEN_APP =
            "com.locationfetch.service.ACTION_OPEN_APP" // For notification click
        const val ACTION_LOCATION_STATE_CHANGED = "com.locationfetch.LOCATION_STATE_CHANGED"

        const val EXTRA_GPS_STATE = "gps_state"

        const val SIGNAL_STRENGTH_UNKNOWN = "Unknown"
        const val SIGNAL_STRENGTH_NONE_OR_WEAK = "Weak"
        const val SIGNAL_STRENGTH_MODERATE = "Moderate"
        const val SIGNAL_STRENGTH_GOOD = "Good"
        const val SIGNAL_STRENGTH_GREAT = "Great"

        var mRequestingLocationUpdates: Boolean = false
        var isEnded: Boolean = false // Manage state as needed
    }
}

