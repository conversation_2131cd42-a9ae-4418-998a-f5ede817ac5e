package com.mapto.driver.service

import android.content.Intent
import android.util.Log
import com.facebook.react.HeadlessJsTaskService
import com.facebook.react.bridge.Arguments
import com.facebook.react.jstasks.HeadlessJsTaskConfig

class FloatingHeadlessTaskService : HeadlessJsTaskService() {
    override fun getTaskConfig(intent: Intent?): HeadlessJsTaskConfig? {
        val extras = intent?.extras
        if (extras != null) {
            Log.d("skt", "Came inside FloatingHeadlessTaskService. Inside getTaskConfig")
            return HeadlessJsTaskConfig(
                "ShowFloatingWindowTaskName", // Must match the name registered in JS
                Arguments.fromBundle(extras),
                5000,
                true
            )
        }
        return null
    }
}