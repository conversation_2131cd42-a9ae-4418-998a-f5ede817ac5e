package com.mapto.driver.modules

import android.Manifest
import android.app.AlarmManager
import android.content.ActivityNotFoundException
import android.content.ComponentName
import android.content.Context
import android.content.Context.MODE_PRIVATE
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.os.Build
import android.os.IBinder
import android.provider.Settings
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.edit
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.modules.core.DeviceEventManagerModule
import com.mapto.driver.service.EnhancedService
import com.mapto.driver.service.LocationFetchService
import com.mapto.driver.service.LocationService
import com.mapto.driver.service.MyLocationCallback

@Suppress("unused")
class LocationFetchTriggerModule(private val reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(reactContext) {

    private var isLocationBound = false
    private var isLocationFetchBound = false

    private var locationService: LocationService? = null

    private var locationFetchServiceIntent: Intent? = null
    private var enhancedServiceIntent: Intent? = null
    private var locationServiceIntent: Intent? =
        null // = Intent(reactApplicationContext, LocationService::class.java)

    companion object {
        private const val TAG = "skt"
        const val LOCATION_PERMISSION_REQUEST_CODE = 100
    }

    private var locationFetchService: LocationFetchService? = null

    private val locationServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName, service: IBinder) {
            Log.d(TAG, "Called on service connected method")
            val binder = service as LocationService.LocalBinder
            locationService = binder.getService()
            Log.d(TAG, "locationService is null --> ${locationService == null}")
            locationService?.setLocationCallback(object : MyLocationCallback {
                override fun onLocationUpdate(latitude: Double, longitude: Double) {
                    sendLocationEvent(latitude, longitude)
                }

            })
            isLocationBound = true
            reactContext.getSharedPreferences("service_prefs", MODE_PRIVATE).edit {
                putBoolean("isServiceRunning", isLocationBound)
            }

        }

        override fun onNullBinding(name: ComponentName?) {
            Log.d(TAG, "On binding null")
            super.onNullBinding(name)
        }

        override fun onBindingDied(name: ComponentName?) {
            Log.d(TAG, "On binding died")
            super.onBindingDied(name)
        }

        override fun onServiceDisconnected(name: ComponentName) {
            Log.d(TAG, "On Service disconnected")
            isLocationBound = false
            reactContext.getSharedPreferences("service_prefs", MODE_PRIVATE).edit {
                putBoolean("isServiceRunning", isLocationBound)
            }
        }
    }

    private val locationFetchServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName, service: IBinder) {
            Log.d(TAG, "Called on service connected method")
            val binder = service as LocationFetchService.LocalBinder
            locationFetchService = binder.getService()
            isLocationFetchBound = true

            // Register the callback to receive location updates
            locationFetchService?.setLocationCallback(object : MyLocationCallback {
                override fun onLocationUpdate(latitude: Double, longitude: Double) {
                    sendLocationEvent(latitude, longitude)
                }
            })
            isLocationFetchBound = true
            reactContext.getSharedPreferences("service_prefs", MODE_PRIVATE).edit {
                putBoolean("isFetchServiceRunning", isLocationFetchBound)
            }

        }

        override fun onNullBinding(name: ComponentName?) {
            Log.d(TAG, "On binding null")
            super.onNullBinding(name)
        }

        override fun onBindingDied(name: ComponentName?) {
            Log.d(TAG, "On binding died")
            super.onBindingDied(name)
        }

        override fun onServiceDisconnected(name: ComponentName) {
            Log.d(TAG, "On Service disconnected")
            isLocationFetchBound = false
            reactContext.getSharedPreferences("service_prefs", MODE_PRIVATE).edit {
                putBoolean("isFetchServiceRunning", isLocationFetchBound)
            }
        }
    }

    override fun getName(): String {
        return "LocationModule"
    }

    @ReactMethod
    fun startService(interval: Int?) {
        Log.d(TAG, "Called startService")
        val isServiceRunningFlag: Boolean = isServiceRunning(reactApplicationContext)
        val isPermissionsGiven = checkAndRequestPermissions()
        Log.d(
            TAG,
            "Permission flag --> $isPermissionsGiven , isServiceRunning --> $isServiceRunningFlag"
        )

        if (!isPermissionsGiven) {
            return
        }
        setLocationFetchInterval(interval, reactApplicationContext)

        Log.d(TAG, "Trying to register again ... Is already bound = $isLocationBound")
        try {
            if (isLocationBound) {
                reactApplicationContext.unbindService(locationServiceConnection)
                reactApplicationContext.unbindService(locationFetchServiceConnection)
            }
        } catch (e: IllegalArgumentException) {
            e.printStackTrace()
        }
        locationServiceIntent = Intent(reactApplicationContext, LocationService::class.java)
        reactApplicationContext.startService(locationServiceIntent)
        reactApplicationContext.bindService(
            locationServiceIntent!!,
            locationServiceConnection,
            ReactApplicationContext.BIND_AUTO_CREATE
        )

        locationFetchServiceIntent =
            Intent(reactApplicationContext, LocationFetchService::class.java)
        reactApplicationContext.startService(locationFetchServiceIntent)
        reactApplicationContext.bindService(
            locationFetchServiceIntent!!,
            locationFetchServiceConnection,
            ReactApplicationContext.BIND_AUTO_CREATE
        )

    }

    @ReactMethod
    fun startLocationService(
        interval: Int?
    ) { // interval is likely in seconds from JS
        Log.d(TAG, "startLocationService called with interval: $interval")

        if (!checkAndRequestPermissions()) {
            return
        }

        locationFetchServiceIntent = Intent(reactContext, LocationFetchService::class.java)
        if (isFetchServiceRunning(context = reactContext)) {
            return
        }

        try {
            ContextCompat.startForegroundService(reactContext, locationFetchServiceIntent!!)
            val didBind = reactContext.bindService(
                locationFetchServiceIntent!!, locationFetchServiceConnection,
                Context.BIND_AUTO_CREATE
            )
            if (didBind) {
                Log.d(TAG, "Service binding initiated successfully.")
            } else {
                Log.e(TAG, "Failed to initiate service binding.")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error starting or binding service: ${e.message}", e)
        }
    }

    @ReactMethod
    fun startEnhancedService(interval: Int?) {
        Log.d(TAG, "startEnhancedService called with interval: $interval")

        if (!checkAndRequestPermissions()) {
            return
        }

        enhancedServiceIntent = Intent(reactContext, EnhancedService::class.java)

        try {
            reactApplicationContext.startService(enhancedServiceIntent)
            reactApplicationContext.bindService(
                enhancedServiceIntent!!,
                locationServiceConnection,
                ReactApplicationContext.BIND_AUTO_CREATE
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error starting or binding service: ${e.message}", e)
        }
    }

    @ReactMethod
    fun stopEnhancedService(promise: Promise) {
        Log.d(TAG, "stopEnhancedService called")
        try {
            // Stop the service itself
            if (enhancedServiceIntent == null)
                enhancedServiceIntent = Intent(reactContext, EnhancedService::class.java)

            val didStop = reactContext.stopService(enhancedServiceIntent)
            Log.d(TAG, "EnhancedService stop attempted: $didStop")

            promise.resolve("EnhancedService service stopped.")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping service: ${e.message}", e)
            promise.reject("STOP_ERROR", "Could not stop location service: ${e.message}")
        }
    }

    @ReactMethod
    fun stopLocationService(promise: Promise) {
        Log.d(TAG, "stopLocationService called")
        try {
            if (isLocationBound) {
                locationFetchService?.setLocationCallback(null) // Important: Clear listener before unbinding
                reactContext.unbindService(locationFetchServiceConnection)
                isLocationBound = false
                locationService = null // Clear reference
                Log.d(TAG, "Service unbound.")
            }

            // Stop the service itself
            if (locationFetchService == null)
                locationFetchServiceIntent = Intent(reactContext, LocationFetchService::class.java)

            val didStop = reactContext.stopService(locationFetchServiceIntent)
            Log.d(TAG, "Service stop attempted: $didStop")

            promise.resolve("Location service stopped.")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping service: ${e.message}", e)
            promise.reject("STOP_ERROR", "Could not stop location service: ${e.message}")
        }
    }

    @ReactMethod
    fun stopService() {
        Log.d(TAG, "Called stopService")
        val isServiceRunningFlag: Boolean = isServiceRunning(reactApplicationContext)
        if (isServiceRunningFlag) {
            Log.d(TAG, "Going to try killing the service flag in pref is true")
            Log.d(TAG, "Going to unbind service flag --> $isLocationBound")
            if (isLocationBound) {
                reactApplicationContext.unbindService(locationServiceConnection)
                reactApplicationContext.unbindService(locationFetchServiceConnection)
                isLocationBound = false
            }

            if (locationServiceIntent != null)
                reactApplicationContext.stopService(locationServiceIntent)

            if (locationFetchServiceIntent != null)
                reactApplicationContext.stopService(locationFetchServiceIntent)
        }
    }

    fun sendLocationEvent(latitude: Double, longitude: Double) {
        Log.d(TAG, "Received lat long in call back --> lat= $latitude, long = $longitude")
        val params = Arguments.createMap()
        params.putDouble("latitude", latitude)
        params.putDouble("longitude", longitude)

        try {
            reactApplicationContext
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                .emit("locationUpdate", params)
        } catch (_: Exception) {
            // Handle any potential exceptions silently
        }
    }

    private fun isServiceRunning(context: Context): Boolean {
        Log.d(TAG, "in isServiceRunning")
        val prefs = context.getSharedPreferences("service_prefs", MODE_PRIVATE)
        val prefFlagValue = prefs.getBoolean("isServiceRunning", false)
        Log.d(TAG, "Flag value saved in prefs = $prefFlagValue")
        return prefFlagValue

    }

    private fun isFetchServiceRunning(context: Context): Boolean {
        Log.d(TAG, "in isFetchServiceRunning")
        val prefs = context.getSharedPreferences("service_prefs", MODE_PRIVATE)
        val prefFlagValue = prefs.getBoolean("isFetchServiceRunning", false)
        Log.d(TAG, "Flag value saved in prefs = $prefFlagValue")
        return prefFlagValue

    }

    private fun setLocationFetchInterval(interval: Int?, context: Context) {
        Log.d(TAG, "in isServiceRunning")
        val prefs = context.getSharedPreferences("service_prefs", MODE_PRIVATE)
        prefs.edit { putLong("locationFetchInterval", (interval?.toLong() ?: 10000L)) }
    }

    private fun checkAndRequestPermissions(): Boolean {
        // Check if FINE_LOCATION and FOREGROUND_SERVICE_LOCATION permissions are granted
        val fineLocationPermission = ContextCompat.checkSelfPermission(
            reactApplicationContext,
            Manifest.permission.ACCESS_FINE_LOCATION
        )
        val courseLocationPermission = ContextCompat.checkSelfPermission(
            reactApplicationContext,
            Manifest.permission.ACCESS_COARSE_LOCATION
        )
        val alarmManager = reactContext.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            Log.d(TAG, "Permission to give exact alarm is -> " + alarmManager.canScheduleExactAlarms())
            if (!alarmManager.canScheduleExactAlarms()) {
                Intent().apply {
                    action = Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM
                }.also {
                    try {
                        reactApplicationContext.startActivity(it)
                    } catch (e: ActivityNotFoundException) {
                        e.printStackTrace()
                    }
                }
            }
        }
        val foregroundServicePermission =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                ContextCompat.checkSelfPermission(
                    reactApplicationContext,
                    Manifest.permission.FOREGROUND_SERVICE_LOCATION
                )
            } else {
                PackageManager.PERMISSION_GRANTED
            }

        val permissionsToRequest = mutableListOf<String>()

        if (fineLocationPermission != PackageManager.PERMISSION_GRANTED) {
            permissionsToRequest.add(Manifest.permission.ACCESS_FINE_LOCATION)
        }

        if (courseLocationPermission != PackageManager.PERMISSION_GRANTED) {
            permissionsToRequest.add(Manifest.permission.ACCESS_COARSE_LOCATION)
        }

        if (foregroundServicePermission != PackageManager.PERMISSION_GRANTED
            && Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE
        ) {
            permissionsToRequest.add(Manifest.permission.FOREGROUND_SERVICE_LOCATION)
        }

        // Request permissions if not already granted
        if (permissionsToRequest.isNotEmpty()) {
            if (foregroundServicePermission == PackageManager.PERMISSION_GRANTED
                && courseLocationPermission == PackageManager.PERMISSION_GRANTED
            ) {
                Log.d(TAG, "This method returned true. But after check")
                return true
            } else {
                ActivityCompat.requestPermissions(
                    reactApplicationContext.currentActivity!!,
                    permissionsToRequest.toTypedArray(),
                    LOCATION_PERMISSION_REQUEST_CODE
                )
                return false
            }
        } else {
            Log.d(TAG, "This method returned true")
            return true
        }
    }

}