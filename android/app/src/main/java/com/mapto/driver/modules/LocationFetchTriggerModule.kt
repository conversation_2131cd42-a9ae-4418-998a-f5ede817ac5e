package com.mapto.driver.modules

import android.Manifest
import android.content.ComponentName
import android.content.Context
import android.content.Context.MODE_PRIVATE
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.modules.core.DeviceEventManagerModule
import com.mapto.driver.LocationCallback
import com.mapto.driver.service.LocationService
import com.mapto.driver.service.MyLocationCallback
import com.mapto.driver.utils.MapToConstants.Companion.LOCATION_PERMISSION_REQUEST_CODE
import com.mapto.driver.utils.MapToConstants.Companion.TAG

@Suppress("unused")
class LocationFetchTriggerModule(reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(reactContext) {
    private val LOCATION_PERMISSION_REQUEST_CODE = 100
    private var isBound = false
    private var locationService: LocationService? = null

    private var locationFetchServiceIntent: Intent? = null
    private var enhancedServiceIntent: Intent? = null
    private var locationServiceIntent: Intent? =
        null // = Intent(reactApplicationContext, LocationService::class.java)

    private var locationFetchService: LocationFetchService? = null

    private val locationServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName, service: IBinder) {
            Log.d("skt", "Called on service connected method")
            val binder = service as LocationService.LocalBinder
            locationService = binder.getService()
            Log.d("skt", "locationService is null --> ${locationService == null}")
            locationService?.setLocationCallback(object : LocationCallback {
                override fun onLocationUpdate(latitude: Double, longitude: Double) {
                    sendLocationEvent(latitude, longitude)
                }

            })
            isBound = true
            reactContext.getSharedPreferences("service_prefs", MODE_PRIVATE).edit()
                .putBoolean("isServiceRunning", isBound).apply()

        }

        override fun onNullBinding(name: ComponentName?) {
            Log.d("skt", "On binding null")
            super.onNullBinding(name)
        }

        override fun onBindingDied(name: ComponentName?) {
            Log.d("skt", "On binding died")
            super.onBindingDied(name)
        }

        override fun onServiceDisconnected(name: ComponentName) {
            Log.d("skt", "On Service disconnected")
            isBound = false
            reactContext.getSharedPreferences("service_prefs", MODE_PRIVATE).edit()
                .putBoolean("isServiceRunning", isBound).apply()
        }
    }

    override fun getName(): String {
        return "LocationModule"
    }

    @ReactMethod
    fun startService(interval: Integer?) {
        Log.d("skt", "Called startService")
        val isServiceRunningFlag: Boolean = isServiceRunning(reactApplicationContext)
        val isPermissionsGiven = checkAndRequestPermissions()
        setLocationFetchInterval(interval, reactApplicationContext)
        Log.d(
            "skt",
            "Permission flag --> $isPermissionsGiven , isServiceRunning --> $isServiceRunningFlag"
        )
        if (isPermissionsGiven) {
            Log.d("skt", "Trying to register again")
            try {
                if (isBound) {
                    reactApplicationContext.unbindService(serviceConnection)
                }
            } catch (e: IllegalArgumentException) {
                e.printStackTrace()
            }
            var intent: Intent = Intent(reactApplicationContext, LocationService::class.java)
            reactApplicationContext.startService(intent)
            reactApplicationContext.bindService(
                intent,
                serviceConnection,
                ReactApplicationContext.BIND_AUTO_CREATE
            )
        }
    }

    @ReactMethod
    fun stopService() {
        Log.d("skt", "Called stopService")
        val isServiceRunningFlag: Boolean = isServiceRunning(reactApplicationContext)
        if (isServiceRunningFlag) {
            Log.d("skt", "Going to try killing the service flag in pref is true")
            Log.d("skt", "Going to unbind service flag --> $isBound")
            if (isBound) {
                reactApplicationContext.unbindService(serviceConnection)
                isBound = false
            }
            reactApplicationContext.stopService(
                Intent(
                    reactApplicationContext,
                    LocationService::class.java
                )
            )
        }
    }

    fun sendLocationEvent(latitude: Double, longitude: Double) {
        Log.d("skt", "Received lat long in call back --> lat= $latitude, long = $longitude")
        val params = Arguments.createMap()
        params.putDouble("latitude", latitude)
        params.putDouble("longitude", longitude)

        try {
            reactApplicationContext
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                .emit("locationUpdate", params)
        } catch (_: Exception) {
            // Handle any potential exceptions silently
        }
    }

    private fun isServiceRunning(context: Context): Boolean {
        Log.d("skt", "in isServiceRunning")
        val prefs = context.getSharedPreferences("service_prefs", Context.MODE_PRIVATE)
        val prefFlagValue = prefs.getBoolean("isServiceRunning", false)
        Log.d("skt", "Flag value saved in prefs = $prefFlagValue")
        return prefFlagValue

    }

    private fun setLocationFetchInterval(interval: Integer?, context: Context) {
        Log.d("skt", "in isServiceRunning")
        val prefs = context.getSharedPreferences("service_prefs", MODE_PRIVATE)
        prefs.edit().putLong("locationFetchInterval", 10000).apply()
    }

    private fun checkAndRequestPermissions(): Boolean {
        // Check if FINE_LOCATION and FOREGROUND_SERVICE_LOCATION permissions are granted
        val fineLocationPermission = ContextCompat.checkSelfPermission(
            reactApplicationContext,
            Manifest.permission.ACCESS_FINE_LOCATION
        )
        val courseLocationPermission = ContextCompat.checkSelfPermission(
            reactApplicationContext,
            Manifest.permission.ACCESS_COARSE_LOCATION
        )
        val foregroundServicePermission =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                ContextCompat.checkSelfPermission(
                    reactApplicationContext,
                    Manifest.permission.FOREGROUND_SERVICE_LOCATION
                )
            } else {
                PackageManager.PERMISSION_GRANTED
            }

        val permissionsToRequest = mutableListOf<String>()

        if (fineLocationPermission != PackageManager.PERMISSION_GRANTED) {
            permissionsToRequest.add(Manifest.permission.ACCESS_FINE_LOCATION)
        }

        if (courseLocationPermission != PackageManager.PERMISSION_GRANTED) {
            permissionsToRequest.add(Manifest.permission.ACCESS_COARSE_LOCATION)
        }

        if (foregroundServicePermission != PackageManager.PERMISSION_GRANTED
            && Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE
        ) {
            permissionsToRequest.add(Manifest.permission.FOREGROUND_SERVICE_LOCATION)
        }

        // Request permissions if not already granted
        if (permissionsToRequest.isNotEmpty()) {
            if (foregroundServicePermission == PackageManager.PERMISSION_GRANTED
                && courseLocationPermission == PackageManager.PERMISSION_GRANTED
            ) {
                return true
            } else {
                ActivityCompat.requestPermissions(
                    reactApplicationContext.currentActivity!!,
                    permissionsToRequest.toTypedArray(),
                    LOCATION_PERMISSION_REQUEST_CODE
                )
                return false
            }
        } else {
            return true
        }
    }

}