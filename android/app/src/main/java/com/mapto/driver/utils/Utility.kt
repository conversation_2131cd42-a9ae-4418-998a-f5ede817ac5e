package com.mapto.driver.utils

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.wifi.WifiManager
import android.os.BatteryManager
import android.os.Build
import android.telephony.SignalStrength
import android.telephony.TelephonyManager
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.getSystemService

class Utility private constructor() { // Private constructor to prevent external instantiation

    fun getNetworkSignalStrength(context: Context): String {
        val connectivityManager =
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        val network = connectivityManager.activeNetwork
        val capabilities = connectivityManager.getNetworkCapabilities(network)

        if (capabilities != null) {
            when {
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> {
                    return getWifiSignalStrength(context)
                }

                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                    return getMobileSignalStrength(context)
                }
            }
        }
        return SIGNAL_STRENGTH_UNKNOWN
    }

    private fun getWifiSignalStrength(context: Context): String {
        val wifiManager =
            context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager

        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.ACCESS_WIFI_STATE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return SIGNAL_STRENGTH_UNKNOWN
        }


        if (!wifiManager.isWifiEnabled) {
            return SIGNAL_STRENGTH_UNKNOWN // Or a specific "Wi-Fi disabled" status
        }

        val connectionInfo = wifiManager.connectionInfo


        if (connectionInfo == null || connectionInfo.bssid == null || connectionInfo.networkId == -1) {
            return SIGNAL_STRENGTH_UNKNOWN // Not connected to a Wi-Fi AP
        }

        val rssi = connectionInfo.rssi

        // Standard RSSI values for Wi-Fi range roughly from -30 (excellent) to -90 (poor).
        // A value like -127 (WifiManager.MIN_RSSI) or if connectionInfo.bssid is null often indicates an issue or no valid signal.
        // The checks above for bssid and networkId should largely cover invalid states.

        return when {
            rssi >= -55 -> SIGNAL_STRENGTH_GREAT
            rssi >= -70 -> SIGNAL_STRENGTH_GOOD
            rssi >= -85 -> SIGNAL_STRENGTH_MODERATE
            // If rssi is very low (e.g., below -85 dBm), it's weak or effectively none.
            // We've already handled the "not connected" cases above.
            rssi < -85 -> SIGNAL_STRENGTH_NONE_OR_WEAK
            else -> SIGNAL_STRENGTH_UNKNOWN // Should ideally be covered by above, but as a fallback
        }
    }

    private fun getMobileSignalStrength(context: Context): String {
        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.ACCESS_FINE_LOCATION // Or COARSE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return SIGNAL_STRENGTH_UNKNOWN
        }

        val telephonyManager =
            context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager

        // Check if there is a SIM card present and ready
        if (telephonyManager.simState != TelephonyManager.SIM_STATE_READY) {
            return SIGNAL_STRENGTH_UNKNOWN // Or "No SIM" or "SIM not ready"
        }

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) { // Android 9 (Pie) and above
            val signalStrength: SignalStrength? = telephonyManager.signalStrength
            if (signalStrength == null) { // Check if signalStrength is null
                return SIGNAL_STRENGTH_UNKNOWN
            }
            val level = signalStrength.level // Level is from 0 (none/weak) to 4 (great)

            when (level) {
                0 -> SIGNAL_STRENGTH_NONE_OR_WEAK
                1 -> SIGNAL_STRENGTH_NONE_OR_WEAK
                2 -> SIGNAL_STRENGTH_MODERATE
                3 -> SIGNAL_STRENGTH_GOOD
                4 -> SIGNAL_STRENGTH_GREAT
                else -> SIGNAL_STRENGTH_UNKNOWN
            }
        } else {
            SIGNAL_STRENGTH_UNKNOWN
        }
    }

    fun getBatteryPercentage(context: Context): Int {
        val iFilter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)

        val batteryStatus: Intent = context.registerReceiver(null, iFilter)
            ?: return -1

        val level: Int = batteryStatus.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
        val scale: Int = batteryStatus.getIntExtra(BatteryManager.EXTRA_SCALE, -1)

        return if (level != -1 && scale != -1 && scale != 0) {
            (level * 100 / scale.toFloat()).toInt()
        } else {
            -1
        }
    }

    /**
     * Checks if the device has an active internet connection.
     * Requires ACCESS_NETWORK_STATE permission.
     *
     * @param context Context to access system services.
     * @return True if an internet connection is available, false otherwise.
     */
    fun isInternetAvailable(context: Context): Boolean {
        val connectivityManager =
            ContextCompat.getSystemService(context, ConnectivityManager::class.java)
                ?: return false // If service is not available, assume no connection

        val network = connectivityManager.activeNetwork ?: return false
        val networkCapabilities =
            connectivityManager.getNetworkCapabilities(network) ?: return false

        return networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
    }

    companion object {
        @Volatile
        private var INSTANCE: Utility? = null

        fun getInstance(): Utility {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: Utility().also { INSTANCE = it }
            }
        }

        const val SIGNAL_STRENGTH_UNKNOWN = "Unknown"
        const val SIGNAL_STRENGTH_NONE_OR_WEAK = "Weak"
        const val SIGNAL_STRENGTH_MODERATE = "Moderate"
        const val SIGNAL_STRENGTH_GOOD = "Good"
        const val SIGNAL_STRENGTH_GREAT = "Great"
    }
}