package com.mapto.driver.receiver

import android.app.ActivityManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import com.mapto.driver.service.FloatingHeadlessTaskService
import com.mapto.driver.service.LocationFetchService

class HeartbeatReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        val serviceIntent = Intent(context, FloatingHeadlessTaskService::class.java)
        context.startService(serviceIntent)

        if (!isServiceRunning(context, LocationFetchService::class.java)) {
            val locFetchServiceIntent = Intent(context, LocationFetchService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(locFetchServiceIntent)
            } else {
                context.startService(locFetchServiceIntent)
            }
        }
    }

    private fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE)
            .any { it.service.className == serviceClass.name }
    }
}