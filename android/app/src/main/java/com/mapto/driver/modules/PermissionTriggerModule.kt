package com.mapto.driver.modules

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import android.util.Log
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod

@Suppress("unused")
class PermissionTriggerModule(reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(reactContext) {

    private val reactContext: ReactApplicationContext = reactContext

    override fun getName(): String {
        return "PermissionModule"
    }

    @ReactMethod
    fun checkBatteryOptimization(promise: Promise) {
        val powerManager = reactContext.getSystemService(Context.POWER_SERVICE) as PowerManager
        val isIgnoringBatteryOptimizations =
            powerManager.isIgnoringBatteryOptimizations(reactContext.packageName)

        promise.resolve(isIgnoringBatteryOptimizations)
    }

    @ReactMethod
    fun askBatteryPermission(promise: Promise) {
        Log.d("skt", "Asking permission for battery")
        val currentActivity = currentActivity ?: run {
            promise.reject("E_ACTIVITY_DOES_NOT_EXIST", "Activity doesn't exist")
            return
        }

        // Check if already ignoring battery optimizations
        val powerManager = reactContext.getSystemService(Context.POWER_SERVICE) as PowerManager
        if (powerManager.isIgnoringBatteryOptimizations(reactContext.packageName)) {
            Log.d("skt", "Given permission")
            promise.resolve(true)
            return
        }

        val intent = openUpdatedBatteryIntentPage()

        try {
            // Launch the intent
            currentActivity.startActivityForResult(intent, BATTERY_OPTIMIZATION_REQUEST_CODE)

            // Setup a promise to be resolved/rejected based on user action
            BatteryOptimizationPromiseHolder.promise = promise
        } catch (e: Exception) {
            // Fallback to settings if direct intent fails
            Log.d("skt", "Asking permission for battery in catch black ${e.message} ${e.cause}")
            e.printStackTrace()
            try {
                val fallbackIntent = Intent(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS)
                currentActivity.startActivityForResult(
                    fallbackIntent,
                    BATTERY_OPTIMIZATION_REQUEST_CODE
                )
                BatteryOptimizationPromiseHolder.promise = promise
            } catch (e: Exception) {
                promise.reject(
                    "E_PERMISSION_REQUEST_FAILED",
                    "Failed to open battery optimization settings"
                )
                Log.d("skt", "Asking permission for battery. Second catch block")
            }
        }
    }

    fun openUpdatedBatteryIntentPage(): Intent {
        var intent = Intent()
        Log.d("skt", "Manufacturer == ${Build.MANUFACTURER}")


        intent = Intent(
            "android.settings.BACKGROUND_BATTERY_OPTIMIZATION_SETTINGS",
            Uri.parse("package:${reactContext.packageName}")
        )
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)


        return intent
    }


    // Companion object to handle promise across activity result
    companion object {
        private const val BATTERY_OPTIMIZATION_REQUEST_CODE = 1001

        object BatteryOptimizationPromiseHolder {
            var promise: Promise? = null
        }
    }

    // Method to be called from your React Native Activity
    fun onActivityResult(requestCode: Int, resultCode: Int) {
        if (requestCode == BATTERY_OPTIMIZATION_REQUEST_CODE) {
            val powerManager = reactContext.getSystemService(Context.POWER_SERVICE) as PowerManager
            val isIgnoringBatteryOptimizations =
                powerManager.isIgnoringBatteryOptimizations(reactContext.packageName)
            Log.d(
                "skt",
                "Asking permission for battery in activity result -> $isIgnoringBatteryOptimizations"
            )

            BatteryOptimizationPromiseHolder.promise?.let { promise ->
                promise.resolve(isIgnoringBatteryOptimizations)
                BatteryOptimizationPromiseHolder.promise = null
            }
        }
    }
}