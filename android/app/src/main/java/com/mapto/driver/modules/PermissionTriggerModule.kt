package com.mapto.driver.modules

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import android.util.Log
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.mapto.driver.utils.MapToConstants.Companion.BATTERY_OPTIMIZATION_REQUEST_CODE
import com.mapto.driver.utils.MapToConstants.Companion.TAG

@Suppress("unused")
class PermissionTriggerModule(reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(reactContext) {

    private val reactContext: ReactApplicationContext = reactContext

    override fun getName(): String {
        return "PermissionModule"
    }

    @ReactMethod
    fun checkBatteryOptimization(promise: Promise) {
        val powerManager = reactContext.getSystemService(Context.POWER_SERVICE) as PowerManager
        val isIgnoringBatteryOptimizations =
            powerManager.isIgnoringBatteryOptimizations(reactContext.packageName)

        promise.resolve(isIgnoringBatteryOptimizations)
    }

    @ReactMethod
    fun askBatteryPermission(promise: Promise) {
        Log.d(TAG, "Asking permission for battery")
        val currentActivity = currentActivity ?: run {
            promise.reject("E_ACTIVITY_DOES_NOT_EXIST", "Activity doesn't exist")
            return
        }

        // Check if already ignoring battery optimizations
        val powerManager = reactContext.getSystemService(Context.POWER_SERVICE) as PowerManager
        if (powerManager.isIgnoringBatteryOptimizations(reactContext.packageName)) {
            Log.d(TAG, "Given permission")
            promise.resolve(true)
            return
        }

        // Create intent to request battery optimization permission
        //ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
        //ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS

//        val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
//            data = Uri.parse("package:${reactContext.packageName}")
//            Log.d(TAG, "Asking permission for battery. Opening specific details")
//        }

        val intent = openUpdatedBatteryIntentPage()

        try {
            // Launch the intent
            currentActivity.startActivityForResult(intent, BATTERY_OPTIMIZATION_REQUEST_CODE)

            // Setup a promise to be resolved/rejected based on user action
            BatteryOptimizationPromiseHolder.promise = promise
        } catch (e: Exception) {
            // Fallback to settings if direct intent fails
            Log.d(TAG, "Asking permission for battery in catch black ${e.message} ${e.cause}")
            e.printStackTrace()
            try {
                val fallbackIntent = Intent(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS)
                currentActivity.startActivityForResult(
                    fallbackIntent,
                    BATTERY_OPTIMIZATION_REQUEST_CODE
                )
                BatteryOptimizationPromiseHolder.promise = promise
            } catch (e: Exception) {
                promise.reject(
                    "E_PERMISSION_REQUEST_FAILED",
                    "Failed to open battery optimization settings"
                )
                Log.d(TAG, "Asking permission for battery. Second catch block")
            }
        }
    }

    fun openUpdatedBatteryIntentPage(): Intent {
        var intent = Intent()
        Log.d(TAG, "Manufacturer == ${Build.MANUFACTURER}")
        if (Build.MANUFACTURER == "samsung") {
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.N) {
                intent.component = ComponentName(
                    "com.samsung.android.lool",
                    "com.samsung.android.sm.ui.battery.BatteryActivity"
                )
            } else if (Build.VERSION.SDK_INT > Build.VERSION_CODES.LOLLIPOP) {
                intent.component = ComponentName(
                    "com.samsung.android.sm",
                    "com.samsung.android.sm.ui.battery.BatteryActivity"
                )
            }
        } else {
            intent = Intent(Settings.ACTION_BATTERY_SAVER_SETTINGS)
        }

//        intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
//            data = Uri.parse("package:${reactContext.packageName}")
//            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//        }
//
        intent = when (Build.MANUFACTURER.lowercase()) {
            "xiaomi", "redmi" -> miPhoneBatterySettings()
            "oppo" -> oppoBatterySettings()
            "vivo" -> vivoBatterySettings()
            "samsung" -> samsungBatterySettings()
            "huawei" -> huaweiBatterySettings()
            "oneplus" -> onePlusBatterySettings()
            "motorola" -> motorolaBatterySettings()
            else -> defaultBatterySettings()
        }

        intent = Intent(
            "android.settings.BACKGROUND_BATTERY_OPTIMIZATION_SETTINGS",
            Uri.parse("package:${reactContext.packageName}")
        )
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

//        intent.putExtra(
//            ":settings:show_fragment",
//            "com.android.settings.fuelgauge.PowerUsageDetail"
//        )

        return intent
    }

    private fun defaultBatterySettings(): Intent {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
        intent.data = Uri.parse("package:${reactContext.packageName}")
        return intent
    }

    private fun miPhoneBatterySettings(): Intent {
        Log.d(TAG, "Mi battery")
        return Intent().apply {
            component = ComponentName(
                "com.miui.powerkeeper",
                "com.miui.powerkeeper.ui.HiddenAppsContainerPage"
            )
            putExtra("package_name", reactContext.packageName)
        }
    }

    private fun oppoBatterySettings(): Intent {
        Log.d(TAG, "OPPO battery")
        return Intent().apply {
            component = ComponentName(
                "com.coloros.safecenter",
                "com.coloros.safecenter.permission.startup.StartupAppListActivity"
            )
        }
    }

    private fun vivoBatterySettings(): Intent {
        Log.d(TAG, "VIVO battery")
        return Intent().apply {
            component = ComponentName(
                "com.vivo.abe",
                "com.vivo.applicationbehaviorengine.ui.ExcessivePowerManagerActivity"
            )
            putExtra("packagename", reactContext.packageName)
        }
    }

    private fun samsungBatterySettings(): Intent {
        Log.d(TAG, "Samsung battery")
        val packageName = reactContext.packageName

        // Try multiple known Samsung battery settings paths
        val samsungIntents = listOf(
            // Intent 1: Direct app power saving settings
            Intent().apply {
                component = ComponentName(
                    "com.samsung.android.settings.devicecare",
                    "com.samsung.android.settings.devicecare.battery.AppPowerSavingModeDetailActivity"
                )
                putExtra("package_name", packageName)
            },

            // Intent 2: Battery optimization settings
            Intent().apply {
                action = Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS
                data = Uri.parse("package:$packageName")
            },

            // Intent 3: App-specific battery usage
            Intent().apply {
                component = ComponentName(
                    "com.android.settings",
                    "com.android.settings.fuelgauge.PowerUsageDetail"
                )
                putExtra("package", packageName)
            },

            // Intent 4: Device care settings
            Intent().apply {
                component = ComponentName(
                    "com.samsung.android.lool",
                    "com.samsung.android.lool.battery.ui.BatteryUsageActivity"
                )
            },

            // Intent 5: Default app details settings
            Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.parse("package:$packageName")
            }
        )

        // Try each intent until one works
        for (intent in samsungIntents) {
            try {
                if (intent.resolveActivity(reactContext.packageManager) != null) {
                    Log.d(TAG, "Samsung intent is ${intent.component?.shortClassName}")
                    return intent
                }
            } catch (e: Exception) {
                // Continue to next intent
                Log.d(TAG, "In catch of samsung ${e.cause}")
            }
        }

        // Fallback to default if no Samsung-specific intent works
        return defaultBatterySettings()
    }

    private fun huaweiBatterySettings(): Intent {
        Log.d(TAG, "Huawei battery")
        return Intent().apply {
            component = ComponentName(
                "com.huawei.systemmanager",
                "com.huawei.systemmanager.appcontrol.activity.AppControlDetailActivity"
            )
            putExtra("package_name", reactContext.packageName)
        }
    }

    private fun onePlusBatterySettings(): Intent {
        Log.d(TAG, "One Plus battery")
        return Intent().apply {
            component = ComponentName(
                "com.oneplus.security",
                "com.oneplus.security.chainlaunch.view.ChainLaunchAppListActivity"
            )
        }
    }

    private fun motorolaBatterySettings(): Intent {
        Log.d(TAG, "Moto battery")
        return Intent().apply {
            component = ComponentName(
                "com.motorola.protect",
                "com.motorola.protect.battery.BatteryOptimizationActivity"
            )
            putExtra("package", reactContext.packageName)
        }
    }

    // Companion object to handle promise across activity result
    companion object {
        object BatteryOptimizationPromiseHolder {
            var promise: Promise? = null
        }
    }

    // Method to be called from your React Native Activity
    fun onActivityResult(requestCode: Int, resultCode: Int) {
        if (requestCode == BATTERY_OPTIMIZATION_REQUEST_CODE) {
            val powerManager = reactContext.getSystemService(Context.POWER_SERVICE) as PowerManager
            val isIgnoringBatteryOptimizations =
                powerManager.isIgnoringBatteryOptimizations(reactContext.packageName)
            Log.d(
                TAG,
                "Asking permission for battery in activity result -> $isIgnoringBatteryOptimizations"
            )

            BatteryOptimizationPromiseHolder.promise?.let { promise ->
                promise.resolve(isIgnoringBatteryOptimizations)
                BatteryOptimizationPromiseHolder.promise = null
            }
        }
    }
}