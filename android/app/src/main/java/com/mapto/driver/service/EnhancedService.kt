package com.mapto.driver.service

import android.Manifest
import android.app.AlarmManager
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.ServiceInfo
import android.os.Build
import android.os.Bundle
import android.os.IBinder
import android.os.PowerManager
import androidx.annotation.RequiresPermission
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import com.mapto.driver.R
import com.mapto.driver.receiver.HeartbeatReceiver

class EnhancedService : Service() {

    companion object {
        const val ACTION_RESTART_AND_SHOW_FLOATING_MODULE =
            "com.locationfetch.ACTION_RESTART_AND_SHOW_FLOATING_MODULE"
        private const val RESTART_PENDING_INTENT_REQUEST_CODE = 1001
    }

    // Multiple survival strategies
    private lateinit var alarmManager: AlarmManager
    private lateinit var wakeLock: PowerManager.WakeLock
    private var restartPendingIntent: PendingIntent? = null

    @RequiresPermission(Manifest.permission.SCHEDULE_EXACT_ALARM)
    override fun onCreate() {
        super.onCreate()

        startEnhancedForegroundService()
        setupAlarmHeartbeat()
        setupWakeLocks()
        setupRestartMechanism()
    }

    private fun startEnhancedForegroundService() {
        val notificationManager =
            getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                LocationFetchService.CHANNEL_ID,
                "Location Service Channel",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Channel for the foreground location service."
            }
            notificationManager.createNotificationChannel(channel)
        }

        val notification: Notification =
            NotificationCompat.Builder(this, LocationFetchService.CHANNEL_ID)
                .setContentTitle("Location Service Active")
                .setContentText("Fetching your location for finding nearby ride requests.") // Be specific about why
                .setSmallIcon(R.drawable.ic_location) // Use a location-relevant icon
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setCategory(NotificationCompat.CATEGORY_SERVICE)
                .setOngoing(true)
                .setAutoCancel(false)
                .build()

        // --- Location Specific Changes ---
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val serviceType: Int =
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) { // Android 14+
                    if (ActivityCompat.checkSelfPermission(
                            this,
                            Manifest.permission.FOREGROUND_SERVICE_LOCATION
                        ) == PackageManager.PERMISSION_GRANTED
                    ) {
                        ServiceInfo.FOREGROUND_SERVICE_TYPE_LOCATION
                    } else {
                        stopSelf()
                        return
                    }
                } else
                    ServiceInfo.FOREGROUND_SERVICE_TYPE_LOCATION

            try {
                if (serviceType != 0 || Build.VERSION.SDK_INT < Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                    startForeground(
                        LocationFetchService.NOTIFICATION_ID,
                        notification,
                        serviceType
                    )
                } else {
                    stopSelf()
                    return
                }
            } catch (e: Exception) {
                e.printStackTrace()
                stopSelf()
            }
        } else {

            startForeground(LocationFetchService.NOTIFICATION_ID, notification)
        }
    }

    private fun setupAlarmHeartbeat() {
        alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager

        val heartbeatIntent = Intent(this, HeartbeatReceiver::class.java)
        val pendingIntent = PendingIntent.getBroadcast(
            this, 0, heartbeatIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val interval = 6 * 60 * 1000L // 6 minute
        alarmManager.setRepeating(
            AlarmManager.RTC_WAKEUP,
            System.currentTimeMillis() + interval,
            interval,
            pendingIntent
        )
    }

    private fun setupWakeLocks() {
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager

        // Primary wake lock
        wakeLock = powerManager.newWakeLock(
            PowerManager.PARTIAL_WAKE_LOCK,
            "skt::MainWakeLock"
        )
        wakeLock.acquire(5 * 60 * 1000L /*5 minutes*/)

        // CPU wake lock for critical operations
        val cpuWakeLock = powerManager.newWakeLock(
            PowerManager.PARTIAL_WAKE_LOCK,
            "skt::CPUWakeLock"
        )
        cpuWakeLock.acquire(10 * 60 * 1000L) // 10 minutes
    }

    @RequiresPermission(Manifest.permission.SCHEDULE_EXACT_ALARM)
    private fun setupRestartMechanism() {
        val restartIntent = Intent(this, this::class.java) // Get class of the current service
        restartIntent.action = ACTION_RESTART_AND_SHOW_FLOATING_MODULE

        val flags = PendingIntent.FLAG_UPDATE_CURRENT or
                PendingIntent.FLAG_IMMUTABLE
        restartPendingIntent = PendingIntent.getService(
            this,
            RESTART_PENDING_INTENT_REQUEST_CODE, // Use a unique request code
            restartIntent,
            flags
        )

        val restartTimeMillis =
            System.currentTimeMillis() + 5 * 60 * 1000L // 5 minutes from now (example)

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && !alarmManager.canScheduleExactAlarms()) {
                setupAlarmHeartbeat()
                return
            }

            alarmManager.setExactAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP,
                restartTimeMillis,
                restartPendingIntent!!
            )
        } catch (se: SecurityException) {
            se.printStackTrace()
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (intent?.action == ACTION_RESTART_AND_SHOW_FLOATING_MODULE) {
            val headlessServiceIntent = Intent(this, FloatingHeadlessTaskService::class.java)
            val bundle = Bundle()
            bundle.putString(LocationFetchService.ALARM_TRIGGER_SOURCE, "alarm_restart") // Example extra, can be anything
            headlessServiceIntent.putExtras(bundle)
            startService(headlessServiceIntent)
        }
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        super.onTaskRemoved(rootIntent)
        // Restart when app is swiped away
        val restartIntent = Intent(applicationContext, LocationFetchService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(restartIntent)
        }
    }
}