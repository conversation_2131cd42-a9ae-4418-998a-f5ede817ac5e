package com.mapto.driver.service

import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import androidx.core.app.NotificationCompat
import com.mapto.driver.R
import com.mapto.driver.utils.MapToConstants.Companion.ACTION_HIDE
import com.mapto.driver.utils.MapToConstants.Companion.ACTION_OPEN_APP
import com.mapto.driver.utils.MapToConstants.Companion.ACTION_SHOW
import com.mapto.driver.utils.MapToConstants.Companion.APP_NAME
import com.mapto.driver.utils.MapToConstants.Companion.FLOATING_NOTIFICATION_CHANNEL_ID
import com.mapto.driver.utils.MapToConstants.Companion.FLOATING_NOTIFICATION_ID
import com.mapto.driver.utils.MapToConstants.Companion.TAG
import kotlin.math.abs

class FloatingWindowService : Service() {
    private val mainThreadHandler = Handler(Looper.getMainLooper())
    private var windowManager: WindowManager? = null
    private var floatingView: View? = null
    private var params: WindowManager.LayoutParams? = null

    private var initialX: Int = 0
    private var initialY: Int = 0
    private var initialTouchX: Float = 0f
    private var initialTouchY: Float = 0f
    private var isMoving = false
    private var clickStartTime: Long = 0
    private val CLICK_TIME_THRESHOLD = 200 // milliseconds

    @Volatile
    private var isWindowActuallyAdded: Boolean = false

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onUnbind(intent: Intent?): Boolean {
        Log.d(TAG, "Service onUnbind")
        return super.onUnbind(intent) // Allow rebinding
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "ON CREATE WAS HIT FOR FLOATING WINDOW")
        if (windowManager == null)
            windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "Service onStartCommand, action: ${intent?.action}")
        val action = intent?.action
        if (action == null) {
            Log.w(TAG, "Action is null, stopping service to prevent timeout.")
            stopSelf() // Or handle appropriately
            return START_NOT_STICKY // CRITICAL: No startForeground() call here!
        }

        when (intent.action) {
            ACTION_SHOW -> {
                if (!Settings.canDrawOverlays(this)) {
                    Log.e(TAG, "Overlay permission not granted")
                    // Optionally send an event to React Native to request permission
                    stopSelf() // Stop if permission not available
                    return START_NOT_STICKY
                }
                try {
                    val notification = createNotification()
                    startForeground(FLOATING_NOTIFICATION_ID, notification)
                    Log.d(TAG, "startForeground() called successfully.")
                    show()
                } catch (e: Exception) {
                    Log.e(TAG, "Error during startForeground or showFloatingView", e)
                    stopSelf()
                    return START_NOT_STICKY
                }
            }

            ACTION_HIDE -> {
                try {
                    val notification = createNotification()
                    startForeground(FLOATING_NOTIFICATION_ID, notification)
                    Log.d(TAG, "startForeground() called successfully in hide.")
                    hide()
                    stopForeground(true)
                    stopSelf()
                } catch (e: Exception) {
                    Log.e(TAG, "Error during startForeground or showFloatingView in action hide", e)
                    stopSelf()
                    return START_NOT_STICKY
                }
            }

            ACTION_OPEN_APP -> {
                openApp()
            }

            else -> {
                Log.w(TAG, "Unknown action: $action. Stopping service.")
                stopSelf()
                return START_NOT_STICKY // CRITICAL: No startForeground() if action isn't handled for foreground
            }
        }
        return START_STICKY // Or START_NOT_STICKY if you don't want it to restart automatically
    }

    override fun onDestroy() {
        Log.d(TAG, "ON DESTROY WAS HIT")
        if (Looper.myLooper() == Looper.getMainLooper()) {
            removePreviousViewIfExists("onDestroy")
        } else {
            mainThreadHandler.post {
                removePreviousViewIfExists("onDestroy main thread handler")
            }
        }
        // windowManager = null
        super.onDestroy()
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                FLOATING_NOTIFICATION_CHANNEL_ID,
                "Floating UI Service",
                NotificationManager.IMPORTANCE_LOW // Use LOW to minimize intrusiveness
            )
            val manager = getSystemService(NotificationManager::class.java)
            manager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {

        val mainActivityIntent = packageManager.getLaunchIntentForPackage(packageName)?.apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        val contentTapIntent =
            mainActivityIntent ?: Intent(this, FloatingWindowService::class.java).apply {
                action = ACTION_OPEN_APP // Use the static ACTION_OPEN_APP
            }

        val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }

        val contentPendingIntent: PendingIntent = if (mainActivityIntent != null) {
            PendingIntent.getActivity(this, 0, contentTapIntent, pendingIntentFlags)
        } else {
            PendingIntent.getService(this, 0, contentTapIntent, pendingIntentFlags)
        }

        val hideFloatingUiIntent = Intent(this, FloatingWindowService::class.java).apply {
            action = ACTION_HIDE // Use the static ACTION_HIDE
        }
        val hideActionPendingIntent: PendingIntent = PendingIntent.getService(
            this,
            1,
            hideFloatingUiIntent,
            pendingIntentFlags
        )

        val notificationBuilder = NotificationCompat.Builder(this, FLOATING_NOTIFICATION_CHANNEL_ID)
            .setContentTitle("$APP_NAME is running")
            .setContentText("$APP_NAME is checking for ride requests")
            .setSmallIcon(R.drawable.ic_location)
            .setContentIntent(contentPendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
//            .addAction(
//                R.mipmap.ic_close_foreground,
//                "Hide",
//                hideActionPendingIntent
//            )

        return notificationBuilder.build()
    }

    @SuppressLint("InflateParams")
    private fun show() {
        if (Looper.myLooper() != Looper.getMainLooper()) {
            Log.d(TAG, "Looper is not main looper")
            mainThreadHandler.post {
                showFloatingView()
            }
        } else {
            Log.d(TAG, "Looper is main looper. (clap)")
            showFloatingView()
        }
    }

    @SuppressLint("InflateParams")
    private fun showFloatingView() {
        removePreviousViewIfExists("Show floating view start")
        if (floatingView != null || isWindowActuallyAdded) {
            Log.d(TAG, "Floating view already exists. Not creating a new one.")
            return
        }

        if (!Settings.canDrawOverlays(this)) {
            Log.e(TAG, "showInternal: Overlay permission not granted.")
            return
        }

        if (windowManager == null) {
            windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
            if (windowManager == null) {
                Log.e(TAG, "showInternal: WindowManager is null after attempting to get it.")
                return
            }
        }

        Log.d(TAG, "Showing Floating View")
        val inflater = getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater
        floatingView = inflater.inflate(R.layout.floating_layout, null)

        val layoutFlag: Int = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            WindowManager.LayoutParams.TYPE_PHONE // For older versions (requires SYSTEM_ALERT_WINDOW)
        }

        params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            layoutFlag,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                    or WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                    or WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL,
            PixelFormat.TRANSLUCENT
        )

        // Specify the initial position of the window
        params?.gravity = Gravity.TOP or Gravity.START // Or use Gravity.NO_GRAVITY for absolute x,y
        params?.x = 0 // Initial X position
        params?.y = 100 // Initial Y position

        try {
            if (floatingView == null) {
                Log.e(TAG, "FloatingView is null after inflation attempt.")
                return
            }
            if (floatingView?.windowToken == null) { // Check if it's not already added
                windowManager?.addView(floatingView, params)
                Log.d(TAG, "Floating view added to WindowManager.")
                isWindowActuallyAdded = true
            } else {
                Log.d(TAG, "Floating view already has a window token, likely already added.")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error adding floating view to WindowManager", e)
            // Clean up if addView fails
            removePreviousViewIfExists("show_internal_exception")
            return
        }

        val iconView = floatingView?.findViewById<ImageView>(R.id.img_floating_icon) // Example ID
        iconView?.let {
            setupTouchListener(it)
        } ?: run {
            floatingView?.let { setupTouchListener(it) }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setupTouchListener(viewToListen: View) {
        viewToListen.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    isMoving = false
                    clickStartTime = System.currentTimeMillis()
                    initialX = params?.x ?: 0
                    initialY = params?.y ?: 100
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    true
                }

                MotionEvent.ACTION_MOVE -> {
                    val deltaX = event.rawX - initialTouchX
                    val deltaY = event.rawY - initialTouchY

                    if (abs(deltaX) > 10 || abs(deltaY) > 10) { // Threshold to consider it a move
                        isMoving = true
                    }

                    if (isMoving) {
                        params?.x = initialX + deltaX.toInt()
                        params?.y = initialY + deltaY.toInt()
                        try {
                            if (floatingView != null && floatingView?.windowToken != null) {
                                windowManager?.updateViewLayout(floatingView, params)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error updating view layout", e)
                            isWindowActuallyAdded = false
                        }
                    }
                    true
                }

                MotionEvent.ACTION_UP -> {
                    val clickDuration = System.currentTimeMillis() - clickStartTime
                    if (!isMoving && clickDuration < CLICK_TIME_THRESHOLD) {
                        Log.d(TAG, "Floating view clicked!")
                        openApp()
                    }
                    isMoving = false
                    true
                }

                else -> false
            }
        }
    }

    private fun hide() {
        if (Looper.myLooper() != Looper.getMainLooper()) {
            mainThreadHandler.post { // Use mainThreadHandler
                hideFloatingView()
            }
        } else {
            hideFloatingView()
        }
    }

    private fun hideFloatingView()  {
        Log.d(TAG, "Attempting to hide floating view and stop service.")
        try {
            if (floatingView != null && isWindowActuallyAdded) {
                if (floatingView!!.isAttachedToWindow) {
                    Log.d(TAG, "hideInternal: View is attached. Removing.")
                    windowManager?.removeViewImmediate(floatingView) // Try immediate version
                    isWindowActuallyAdded = false
                    Log.d(TAG, "hideInternal: View removed.")
                } else {
                    Log.w(
                        TAG,
                        "hideInternal: isWindowActuallyAdded was true, " +
                                "but view not attached. Correcting state."
                    )
                    removeFloatingView()
                    isWindowActuallyAdded = false // Correct the state
                }
            } else {
                Log.d(TAG, "hideInternal: No view to hide or already hidden.")
                // Ensure state consistency
                if (isWindowActuallyAdded) {
                    Log.w(
                        TAG,
                        "hideInternal: isWindowActuallyAdded was true, " +
                                "but floatingViewInternal is null. Correcting state."
                    )
                    isWindowActuallyAdded = false
                }
            }
            floatingView = null // Always nullify the reference
            params = null
        } catch (e: Exception) {
            Log.e(TAG, "hideInternal: Error hiding floating window", e)
            // Even if removal fails, try to update state to prevent retrying on a problematic view
            isWindowActuallyAdded = false
            floatingView = null
            params = null
        }

        floatingView = null // Crucial to allow it to be re-created
    }

    private fun openApp() {
        Log.d(TAG, "openApp called")
        val launchIntent = packageManager.getLaunchIntentForPackage(packageName)
        launchIntent?.let {
            // it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            try {
                startActivity(it)
                Log.d(TAG, "Activity started")
            } catch (e: Exception) {
                Log.e(TAG, "Could not start activity", e)
            }
        } ?: run {
            Log.e(TAG, "Launch intent for package $packageName is null.")
        }
    }

    private fun removePreviousViewIfExists(reason: String) {
        Log.d(TAG, "Remove view called at ${System.currentTimeMillis()}")
        if (floatingView != null && floatingView!!.isAttachedToWindow) {
            Log.d(
                TAG,
                "removePreviousViewIfExists (Reason: $reason): " +
                        "View is attached. Removing."
            )
            try {
                windowManager?.removeViewImmediate(floatingView) // Try immediate removal
                Log.d(TAG, "removePreviousViewIfExists: Removed view successfully.")
            } catch (e: Exception) {
                Log.e(
                    TAG,
                    "removePreviousViewIfExists: Error removing existing view. " +
                            "This is concerning.",
                    e
                )
                // Even if removeViewImmediate fails, try to nullify to prevent reuse
            }
        } else if (floatingView != null) {
            Log.d(
                TAG,
                "removePreviousViewIfExists (Reason: $reason): View exists " +
                        "but not attached. Nullifying."
            )
        }
        floatingView = null
        isWindowActuallyAdded = false
    }

    private fun removeFloatingView() {
        floatingView?.let {
            try {
                if (it.windowToken != null) { // Check if it's actually added
                    windowManager?.removeView(it)
                    Log.d(TAG, "Floating view removed from WindowManager.")
                } else {
                    if (floatingView != null) {
                        floatingView = null
                    }
                    Log.d(TAG, "Floating view is null or not attached. No action needed.")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error removing floating view", e)
            }
        }
    }
}