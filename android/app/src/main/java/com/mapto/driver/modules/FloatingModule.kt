package com.mapto.driver.modules

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.PixelFormat
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.LifecycleEventListener
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.WritableMap
import com.facebook.react.modules.core.DeviceEventManagerModule
import com.mapto.driver.R
import kotlin.math.abs
import androidx.core.net.toUri

@Suppress("unused")
@SuppressLint("ObsoleteSdkInt")
class FloatingModule(private val reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(reactContext), LifecycleEventListener {

    private var windowManager: WindowManager? = null
    private var floatingView: View? = null
    private lateinit var closeButton: View

    private var isFloatingWindowVisible = false
    private var isMoving: Boolean = false

    private var initialX: Int = 40
    private var initialY: Int = 96

    private var initialTouchX: Float = 40f
    private var initialTouchY: Float = 96f

    private var clickStartTime: Long = 0
    private val CLICK_TIME_THRESHOLD = 200


    override fun getName() = "FloatingWindow"

    init {
        reactContext.addLifecycleEventListener(this)
    }

    @ReactMethod
    fun checkPermission(promise: Promise) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            promise.resolve(Settings.canDrawOverlays(reactContext))
        } else {
            promise.resolve(true)
        }
    }

    @ReactMethod
    fun requestPermission(promise: Promise) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(
                Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                "package:${reactContext.packageName}".toUri()
            ).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            reactContext.startActivity(intent)
            promise.resolve(null)
        } else {
            promise.resolve(true)
        }
    }

    @SuppressLint("InflateParams")
    @ReactMethod
    fun show(promise: Promise) {
        Log.d("skt", "Called show method for floating window module")
        if (!Settings.canDrawOverlays(reactContext)) {
            Log.d("skt", "Floating window module --> No permission")
            promise.reject("PERMISSION_DENIED", "Overlay permission is required")
            return
        }

        try {
            Log.d("skt", "Floating window module --> Has permission")
            windowManager =
                reactContext.getSystemService(ReactApplicationContext.WINDOW_SERVICE) as WindowManager

            // Create floating view if it doesn't exist
            if (floatingView == null) {
                val inflater = LayoutInflater.from(reactContext)
                floatingView = inflater.inflate(R.layout.floating_layout, null)
                Log.d(
                    "skt",
                    "Floating window module --> Inflated floating view - " + (floatingView == null)
                )

                val imageView: ImageView? =
                    floatingView?.findViewById<ImageView>(R.id.img_floating_icon)
                Log.d(
                    "skt",
                    "Floating window module --> Imageview is - " + (imageView == null)
                )
                floatingView?.setOnClickListener {
                    openApp()
                }
                // Set click listener to open app
                imageView?.setOnClickListener {
                    openApp()
                }
            }

            // Window params
            val params = WindowManager.LayoutParams().apply {
                width = WindowManager.LayoutParams.WRAP_CONTENT
                height = WindowManager.LayoutParams.WRAP_CONTENT
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    WindowManager.LayoutParams.TYPE_PHONE
                }
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                format = PixelFormat.TRANSLUCENT
                gravity = Gravity.TOP or Gravity.START
                x = 40
                y = 96
            }


            if (floatingView?.windowToken == null && !isFloatingWindowVisible) {
                setupCloseButton()
                addFloatingIconTouchListener(params)
                Log.d("skt", "Floating window module --> Window token is null. Adding again")
                windowManager?.addView(floatingView, params)
                isFloatingWindowVisible = true
            } else if (floatingView != null && floatingView!!.isAttachedToWindow) {
                Log.d("skt", "Floating window module --> Updating layout.")
                windowManager?.updateViewLayout(
                    floatingView,
                    params
                ) // Example: update if params changed
                isFloatingWindowVisible = true
            } else {
                Log.e("skt", "Floating window module --> floatingView is null, cannot show.")
                promise.reject("ERROR", "Floating view is null.")
                return
            }

            promise.resolve(null)
        } catch (e: Exception) {
            Log.d("skt", "Floating window module --> Error while displaying floating window")
            promise.reject("ERROR", e.message, e)
        }
    }

    @ReactMethod
    fun hide(promise: Promise?) {
        Log.d("skt", "Floating window module --> Trying to hide")
        try {
            if (floatingView != null && floatingView!!.isAttachedToWindow) {
                Log.d("skt", "Floating window module --> All values are not null")
                floatingView?.visibility = View.GONE
                windowManager?.removeView(floatingView)
                Log.d("skt", "Floating window module --> Removed view")

                floatingView = null
                isFloatingWindowVisible = false
            } else {
                Log.d("skt", "Floating window --> View is null or not attached. No action needed")
                // Ensure the flag is also false in this case if the view is gone.
                if (floatingView != null) {
                    floatingView == null
                }
                isFloatingWindowVisible = false
            }
            promise?.resolve(null)
        } catch (e: Exception) {
            promise?.reject("ERROR", e.message, e)
        }
    }

    @ReactMethod
    fun openAppFromBackground(promise: Promise) {
        try {
            if (floatingView != null && windowManager != null) {
                openApp()
            }
            promise.resolve(null)
        } catch (e: Exception) {
            promise.reject("ERROR", e.message)
        }
    }

    override fun onHostResume() {
        hide(object : Promise {
            override fun resolve(data: Any?) {}
            override fun reject(code: String?, message: String?) {}
            override fun reject(p0: String?, p1: Throwable?) {}

            override fun reject(p0: String?, p1: String?, p2: Throwable?) {}

            override fun reject(p0: Throwable?) {}

            override fun reject(p0: Throwable?, p1: WritableMap?) {}

            override fun reject(p0: String?, p1: WritableMap) {}

            override fun reject(
                p0: String?,
                p1: Throwable?,
                p2: WritableMap?
            ) {
            }

            override fun reject(
                p0: String?,
                p1: String?,
                p2: WritableMap
            ) {
            }

            override fun reject(
                p0: String?,
                p1: String?,
                p2: Throwable?,
                p3: WritableMap?
            ) {
            }

            override fun reject(p0: String?) {}
        })
    }

    override fun onHostPause() {}

    override fun onHostDestroy() {
        // Clean up floating window when the app is destroyed

    }

    private fun openApp() {
        val intent = reactContext.packageManager.getLaunchIntentForPackage(reactContext.packageName)
        intent?.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        reactContext.startActivity(intent)
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun addFloatingIconTouchListener(params: WindowManager.LayoutParams) {
        val imageView: ImageView? = floatingView?.findViewById<ImageView>(R.id.img_floating_icon)
        // Set touch listener to open app
        imageView?.setOnTouchListener { view, event ->
            Log.d(
                "skt",
                "Floating window module --> Touch event action - " + (event.action)
            )
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    Log.d("skt", "Floating window module --> Touch event action - Down")
                    initialX = params.x
                    initialY = params.y
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    isMoving = false
                    clickStartTime = System.currentTimeMillis()
//                    showCloseButton()
                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    Log.d("skt", "Floating window module --> Touch event action - Move")
                    val deltaX = event.rawX - initialTouchX
                    val deltaY = event.rawY - initialTouchY

                    if (abs(deltaX) > 5 || abs(deltaY) > 5) {
                        isMoving = true
                    }

                    params.x = (initialX + deltaX).toInt()
                    params.y = (initialY + deltaY).toInt()

                    if (floatingView != null && (floatingView!!.isAttachedToWindow)) {
                        windowManager?.updateViewLayout(floatingView, params)
                    }

                    // Check if icon is over close button
//                    if (isOverCloseButton(event.rawX, event.rawY)) {
//                        closeButton.alpha = 0.7f
//                    } else {
//                        closeButton.alpha = 1.0f
//                    }
                    true
                }
                MotionEvent.ACTION_UP -> {
                    val clickDuration = System.currentTimeMillis() - clickStartTime

                    if (!isMoving && (clickDuration < CLICK_TIME_THRESHOLD)) {
                        imageView.performClick()
                    }
                    Log.d("skt", "Floating window module --> Touch event action - Up")
//                    if (isOverCloseButton(event.rawX, event.rawY)) {
//                        hide(null)
//                    }
//                    hideCloseButton()
                    true
                }
                else -> false
            }
        }
    }

    // Method to handle push notifications
    fun handlePushNotification(notificationData: Map<String, String>) {
        if (isFloatingWindowVisible) {
            // Send event to React Native
            sendEvent("onPushNotification", Arguments.makeNativeMap(notificationData))
        } else {
            // Show the floating window
            show(object : Promise {
                override fun resolve(data: Any?) {
                    // Floating window shown successfully
                }

                override fun reject(code: String?, message: String?) {
                    // Failed to show floating window
                }

                override fun reject(p0: String?, p1: Throwable?) {
                    TODO("Not yet implemented")
                }

                override fun reject(p0: String?, p1: String?, p2: Throwable?) {
                    TODO("Not yet implemented")
                }

                override fun reject(p0: Throwable?) {
                    TODO("Not yet implemented")
                }

                override fun reject(
                    p0: Throwable?,
                    p1: WritableMap?
                ) {
                    TODO("Not yet implemented")
                }

                override fun reject(p0: String?, p1: WritableMap) {
                    TODO("Not yet implemented")
                }

                override fun reject(
                    p0: String?,
                    p1: Throwable?,
                    p2: WritableMap?
                ) {
                    TODO("Not yet implemented")
                }

                override fun reject(
                    p0: String?,
                    p1: String?,
                    p2: WritableMap
                ) {
                    TODO("Not yet implemented")
                }

                override fun reject(
                    p0: String?,
                    p1: String?,
                    p2: Throwable?,
                    p3: WritableMap?
                ) {
                    TODO("Not yet implemented")
                }

                override fun reject(p0: String?) {
                    TODO("Not yet implemented")
                }
            })
        }
    }

    private fun sendEvent(eventName: String, params: WritableMap?) {
        reactContext.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
            .emit(eventName, params)
    }

    @SuppressLint("InlinedApi", "InflateParams")
    private fun setupCloseButton() {
        // Create close button view
        closeButton = LayoutInflater.from(reactContext).inflate(R.layout.close_layout, null)

        // Setup window params for close button
        val closeParams = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        )
        closeParams.gravity = Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL
        closeParams.y = 100 // Margin from bottom

        closeButton.visibility = View.GONE
        windowManager?.addView(closeButton, closeParams)
    }

    private fun showCloseButton() {
        closeButton.visibility = View.VISIBLE
        closeButton.animate().alpha(1f).duration = 200
    }

    private fun hideCloseButton() {
        closeButton.animate().alpha(0f).withEndAction {
            closeButton.visibility = View.GONE
        }.duration = 200
    }

    private fun isOverCloseButton(rawX: Float, rawY: Float): Boolean {
        val closeLocation = IntArray(2)
        closeButton.getLocationOnScreen(closeLocation)

        val closeRect = android.graphics.Rect(
            closeLocation[0],
            closeLocation[1],
            closeLocation[0] + closeButton.width,
            closeLocation[1] + closeButton.height
        )

        return closeRect.contains(rawX.toInt(), rawY.toInt())
    }
}