require 'json'
require 'open3'
project '../ios/engine.xcworkspace'


# Resolve the path to react_native_pods.rb using Node
output, status = Open3.capture2('node', '-p', 'require.resolve("react-native/scripts/react_native_pods.rb", {paths: [process.argv[1]]})', __dir__)
raise "Failed to resolve react_native_pods.rb" unless status.success?

require output.strip

def node_require(script)
   # Resolve script with node to allow for hoisting
   require Pod::Executable.execute_command('node', ['-p',
     "require.resolve(
       '#{script}',
       {paths: [process.argv[1]]},
     )", __dir__]).strip
end

node_require('react-native-permissions/scripts/setup.rb')

platform :ios, '14.0'

prepare_react_native_project!

setup_permissions([
  # 'AppTrackingTransparency',
  'Camera',
  'LocationAccuracy',
  'LocationAlways',
  'LocationWhenInUse',
  'Notifications',
])

linkage = ENV['USE_FRAMEWORKS']
if linkage
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! linkage.to_sym
end

target 'engine' do
  config = use_native_modules!
  
  pod 'RNFBApp', :path => '../node_modules/@react-native-firebase/app'
  pod 'RNFBMessaging', :path => '../node_modules/@react-native-firebase/messaging'
  pod 'react-native-google-maps', :path => '../node_modules/react-native-maps'
  pod 'react-native-maps', :path => '../node_modules/react-native-maps'
  pod 'RNGestureHandler', :path => '../node_modules/react-native-gesture-handler'

  
  # Firebase Pods with modular headers
  pod 'FirebaseCoreInternal', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'Firebase/Core', :modular_headers => true
  pod 'Firebase/Messaging', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
  
  # Google Maps SDK
  pod 'GoogleMaps'
  pod 'GooglePlaces'

  use_react_native!(
    :path => config[:reactNativePath],
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'engineTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    installer.pods_project.targets.each do |target|
      if target.name == 'GooglePlaces'
        target.build_configurations.each do |config|
          config.build_settings['CLANG_ENABLE_MODULES'] = 'YES'
        end
      end
    end

    # This block will fix any issues with specific pods like RNScreens
    installer.pods_project.targets.each do |target|
      if target.name == 'RNScreens'
        target.build_configurations.each do |config|
          config.build_settings['APPLICATION_EXTENSION_API_ONLY'] = 'NO'
        end
      end
    end

    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false
      # :ccache_enabled => true
    )

    # Example: Adjust minimum deployment target
    `sed -i -e $'s/__IPHONE_10_0/__IPHONE_13_0/' Pods/RCT-Folly/folly/portability/Time.h`
  end
end
