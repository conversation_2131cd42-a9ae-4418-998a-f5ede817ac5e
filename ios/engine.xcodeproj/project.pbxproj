// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* engineTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* engineTests.m */; };
		0C03D7E02C256D1F004E1E8F /* EBGaramond-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7C22C256D1E004E1E8F /* EBGaramond-Bold.ttf */; };
		0C03D7E12C256D1F004E1E8F /* Geist-UltraBlack.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7C32C256D1E004E1E8F /* Geist-UltraBlack.woff2 */; };
		0C03D7E22C256D1F004E1E8F /* Geist-UltraLight.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7C42C256D1E004E1E8F /* Geist-UltraLight.woff2 */; };
		0C03D7E32C256D1F004E1E8F /* EBGaramond-ExtraBoldItalic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7C52C256D1E004E1E8F /* EBGaramond-ExtraBoldItalic.woff2 */; };
		0C03D7E42C256D1F004E1E8F /* EBGaramond-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7C62C256D1E004E1E8F /* EBGaramond-ExtraBold.ttf */; };
		0C03D7E52C256D1F004E1E8F /* EBGaramond-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7C72C256D1E004E1E8F /* EBGaramond-SemiBold.ttf */; };
		0C03D7E62C256D1F004E1E8F /* EBGaramond-SemiBoldItalic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7C82C256D1E004E1E8F /* EBGaramond-SemiBoldItalic.woff2 */; };
		0C03D7E72C256D1F004E1E8F /* EBGaramond-Bold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7C92C256D1E004E1E8F /* EBGaramond-Bold.woff2 */; };
		0C03D7E82C256D1F004E1E8F /* EBGaramond-ExtraBold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7CA2C256D1E004E1E8F /* EBGaramond-ExtraBold.woff2 */; };
		0C03D7E92C256D1F004E1E8F /* EBGaramond-MediumItalic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7CB2C256D1E004E1E8F /* EBGaramond-MediumItalic.woff2 */; };
		0C03D7EA2C256D20004E1E8F /* EBGaramond-BoldItalic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7CC2C256D1E004E1E8F /* EBGaramond-BoldItalic.woff2 */; };
		0C03D7EB2C256D20004E1E8F /* EBGaramond-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7CD2C256D1E004E1E8F /* EBGaramond-SemiBoldItalic.ttf */; };
		0C03D7EC2C256D20004E1E8F /* EBGaramond-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7CE2C256D1E004E1E8F /* EBGaramond-Medium.ttf */; };
		0C03D7ED2C256D20004E1E8F /* EBGaramond-Italic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7CF2C256D1F004E1E8F /* EBGaramond-Italic.woff2 */; };
		0C03D7EE2C256D20004E1E8F /* EBGaramond-Regular.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7D02C256D1F004E1E8F /* EBGaramond-Regular.woff2 */; };
		0C03D7EF2C256D20004E1E8F /* Geist-Bold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7D12C256D1F004E1E8F /* Geist-Bold.woff2 */; };
		0C03D7F02C256D20004E1E8F /* Geist-SemiBold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7D22C256D1F004E1E8F /* Geist-SemiBold.woff2 */; };
		0C03D7F12C256D20004E1E8F /* EBGaramond-SemiBold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7D32C256D1F004E1E8F /* EBGaramond-SemiBold.woff2 */; };
		0C03D7F22C256D20004E1E8F /* EBGaramond-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7D42C256D1F004E1E8F /* EBGaramond-MediumItalic.ttf */; };
		0C03D7F32C256D20004E1E8F /* GeistVariableVF.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7D52C256D1F004E1E8F /* GeistVariableVF.woff2 */; };
		0C03D7F42C256D20004E1E8F /* EBGaramond-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7D62C256D1F004E1E8F /* EBGaramond-Regular.ttf */; };
		0C03D7F52C256D20004E1E8F /* Geist-Medium.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7D72C256D1F004E1E8F /* Geist-Medium.woff2 */; };
		0C03D7F62C256D20004E1E8F /* Geist-Regular.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7D82C256D1F004E1E8F /* Geist-Regular.woff2 */; };
		0C03D7F72C256D20004E1E8F /* Geist-Black.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7D92C256D1F004E1E8F /* Geist-Black.woff2 */; };
		0C03D7F82C256D20004E1E8F /* EBGaramond-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7DA2C256D1F004E1E8F /* EBGaramond-BoldItalic.ttf */; };
		0C03D7F92C256D20004E1E8F /* EBGaramond-ExtraBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7DB2C256D1F004E1E8F /* EBGaramond-ExtraBoldItalic.ttf */; };
		0C03D7FA2C256D20004E1E8F /* EBGaramond-Medium.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7DC2C256D1F004E1E8F /* EBGaramond-Medium.woff2 */; };
		0C03D7FB2C256D20004E1E8F /* Geist-Thin.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7DD2C256D1F004E1E8F /* Geist-Thin.woff2 */; };
		0C03D7FC2C256D20004E1E8F /* Geist-Light.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7DE2C256D1F004E1E8F /* Geist-Light.woff2 */; };
		0C03D7FD2C256D20004E1E8F /* EBGaramond-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0C03D7DF2C256D1F004E1E8F /* EBGaramond-Italic.ttf */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		1A377B50FC760CF447068906 /* libPods-engine.a in Frameworks */ = {isa = PBXBuildFile; fileRef = A79B16A7AFCE8356B09EEBB3 /* libPods-engine.a */; };
		477EE32DC57848F6B8F5D265 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = CF6B8A67BB39834DD1123DC1 /* PrivacyInfo.xcprivacy */; };
		5F424A015115F1BEE969232F /* libPods-engine-engineTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 6EFAEBA83BD7739C6EC0AAD5 /* libPods-engine-engineTests.a */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = engine;
		};
		0CBEDE642CA1402B00FFCCC5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0CBEDE5E2CA1402B00FFCCC5 /* RNCNetInfo.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RNCNetInfo;
		};
		0CBEDE662CA1402B00FFCCC5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0CBEDE5E2CA1402B00FFCCC5 /* RNCNetInfo.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = B5027B1B2237B30F00F1AABA;
			remoteInfo = "RNCNetInfo-tvOS";
		};
		0CBEDE682CA1402B00FFCCC5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0CBEDE5E2CA1402B00FFCCC5 /* RNCNetInfo.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3846DAC62403170500B4283E;
			remoteInfo = "RNCNetInfo-macOS";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* engineTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = engineTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* engineTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = engineTests.m; sourceTree = "<group>"; };
		0C03D7C22C256D1E004E1E8F /* EBGaramond-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-Bold.ttf"; path = "../assets/fonts/EBGaramond-Bold.ttf"; sourceTree = "<group>"; };
		0C03D7C32C256D1E004E1E8F /* Geist-UltraBlack.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Geist-UltraBlack.woff2"; path = "../assets/fonts/Geist-UltraBlack.woff2"; sourceTree = "<group>"; };
		0C03D7C42C256D1E004E1E8F /* Geist-UltraLight.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Geist-UltraLight.woff2"; path = "../assets/fonts/Geist-UltraLight.woff2"; sourceTree = "<group>"; };
		0C03D7C52C256D1E004E1E8F /* EBGaramond-ExtraBoldItalic.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-ExtraBoldItalic.woff2"; path = "../assets/fonts/EBGaramond-ExtraBoldItalic.woff2"; sourceTree = "<group>"; };
		0C03D7C62C256D1E004E1E8F /* EBGaramond-ExtraBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-ExtraBold.ttf"; path = "../assets/fonts/EBGaramond-ExtraBold.ttf"; sourceTree = "<group>"; };
		0C03D7C72C256D1E004E1E8F /* EBGaramond-SemiBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-SemiBold.ttf"; path = "../assets/fonts/EBGaramond-SemiBold.ttf"; sourceTree = "<group>"; };
		0C03D7C82C256D1E004E1E8F /* EBGaramond-SemiBoldItalic.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-SemiBoldItalic.woff2"; path = "../assets/fonts/EBGaramond-SemiBoldItalic.woff2"; sourceTree = "<group>"; };
		0C03D7C92C256D1E004E1E8F /* EBGaramond-Bold.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-Bold.woff2"; path = "../assets/fonts/EBGaramond-Bold.woff2"; sourceTree = "<group>"; };
		0C03D7CA2C256D1E004E1E8F /* EBGaramond-ExtraBold.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-ExtraBold.woff2"; path = "../assets/fonts/EBGaramond-ExtraBold.woff2"; sourceTree = "<group>"; };
		0C03D7CB2C256D1E004E1E8F /* EBGaramond-MediumItalic.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-MediumItalic.woff2"; path = "../assets/fonts/EBGaramond-MediumItalic.woff2"; sourceTree = "<group>"; };
		0C03D7CC2C256D1E004E1E8F /* EBGaramond-BoldItalic.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-BoldItalic.woff2"; path = "../assets/fonts/EBGaramond-BoldItalic.woff2"; sourceTree = "<group>"; };
		0C03D7CD2C256D1E004E1E8F /* EBGaramond-SemiBoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-SemiBoldItalic.ttf"; path = "../assets/fonts/EBGaramond-SemiBoldItalic.ttf"; sourceTree = "<group>"; };
		0C03D7CE2C256D1E004E1E8F /* EBGaramond-Medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-Medium.ttf"; path = "../assets/fonts/EBGaramond-Medium.ttf"; sourceTree = "<group>"; };
		0C03D7CF2C256D1F004E1E8F /* EBGaramond-Italic.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-Italic.woff2"; path = "../assets/fonts/EBGaramond-Italic.woff2"; sourceTree = "<group>"; };
		0C03D7D02C256D1F004E1E8F /* EBGaramond-Regular.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-Regular.woff2"; path = "../assets/fonts/EBGaramond-Regular.woff2"; sourceTree = "<group>"; };
		0C03D7D12C256D1F004E1E8F /* Geist-Bold.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Geist-Bold.woff2"; path = "../assets/fonts/Geist-Bold.woff2"; sourceTree = "<group>"; };
		0C03D7D22C256D1F004E1E8F /* Geist-SemiBold.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Geist-SemiBold.woff2"; path = "../assets/fonts/Geist-SemiBold.woff2"; sourceTree = "<group>"; };
		0C03D7D32C256D1F004E1E8F /* EBGaramond-SemiBold.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-SemiBold.woff2"; path = "../assets/fonts/EBGaramond-SemiBold.woff2"; sourceTree = "<group>"; };
		0C03D7D42C256D1F004E1E8F /* EBGaramond-MediumItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-MediumItalic.ttf"; path = "../assets/fonts/EBGaramond-MediumItalic.ttf"; sourceTree = "<group>"; };
		0C03D7D52C256D1F004E1E8F /* GeistVariableVF.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = GeistVariableVF.woff2; path = ../assets/fonts/GeistVariableVF.woff2; sourceTree = "<group>"; };
		0C03D7D62C256D1F004E1E8F /* EBGaramond-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-Regular.ttf"; path = "../assets/fonts/EBGaramond-Regular.ttf"; sourceTree = "<group>"; };
		0C03D7D72C256D1F004E1E8F /* Geist-Medium.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Geist-Medium.woff2"; path = "../assets/fonts/Geist-Medium.woff2"; sourceTree = "<group>"; };
		0C03D7D82C256D1F004E1E8F /* Geist-Regular.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Geist-Regular.woff2"; path = "../assets/fonts/Geist-Regular.woff2"; sourceTree = "<group>"; };
		0C03D7D92C256D1F004E1E8F /* Geist-Black.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Geist-Black.woff2"; path = "../assets/fonts/Geist-Black.woff2"; sourceTree = "<group>"; };
		0C03D7DA2C256D1F004E1E8F /* EBGaramond-BoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-BoldItalic.ttf"; path = "../assets/fonts/EBGaramond-BoldItalic.ttf"; sourceTree = "<group>"; };
		0C03D7DB2C256D1F004E1E8F /* EBGaramond-ExtraBoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-ExtraBoldItalic.ttf"; path = "../assets/fonts/EBGaramond-ExtraBoldItalic.ttf"; sourceTree = "<group>"; };
		0C03D7DC2C256D1F004E1E8F /* EBGaramond-Medium.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-Medium.woff2"; path = "../assets/fonts/EBGaramond-Medium.woff2"; sourceTree = "<group>"; };
		0C03D7DD2C256D1F004E1E8F /* Geist-Thin.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Geist-Thin.woff2"; path = "../assets/fonts/Geist-Thin.woff2"; sourceTree = "<group>"; };
		0C03D7DE2C256D1F004E1E8F /* Geist-Light.woff2 */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Geist-Light.woff2"; path = "../assets/fonts/Geist-Light.woff2"; sourceTree = "<group>"; };
		0C03D7DF2C256D1F004E1E8F /* EBGaramond-Italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "EBGaramond-Italic.ttf"; path = "../assets/fonts/EBGaramond-Italic.ttf"; sourceTree = "<group>"; };
		0CBEDE5E2CA1402B00FFCCC5 /* RNCNetInfo.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RNCNetInfo.xcodeproj; path = "../node_modules/@react-native-community/netinfo/macos/RNCNetInfo.xcodeproj"; sourceTree = "<group>"; };
		0CFB955A2D2FEE0200AE866C /* engine.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = engine.entitlements; path = engine/engine.entitlements; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* engine.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = engine.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = engine/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = engine/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = engine/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = engine/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = engine/main.m; sourceTree = "<group>"; };
		13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = PrivacyInfo.xcprivacy; path = engine/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		3CE7B938E74D2192A50C05B0 /* Pods-engine.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-engine.release.xcconfig"; path = "Target Support Files/Pods-engine/Pods-engine.release.xcconfig"; sourceTree = "<group>"; };
		4A14ED2490435E8A34E8467F /* Pods-engine-engineTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-engine-engineTests.debug.xcconfig"; path = "Target Support Files/Pods-engine-engineTests/Pods-engine-engineTests.debug.xcconfig"; sourceTree = "<group>"; };
		6EFAEBA83BD7739C6EC0AAD5 /* libPods-engine-engineTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-engine-engineTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		71D3C412250F370C8DA02B49 /* Pods-engine.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-engine.debug.xcconfig"; path = "Target Support Files/Pods-engine/Pods-engine.debug.xcconfig"; sourceTree = "<group>"; };
		75FC97146649F48AEE74FCA5 /* Pods-engine-engineTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-engine-engineTests.release.xcconfig"; path = "Target Support Files/Pods-engine-engineTests/Pods-engine-engineTests.release.xcconfig"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = engine/LaunchScreen.storyboard; sourceTree = "<group>"; };
		A79B16A7AFCE8356B09EEBB3 /* libPods-engine.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-engine.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		CF6B8A67BB39834DD1123DC1 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = engine/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				5F424A015115F1BEE969232F /* libPods-engine-engineTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				1A377B50FC760CF447068906 /* libPods-engine.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* engineTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* engineTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = engineTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		0CBEDE5F2CA1402B00FFCCC5 /* Products */ = {
			isa = PBXGroup;
			children = (
				0CBEDE652CA1402B00FFCCC5 /* libRNCNetInfo.a */,
				0CBEDE672CA1402B00FFCCC5 /* libRNCNetInfo-tvOS.a */,
				0CBEDE692CA1402B00FFCCC5 /* libRNCNetInfo-macOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* engine */ = {
			isa = PBXGroup;
			children = (
				0CFB955A2D2FEE0200AE866C /* engine.entitlements */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
				13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */,
				CF6B8A67BB39834DD1123DC1 /* PrivacyInfo.xcprivacy */,
			);
			name = engine;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				A79B16A7AFCE8356B09EEBB3 /* libPods-engine.a */,
				6EFAEBA83BD7739C6EC0AAD5 /* libPods-engine-engineTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
				0CBEDE5E2CA1402B00FFCCC5 /* RNCNetInfo.xcodeproj */,
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				0C03D7C22C256D1E004E1E8F /* EBGaramond-Bold.ttf */,
				0C03D7C92C256D1E004E1E8F /* EBGaramond-Bold.woff2 */,
				0C03D7DA2C256D1F004E1E8F /* EBGaramond-BoldItalic.ttf */,
				0C03D7CC2C256D1E004E1E8F /* EBGaramond-BoldItalic.woff2 */,
				0C03D7C62C256D1E004E1E8F /* EBGaramond-ExtraBold.ttf */,
				0C03D7CA2C256D1E004E1E8F /* EBGaramond-ExtraBold.woff2 */,
				0C03D7DB2C256D1F004E1E8F /* EBGaramond-ExtraBoldItalic.ttf */,
				0C03D7C52C256D1E004E1E8F /* EBGaramond-ExtraBoldItalic.woff2 */,
				0C03D7DF2C256D1F004E1E8F /* EBGaramond-Italic.ttf */,
				0C03D7CF2C256D1F004E1E8F /* EBGaramond-Italic.woff2 */,
				0C03D7CE2C256D1E004E1E8F /* EBGaramond-Medium.ttf */,
				0C03D7DC2C256D1F004E1E8F /* EBGaramond-Medium.woff2 */,
				0C03D7D42C256D1F004E1E8F /* EBGaramond-MediumItalic.ttf */,
				0C03D7CB2C256D1E004E1E8F /* EBGaramond-MediumItalic.woff2 */,
				0C03D7D62C256D1F004E1E8F /* EBGaramond-Regular.ttf */,
				0C03D7D02C256D1F004E1E8F /* EBGaramond-Regular.woff2 */,
				0C03D7C72C256D1E004E1E8F /* EBGaramond-SemiBold.ttf */,
				0C03D7D32C256D1F004E1E8F /* EBGaramond-SemiBold.woff2 */,
				0C03D7CD2C256D1E004E1E8F /* EBGaramond-SemiBoldItalic.ttf */,
				0C03D7C82C256D1E004E1E8F /* EBGaramond-SemiBoldItalic.woff2 */,
				0C03D7D92C256D1F004E1E8F /* Geist-Black.woff2 */,
				0C03D7D12C256D1F004E1E8F /* Geist-Bold.woff2 */,
				0C03D7DE2C256D1F004E1E8F /* Geist-Light.woff2 */,
				0C03D7D72C256D1F004E1E8F /* Geist-Medium.woff2 */,
				0C03D7D82C256D1F004E1E8F /* Geist-Regular.woff2 */,
				0C03D7D22C256D1F004E1E8F /* Geist-SemiBold.woff2 */,
				0C03D7DD2C256D1F004E1E8F /* Geist-Thin.woff2 */,
				0C03D7C32C256D1E004E1E8F /* Geist-UltraBlack.woff2 */,
				0C03D7C42C256D1E004E1E8F /* Geist-UltraLight.woff2 */,
				0C03D7D52C256D1F004E1E8F /* GeistVariableVF.woff2 */,
				13B07FAE1A68108700A75B9A /* engine */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* engineTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* engine.app */,
				00E356EE1AD99517003FC87E /* engineTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				71D3C412250F370C8DA02B49 /* Pods-engine.debug.xcconfig */,
				3CE7B938E74D2192A50C05B0 /* Pods-engine.release.xcconfig */,
				4A14ED2490435E8A34E8467F /* Pods-engine-engineTests.debug.xcconfig */,
				75FC97146649F48AEE74FCA5 /* Pods-engine-engineTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* engineTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "engineTests" */;
			buildPhases = (
				863FC5F2F497951E6E705311 /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				5A7E17790D3DA46C66D090AE /* [CP] Embed Pods Frameworks */,
				EA18CA91CD62296B695497A9 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = engineTests;
			productName = engineTests;
			productReference = 00E356EE1AD99517003FC87E /* engineTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* engine */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "engine" */;
			buildPhases = (
				98D4FCCAC12506B8CB69194C /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				699105709C083D4F9A7E4B22 /* [CP] Embed Pods Frameworks */,
				1CA5CCE52417DA6B4F7D8CD7 /* [CP] Copy Pods Resources */,
				D82397FF96621E126210B0E3 /* [CP-User] [RNFB] Core Configuration */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = engine;
			productName = engine;
			productReference = 13B07F961A680F5B00A75B9A /* engine.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1520;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "engine" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 0CBEDE5F2CA1402B00FFCCC5 /* Products */;
					ProjectRef = 0CBEDE5E2CA1402B00FFCCC5 /* RNCNetInfo.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* engine */,
				00E356ED1AD99517003FC87E /* engineTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		0CBEDE652CA1402B00FFCCC5 /* libRNCNetInfo.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRNCNetInfo.a;
			remoteRef = 0CBEDE642CA1402B00FFCCC5 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		0CBEDE672CA1402B00FFCCC5 /* libRNCNetInfo-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRNCNetInfo-tvOS.a";
			remoteRef = 0CBEDE662CA1402B00FFCCC5 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		0CBEDE692CA1402B00FFCCC5 /* libRNCNetInfo-macOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRNCNetInfo-macOS.a";
			remoteRef = 0CBEDE682CA1402B00FFCCC5 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				0C03D7E02C256D1F004E1E8F /* EBGaramond-Bold.ttf in Resources */,
				0C03D7E12C256D1F004E1E8F /* Geist-UltraBlack.woff2 in Resources */,
				0C03D7E22C256D1F004E1E8F /* Geist-UltraLight.woff2 in Resources */,
				0C03D7E32C256D1F004E1E8F /* EBGaramond-ExtraBoldItalic.woff2 in Resources */,
				0C03D7E42C256D1F004E1E8F /* EBGaramond-ExtraBold.ttf in Resources */,
				0C03D7E52C256D1F004E1E8F /* EBGaramond-SemiBold.ttf in Resources */,
				0C03D7E62C256D1F004E1E8F /* EBGaramond-SemiBoldItalic.woff2 in Resources */,
				0C03D7E72C256D1F004E1E8F /* EBGaramond-Bold.woff2 in Resources */,
				0C03D7E82C256D1F004E1E8F /* EBGaramond-ExtraBold.woff2 in Resources */,
				0C03D7E92C256D1F004E1E8F /* EBGaramond-MediumItalic.woff2 in Resources */,
				0C03D7EA2C256D20004E1E8F /* EBGaramond-BoldItalic.woff2 in Resources */,
				0C03D7EB2C256D20004E1E8F /* EBGaramond-SemiBoldItalic.ttf in Resources */,
				0C03D7EC2C256D20004E1E8F /* EBGaramond-Medium.ttf in Resources */,
				0C03D7ED2C256D20004E1E8F /* EBGaramond-Italic.woff2 in Resources */,
				0C03D7EE2C256D20004E1E8F /* EBGaramond-Regular.woff2 in Resources */,
				0C03D7EF2C256D20004E1E8F /* Geist-Bold.woff2 in Resources */,
				0C03D7F02C256D20004E1E8F /* Geist-SemiBold.woff2 in Resources */,
				0C03D7F12C256D20004E1E8F /* EBGaramond-SemiBold.woff2 in Resources */,
				0C03D7F22C256D20004E1E8F /* EBGaramond-MediumItalic.ttf in Resources */,
				0C03D7F32C256D20004E1E8F /* GeistVariableVF.woff2 in Resources */,
				0C03D7F42C256D20004E1E8F /* EBGaramond-Regular.ttf in Resources */,
				0C03D7F52C256D20004E1E8F /* Geist-Medium.woff2 in Resources */,
				0C03D7F62C256D20004E1E8F /* Geist-Regular.woff2 in Resources */,
				0C03D7F72C256D20004E1E8F /* Geist-Black.woff2 in Resources */,
				0C03D7F82C256D20004E1E8F /* EBGaramond-BoldItalic.ttf in Resources */,
				0C03D7F92C256D20004E1E8F /* EBGaramond-ExtraBoldItalic.ttf in Resources */,
				0C03D7FA2C256D20004E1E8F /* EBGaramond-Medium.woff2 in Resources */,
				0C03D7FB2C256D20004E1E8F /* Geist-Thin.woff2 in Resources */,
				0C03D7FC2C256D20004E1E8F /* Geist-Light.woff2 in Resources */,
				0C03D7FD2C256D20004E1E8F /* EBGaramond-Italic.ttf in Resources */,
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				477EE32DC57848F6B8F5D265 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		1CA5CCE52417DA6B4F7D8CD7 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-engine/Pods-engine-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-engine/Pods-engine-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-engine/Pods-engine-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		5A7E17790D3DA46C66D090AE /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-engine-engineTests/Pods-engine-engineTests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-engine-engineTests/Pods-engine-engineTests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-engine-engineTests/Pods-engine-engineTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		699105709C083D4F9A7E4B22 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-engine/Pods-engine-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-engine/Pods-engine-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-engine/Pods-engine-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		863FC5F2F497951E6E705311 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-engine-engineTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		98D4FCCAC12506B8CB69194C /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-engine-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		D82397FF96621E126210B0E3 /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		EA18CA91CD62296B695497A9 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-engine-engineTests/Pods-engine-engineTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-engine-engineTests/Pods-engine-engineTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-engine-engineTests/Pods-engine-engineTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				00E356F31AD99517003FC87E /* engineTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* engine */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4A14ED2490435E8A34E8467F /* Pods-engine-engineTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = engineTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.laan.engine.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/engine.app/engine";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 75FC97146649F48AEE74FCA5 /* Pods-engine-engineTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = engineTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.laan.engine.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/engine.app/engine";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71D3C412250F370C8DA02B49 /* Pods-engine.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = engine/engine.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7L9SZMBW6K;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = engine/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.laan.engine.$(PRODUCT_NAME:rfc1034identifier)";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = com.mapto.driver;
				PRODUCT_NAME = engine;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3CE7B938E74D2192A50C05B0 /* Pods-engine.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = engine/engine.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7L9SZMBW6K;
				INFOPLIST_FILE = engine/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.laan.engine.$(PRODUCT_NAME:rfc1034identifier)";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = com.mapto.driver;
				PRODUCT_NAME = engine;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "engineTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "engine" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "engine" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
