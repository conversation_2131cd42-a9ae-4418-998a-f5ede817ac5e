<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	</array>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>engine</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSApplicationCategoryType</key>
	<string></string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
		<key>UIAppFonts</key>
		<array>
			<string>EBGaramond-Bold.ttf</string>
			<string>EBGaramond-BoldItalic.ttf</string>
			<string>EBGaramond-ExtraBold.ttf</string>
			<string>EBGaramond-ExtraBoldItalic.ttf</string>
			<string>EBGaramond-Italic.ttf</string>
			<string>EBGaramond-Medium.ttf</string>
			<string>EBGaramond-MediumItalic.ttf</string>
			<string>EBGaramond-Regular.ttf</string>
			<string>EBGaramond-SemiBold.ttf</string>
			<string>EBGaramond-SemiBoldItalic.ttf</string>
		</array>
	</dict>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We need your location to always show your position on the map</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>We need your location for continuous tracking on the map.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We need your location to show your position on the map while you are using the app.</string>
	<key>NSMotionUsageDescription</key>
	<string>This app uses motion data to enhance user experience.</string>
	<key>UIAppFonts</key>
	<array>
		<string>EBGaramond-Regular.ttf</string>
		<string>GeistVariableVF.ttf</string>
		<string>Geist-Regular.ttf</string>
		<string>EBGaramond-Bold.ttf</string>
		<string>Geist-Bold.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>fetch</string>
		<string>location</string>
		<string>processing</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
