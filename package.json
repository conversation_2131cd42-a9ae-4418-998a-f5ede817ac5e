{"name": "mapto-driver", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "android:local": "react-native run-android --mode=localdebug --appId com.mapto.driver.development", "android:dev": "react-native run-android --mode=developmentdebug --appId com.mapto.driver.development", "android:prod": "react-native run-android --mode=productiondebug --appId com.mapto.driver.production", "android:dev-release": "react-native run-android --mode=developmentrelease --appId com.mapto.driver.development", "android:prod-release": "react-native run-android --mode=productionrelease --appId com.mapto.driver.production", "androidProductionRelease": "cd android && ./gradlew clean &&./gradlew assembleProductionRelease", "androidDevelopmentRelease": "cd android && ./gradlew clean && ./gradlew assembleDevelopmentRelease", "androidProductionReleaseBundle": "cd android && ./gradlew clean && ./gradlew bundleProductionRelease "}, "dependencies": {"@babel/plugin-transform-private-methods": "^7.24.7", "@babel/preset-react": "^7.24.7", "@gorhom/bottom-sheet": "^4.6.3", "@notifee/react-native": "^9.1.2", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/netinfo": "^11.4.1", "@react-native-firebase/app": "^20.5.0", "@react-native-firebase/messaging": "^20.5.0", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@react-navigation/stack": "^6.3.29", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.5.1", "@tsconfig/react-native": "^3.0.5", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.5", "axios": "^1.7.2", "axios-retry": "^4.5.0", "babel-core": "^7.0.0-bridge.0", "babel-plugin-inline-import": "^3.0.0", "babel-plugin-styled-components": "^2.1.4", "i18next": "^23.11.5", "lodash": "^4.17.21", "react": "18.2.0", "react-i18next": "^14.1.2", "react-native": "0.74.2", "react-native-background-actions": "^4.0.1", "react-native-background-fetch": "^4.2.5", "react-native-background-timer": "^2.4.1", "react-native-config": "^1.5.3", "react-native-date-picker": "^5.0.7", "react-native-device-info": "^14.0.4", "react-native-document-picker": "^9.3.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.20.0", "react-native-image-picker": "^8.2.0", "react-native-keep-awake": "^4.0.0", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.2.0", "react-native-maps": "^1.15.6", "react-native-otp-entry": "^1.8.3", "react-native-permissions": "^4.1.5", "react-native-push-notification": "^8.1.1", "react-native-qrcode-svg": "^6.3.2", "react-native-reanimated": "^3.15.4", "react-native-safe-area-context": "^4.11.0", "react-native-screens": "^3.34.0", "react-native-sound": "^0.11.2", "react-native-svg": "^15.3.0", "react-native-svg-transformer": "^1.4.0", "react-native-toast-message": "^2.2.0", "react-native-ui-datepicker": "^2.0.4", "snackbar": "^1.1.0", "socket.io-client": "^4.7.5", "styled-components": "^6.1.11", "ts-jest": "^29.1.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.74.84", "@react-native/eslint-config": "^0.74.84", "@react-native/metro-config": "0.74.84", "@react-native/typescript-config": "0.74.84", "@types/react": "^18.3.3", "@types/react-native-background-timer": "^2.0.2", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.7.0", "eslint": "^8.57.0", "jest": "^29.7.0", "metro-react-native-babel-preset": "^0.77.0", "prettier": "^2.8.8", "react-test-renderer": "^18.2.0", "typescript": "^5.0.4"}, "engines": {"node": ">=18"}}