import messaging from '@react-native-firebase/messaging';
import DriverService from './src/services/DriverService';
import {STATUS_CODE} from './src/constants/constants';

export const getFcmToken = async () => {
  try {
    const fcmToken = await messaging().getToken();
    if (fcmToken) {
      console.log('Your Firebase Token is:', fcmToken);
      try {
        const response = await DriverService.updateFcmToken(fcmToken);
        if (response.status === STATUS_CODE.created) {
          console.log('FCM token saved successfully');
        }
      } catch (err: any) {
        const status = err?.response?.status;
        const message = err?.response?.data?.message;
        if (
          [STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)
        ) {
          return;
        }
        if (message) {
          console.log(message);
        }
      }
    } else {
      console.log('Failed to get FCM token');
    }
  } catch (error) {
    console.error('Error getting FCM token:', error);
  }
};
